<PERSON><PERSON><PERSON> yêu cầu cơ bản:
- <PERSON><PERSON><PERSON> tuân thủ rules của [angular.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/angular.mdc) [general.mdc](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/rules/general.mdc)
- Sử dụng MCP server từ [mcp.json](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/.cursor/mcp.json) khi cần thiết để debug lỗi và view trên browser.
- Trước khi tự động chạy, nếu có gì cần làm rõ thì hỏi lại để rõ ràng các ý chính trước khi tự động chạy, không phải sửa đi sửa lại nhiều lần vì không hiểu ý.

Interface sử dụng: [text](../../../shared_contracts/js/dist/requests/sales/create_order.js)
Mock data sử dụng: [text](../../src/app/mock/sales/create_order.ts)

---
Tôi muốn bạn tạo một component Angular với các yêu cầu sau: Xây Dựng Hệ Thống Tạo Đơn Hàng trong Frontend ERP bằng Angular

**Mục tiêu**:  
Hệ thống này là một phần của giao diện người dùng (frontend) trong một ứng dụng ERP, được xây dựng bằng Angular. Nó cho phép người dùng tạo và quản lý đơn hàng với các thông tin chi tiết về sản phẩm, khách hàng, vận chuyển và thanh toán. Mỗi đơn hàng được hiển thị trong một tab riêng biệt, và khi hoàn tất, hệ thống sẽ xây dựng một đối tượng `CreateOrderRequest` đầy đủ. Tôi sẽ chia nhỏ từng vùng và mô tả chúng một cách kỹ lưỡng để Cursor AI có thể hiểu rõ yêu cầu và triển khai chính xác.

---

## **Phần 1: Cấu Trúc Giao Diện Chung**

Hệ thống được chia thành **hai vùng chính** trên giao diện:

### **Region 1: Thanh Tab**
- **Chức năng chính**:  
  Thanh tab nằm ở phía trên cùng của giao diện, hiển thị danh sách các đơn hàng mà người dùng đang tạo. Mỗi tab đại diện cho một đơn hàng riêng biệt. Giới hạn tối đa 10 tab để tránh quá tải giao diện.

- **Chi tiết giao diện**:
  - **Hiển thị tab**: Sử dụng `mat-tab-group` từ Angular Material để tạo một thanh tab ngang. Mỗi tab có nhãn (label) mặc định như "Đơn hàng 1", "Đơn hàng 2", v.v. Nếu đơn hàng có thông tin khách hàng, nhãn sẽ thay đổi thành "[Tên khách hàng] - [Tổng tiền]" (lấy từ `CreateOrderRequest.customer.name`).
  - **Nút thêm tab**: Một nút "+" nằm ở cuối danh sách tab (bên phải thanh tab). Khi nhấn nút này, một tab mới sẽ được thêm vào với trạng thái rỗng (chưa có dữ liệu đơn hàng). Nút này sử dụng biểu tượng `mat-icon` với giá trị `add`. Disable nút nếu đã đạt giới hạn 10 tab.
  - **Đóng tab**: Mỗi tab có một biểu tượng "x" (sử dụng `mat-icon` với giá trị `close`) ở góc phải của nhãn tab. Khi nhấn, tab sẽ bị xóa khỏi danh sách, và dữ liệu đơn hàng trong tab đó sẽ bị hủy (trừ khi đã lưu tạm).
  - **Chuyển đổi tab**: Người dùng có thể nhấp vào bất kỳ tab nào để chuyển sang tab đó. Tab đang active sẽ được làm nổi bật (ví dụ: màu nền hoặc viền khác biệt theo theme của Angular Material).

- **Logic nghiệp vụ**:
  - Mỗi tab chứa một instance riêng của đối tượng `CreateOrderRequest`. Dữ liệu trong tab được lưu trữ tạm thời trong một service (ví dụ: `OrderTabService`) để không bị mất khi chuyển đổi giữa các tab. Ngoài ra, lưu tạm dữ liệu vào `localStorage` để khôi phục khi reload trang.
  - Khi thêm tab mới, một đối tượng `CreateOrderRequest` rỗng được khởi tạo và gắn với tab đó.

- **Yêu cầu kỹ thuật**:
  - Sử dụng signal trong Angular để quản lý danh sách tab và trạng thái active của tab hiện tại.
  - Đảm bảo hiệu suất bằng cách chỉ render nội dung của tab đang active (sử dụng lazy loading hoặc `ngIf`).

---

### **Region 2: Nội Dung của Tab Đang Active**
- **Chức năng chính**:  
  Nội dung của tab đang active hiển thị chi tiết đơn hàng mà người dùng đang thao tác. Vùng này được chia thành **hai cột** để tổ chức thông tin một cách rõ ràng.

- **Cấu trúc giao diện**:
  - **Cột Trái**: Chứa các phần nhập liệu chính liên quan đến đơn hàng, khách hàng và vận chuyển. Cột này chiếm khoảng 60-70% chiều rộng màn hình (có thể điều chỉnh responsive).
  - **Cột Phải**: Hiển thị tóm tắt đơn hàng và các thông tin liên quan đến thanh toán. Cột này chiếm phần còn lại (30-40% chiều rộng).

- **Chi tiết kỹ thuật**:
  - Cả hai cột được bao bọc trong thẻ `<ngx-scrollbar>` để hỗ trợ cuộn dọc khi nội dung vượt quá chiều cao màn hình. Thanh cuộn phải có giao diện mượt mà, không che khuất nội dung (sử dụng cấu hình mặc định của `ngx-scrollbar`).
  - Sử dụng `mat-grid-list` hoặc Flexbox để chia layout thành hai cột, đảm bảo responsive trên các kích thước màn hình khác nhau (ví dụ: trên mobile, có thể chuyển thành một cột duy nhất).

---

## **Phần 2: Chi Tiết Cột Trái**

Cột trái được chia thành **ba phần chính**, mỗi phần sử dụng `mat-expansion-panel` từ Angular Material để hiển thị thông tin. Các panel mặc định mở để người dùng có thể thấy toàn bộ nội dung ngay lập tức.

### **2.1. Thông Tin Đơn Hàng**
- **Mô tả chung**:  
  Phần này cho phép người dùng thêm sản phẩm vào đơn hàng, chỉnh sửa chi tiết sản phẩm và thêm ghi chú hoặc tùy chọn bổ sung.

#### **Row 1: Ô Tìm Kiếm Sản Phẩm**
- **Giao diện**:
  - Một ô nhập liệu (`mat-form-field` với `matInput`) có placeholder như "Tìm kiếm sản phẩm theo tên hoặc mã SKU".
  - Khi người dùng nhấp vào ô này hoặc bắt đầu nhập, một `mat-menu` sẽ xuất hiện ngay bên dưới ô nhập liệu.
- **Chức năng**:
  - `mat-menu` chứa một component con là `ProductSelectionComponent`.
  - `ProductSelectionComponent` nhận hai input:
    - `list: mockProductList` (danh sách sản phẩm mẫu từ mock data).
    - `data: order.items` (danh sách sản phẩm đã chọn trong đơn hàng hiện tại).
  - Component này hiển thị danh sách sản phẩm có thể chọn, với khả năng lọc theo tên hoặc mã SKU khi người dùng nhập. Khi chọn một sản phẩm, nó sẽ được thêm vào danh sách sản phẩm đã chọn (order.items).

#### **Row 2: Danh Sách Sản Phẩm Đã Chọn**
- **Giao diện**:
  - Một `mat-expansion-panel` (mặc định mở) chứa một danh sách các sản phẩm đã chọn.
  - Mỗi sản phẩm hiển thị trong một hàng với các cột sau:
    1. **Biểu tượng Xóa**: Một nút `mat-icon` (`delete`) để xóa sản phẩm khỏi danh sách.
    2. **Thông Tin Sản Phẩm**:
       - **Dòng 1**: Tên sản phẩm (ô nhập liệu inline sử dụng `mat-form-field` với `matInput`, cho phép chỉnh sửa trực tiếp).
       - **Dòng 2**: Mã SKU (hiển thị dạng text, không chỉnh sửa được).
       - **Dòng 3**: Variant (nếu có), hiển thị dạng text có thể nhấp. Khi nhấp, mở `VariantSelectorBottomSheetComponent` (dùng `mat-bottom-sheet`) để chọn variant khác.
       - **Dòng 4**: Đơn vị tính (nếu có), tương tự variant, nhấp để mở `VariantSelectorBottomSheetComponent`.
    3. **Số Lượng**: Một nhóm gồm:
       - Nút "-" (`mat-icon: remove`) để giảm số lượng.
       - Ô nhập liệu (`mat-form-field` với `matInput`) để nhập số lượng trực tiếp.
       - Nút "+" (`mat-icon: add`) để tăng số lượng.
    4. **Đơn Giá**: Ô nhập liệu inline (`mat-form-field` với `matInput`) để chỉnh sửa giá. Nếu có giảm giá, hiển thị row bên dưới với giá giảm (màu đỏ) và số tiền giảm.
    5. **Thành Tiền**: Hiển thị text, tự động tính bằng công thức: `Số Lượng * Đơn Giá`.
    6. **Nút Thêm Tùy Chọn**: Một nút `mat-icon` (`add_circle`) mở `ProductModifiersSheetComponent` (dùng `mat-bottom-sheet`) để thêm tùy chọn bổ sung (modifiers) cho sản phẩm.
    7. **Biểu tượng More**: Một nút `mat-icon` (`more_vert`) mở `mat-menu` với các tùy chọn:
       - "Thêm khuyến mại": Mở `PromotionDialogComponent` (dùng `mat-dialog`) để thêm giảm giá cho sản phẩm.
       - "Thêm ghi chú": Mở `NoteDialogComponent` (dùng `mat-dialog`) để thêm ghi chú cho sản phẩm.
- **Hiển thị Modifier**:
  - Nếu sản phẩm có modifier (tùy chọn bổ sung), các modifier được hiển thị thụt vào dưới sản phẩm cha trong danh sách, với định dạng tương tự (Tên, Số lượng, Đơn giá, Thành tiền).
  - Nếu có ghi chú cho sản phẩm, sinh 1 row hiện ra note.
- **Validation**: 
  - Số lượng không âm (min="1"), đơn giá không âm (min="0"), hiển thị lỗi bằng `mat-error` nếu không hợp lệ.

#### **Row 3: Nút Thêm Sản Phẩm Tùy Chọn và Ghi Chú**
- **Cột Trái**:
  - Nút "Thêm sản phẩm tùy chọn" (`mat-button`) để thêm sản phẩm không có trong danh sách mặc định (ví dụ: phí dịch vụ).
  - Khi nhấn, một hàng mới được thêm vào danh sách sản phẩm với các trường:
    - Tên sản phẩm: Ô nhập liệu trống.
    - Số lượng: Mặc định là 1, có thể chỉnh sửa.
    - Đơn giá: Ô nhập liệu trống.
    - Thành tiền: Tự động tính.
- **Cột Phải**:
  - Nút "Thêm ghi chú toàn đơn" (`mat-button`) mở `NoteDialogComponent` để thêm ghi chú áp dụng cho toàn đơn hàng.

#### **Row 4: Nếu có ghi chú toàn đơn hàng, sinh 1 row để hiện ra ghi chú**

---

### **2.2. Thông Tin Khách Hàng**
- **Mô tả chung**:  
  Phần này thu thập thông tin về khách hàng đặt đơn hàng.

- **Các trường nhập liệu**:
  - **Số Điện Thoại**:
    - Ô nhập liệu (`mat-form-field` với `matInput` và `mat-autocomplete`).
    - Khi nhập ít nhất 3 ký tự, hiển thị danh sách gợi ý từ `mockCustomerAutocompleteList`.
  - **Tên Khách Hàng**:
    - Tương tự số điện thoại, dùng `mat-autocomplete` để gợi ý tên từ danh sách `mockCustomerAutocompleteList`.
  - **Địa Chỉ**:
    - Sử dụng component `InputPlaceComponent` (có thể là một component tùy chỉnh tích hợp Google Maps hoặc dữ liệu địa chỉ nội bộ) để nhập địa chỉ.
  - **Ghi Chú Khách Hàng**:
    - Ô nhập liệu dạng textarea (`mat-form-field` với `textarea`) để nhập ghi chú.

- **Hiển thị bổ sung** (khi chọn khách hàng từ danh sách):
  - **Điểm số**: Hiển thị dạng text (ví dụ: "9/10").
  - **Thống kê**: Một dòng text hoặc bảng nhỏ với các thông tin:
    - Tỷ lệ thành công (ví dụ: "90%").
    - Tỷ lệ hoàn hàng (ví dụ: "5%").
    - Trạng thái khác (ví dụ: "Đã giao 50 đơn").
  - **Tags**: Hiển thị dưới dạng `mat-chip-list` với các nhãn như "Khách thường xuyên", "Lâu không đặt".

---

### **2.3. Thông Tin Vận Chuyển**
- **Mô tả chung**:  
  Phần này quản lý thông tin liên quan đến giao hàng và gói hàng.

- **Thông Tin Người Nhận**:
  - Một `mat-expansion-panel` (mặc định đóng).
  - Ban đầu copy thông tin từ phần "Thông Tin Khách Hàng" (số điện thoại, tên, địa chỉ). Người dùng có thể chỉnh sửa riêng nếu cần.

- **Thông Tin Gói Hàng**:
  - **Khoảng Cách**: Ô nhập liệu số (`mat-form-field` với `matInput`, type="number") để nhập khoảng cách (km).
  - **Trọng Lượng**: Ô nhập liệu số (đơn vị gram).
  - **Kích Thước**: Ba ô nhập liệu số cho chiều dài, chiều rộng, chiều cao (đơn vị cm).
  - **Địa Chỉ Lấy Hàng**: Một `mat-select` để chọn chi nhánh từ danh sách `mockPickupAddressList`
  - **Tự Vận Chuyển**: Một `mat-slide-toggle` để bật/tắt tùy chọn này.
  - **Thời Gian Giao**: Radio button (`mat-radio-group`) với hai tùy chọn: "Giao ngay" hoặc "Giao sau".
  - **Đối Tác Giao Hàng**: Một `mat-select` hiển thị danh sách đối tác (ví dụ: GHN, GHTK) kèm phí giao hàng tương ứng.
  - **Thử Hàng**: Một `mat-select` với các tùy chọn:
      1. Cho xem hàng, không cho thử
			2. Cho phép thử
			3. Không cho xem hàng
			4. Cho xem, không lấy thu ship
  - **Phí Giao Hàng Thu Khách**: Ô nhập liệu số.
  - **Phí Giao Trả Đối Tác**: Ô nhập liệu số.
  - **Đối Tượng Trả Phí Ship**: Radio button với hai tùy chọn: "Khách trả" hoặc "Shop trả".
  - **Hình Thức Giao Hàng**: Radio button với hai tùy chọn: "Bưu tá đến lấy" hoặc "Gửi hàng tại bưu cục".
  - **Thời Gian Lấy Hàng**: Một `mat-select` với các tùy chọn như "Cả ngày", "8h-12h", "13h-17h".
  - **Phí Hoàn Đơn Hàng**: Ô nhập liệu số.
  - **Ghi Chú Vận Chuyển**: Ô nhập liệu textarea.
  - **Tự Động Gửi Đơn**: Một `mat-slide-toggle` (mặc định bật) để bật/tắt tính năng tự động gửi đơn cho đơn vị vận chuyển.

---

## **Phần 3: Chi Tiết Cột Phải**

Cột phải gồm **hai block chính**: Block Thanh Toán và Block Nút Thanh Toán.

### **3.1. Block Thanh Toán**
- **Row 1**:
  - **Bên Trái**:
    - **Nhân viên bán hàng**: Một `mat-select` hiển thị danh sách từ `mockEmployeeList`.
    - **Kênh bán hàng**: Một `mat-select` hiển thị danh sách từ `mockSaleChannelList`.
  - **Bên Phải**:
    - **Ngày giờ tạo đơn**: Ô nhập liệu (`mat-form-field` với `mat-datepicker`) để chọn ngày giờ (`createdAt`), default là giờ hiện tại.

- **Row 2: Các nút**:
  - **Thêm Thẻ**: Nút `mat-button` thêm một row chứa `ChipFormFieldComponent` để nhập thẻ (tag).
  - **Ghi Chú**: Nút `mat-button` mở `NoteDialogComponent`.
  - **Giảm Giá**: Nút `mat-button` mở `PromotionDialogComponent`.

- **Các hàng động**:
  - **Tổng Tiền Hàng**: Text, tính từ tổng thành tiền của các sản phẩm.
  - **Giảm Giá**: Text, hiển thị giá trị giảm giá toàn đơn.
  - **Phí Vận Chuyển**: Text, lấy từ thông tin vận chuyển.
  - **Phụ Thu**: Ô nhập liệu số, default 0.
  - **Khách Cần Trả**: Text, tính bằng: Tổng Tiền Hàng - Giảm Giá + Phí Vận Chuyển + Phụ Thu.
  - **Khách thanh toán**: 
    - **Giao diện**: Một ô nhập liệu số (`mat-form-field` với `matInput`, type="number") để người dùng nhập số tiền khách hàng thanh toán.
    - **Chức năng**: 
      - Khi người dùng nhấp vào ô nhập liệu, ngay lập tức hiển thị một `mat-autocomplete` chứa **8 giá trị nhanh**.
      - Các giá trị nhanh được chia thành **2 hàng**, mỗi hàng **4 giá trị**.
      - **Ví dụ**: Nếu tổng đơn hàng là 1625, các giá trị nhanh sẽ là: 
        - Hàng 1: `1625`, `1630`, `1650`, `1700`.
        - Hàng 2: `2000`, cùng với 3 giá trị khác (có thể dựa trên logic làm tròn hoặc giá trị phổ biến, ví dụ: `2500`, `3000`, `5000`).
      - Khi người dùng nhấp vào một trong các giá trị nhanh, giá trị đó sẽ tự động được điền vào ô nhập liệu (`input` nhận giá trị ngay lập tức).
  - **Phương thức thanh toán**: 
    - **Radio button (1)**:
      - **Tiền mặt**: Một tùy chọn trong nhóm radio button.
      - **Chuyển khoản**: Khi chọn tùy chọn này, hiển thị một block bên dưới với:
        - **Bên trái**: Hiển thị QR code thanh toán, hiện tại chỉ để 1 image test: https://images.squarespace-cdn.com/content/v1/5d3f241fa4e0350001fa20d5/*************-AIZAXV2978MGIDQE0GT7/qr-code.png
        - **Bên phải**: Một danh sách chọn (`mat-select`) hiển thị các tài khoản ngân hàng từ `mockBankList`.
    - **Dấu 3 chấm**: Một biểu tượng (`mat-icon` với giá trị `more_vert`) khi nhấp vào sẽ mở một `mat-menu` với các tùy chọn:
      - **Thẻ tín dụng**: Nếu chọn, hiển thị dấu tick trước tùy chọn này.
      - **Ví điện tử**: Nếu chọn, hiển thị dấu tick trước tùy chọn này.
      - **Thanh toán hỗn hợp**: Nếu chọn, hiển thị dấu tick trước tùy chọn này.
        - Khi nhấp vào, mở dialog `MixedPaymentDialogComponent`.
        - Sau khi người dùng chọn phương thức thanh toán hỗn hợp và nhấn "OK" trong dialog, sinh một hàng mới bên dưới với:
          - **Bên trái**: Hiển thị text "Thanh toán hỗn hợp".
          - **Bên phải**: 
            - Hiển thị chi tiết như "x tiền mặt", "x chuyển khoản" (dựa trên dữ liệu từ dialog).
            - Một nút "pencil" (`mat-icon` với giá trị `edit`) để chỉnh sửa. Khi nhấp vào, mở lại dialog `MixedPaymentDialogComponent` để cập nhật.
  - **Trả Lại Khách**: Text, tính nếu khách thanh toán dư, tính bằng: `CreateOrderRequest.payment.paidAmount - Khách Cần Trả`. Hiển thị "0" nếu âm.
  - **Công Nợ Khách**: Text, hiển thị nếu khách chưa thanh toán đủ, tính bằng: `Khách Cần Trả - CreateOrderRequest.payment.paidAmount`. Hiển thị "0" nếu âm.
  - **Dư Nợ Hiện Tại**: Text, tổng dư nợ của khách từ hệ thống.
  - **Thời Gian Giao Hàng**: Một `mat-select` với tùy chọn "Càng sớm càng tốt" hoặc "Giao hàng sau" (nếu chọn "Giao hàng sau", sinh 1 row ở dưới là datetime field -> set trạng thái order là preorder, thêm vào các field scheduledOrderInfo.).
  - **Trạng Thái**: Một `mat-select` với danh sách trạng thái từ `mockOrderStatuses`.

### **3.2. Block Nút Thanh Toán**
- **Giao diện**:
  - Một dòng text lớn hiển thị "Khách Cần Trả" (tổng số tiền cuối cùng).
  - Nút "Thanh Toán" (`mat-raised-button`) nằm bên dưới.
- **Chức năng**:
  - Nút "Thanh Toán" chỉ enable khi tất cả dữ liệu trong đơn hàng hợp lệ (sử dụng validation từ `OrderService`).

---


## Ánh Xạ Hành Động Giao Diện với `CreateOrderRequest`

### 1. Thông Tin Đơn Hàng
#### Thêm sản phẩm
- **Mô tả**: Người dùng chọn một sản phẩm để thêm vào đơn hàng.
- **Ánh xạ**: Thêm một mục mới vào `CreateOrderRequest.items`.
- **Chi tiết**:
  - `items.product.name`: Tên sản phẩm.
  - `items.quantity`: Số lượng mặc định (thường là 1).
  - `items.price`: Giá mặc định của sản phẩm.
  - `items.product.variant`: Variant của sản phẩm (nếu có).
  - `items.product.unit`: Đơn vị tính của sản phẩm (nếu có).

#### Chỉnh sửa sản phẩm
- **Mô tả**: Người dùng chỉnh sửa sản phẩm trực tiếp trên giao diện.
- **Ánh xạ**: Cập nhật các trường tương ứng, ví dụ `CreateOrderRequest.items.product.name`.
- **Chi tiết**: Giá trị mới được nhập sẽ thay thế tên sản phẩm trong `items.product.name`.

#### Chọn variant
- **Mô tả**: Người dùng chọn một variant (biến thể) của sản phẩm.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.items.product.variant`.
- **Chi tiết**: Variant được chọn từ danh sách sẽ được lưu vào `items.product.variant`.

#### Chọn đơn vị tính
- **Mô tả**: Người dùng chọn đơn vị tính (ví dụ: kg, cái).
- **Ánh xạ**: Cập nhật `CreateOrderRequest.items.product.unit`.
- **Chi tiết**: Đơn vị tính được lưu vào `items.product.unit`.

#### Thay đổi số lượng
- **Mô tả**: Người dùng tăng hoặc giảm số lượng sản phẩm (nhấn "+" hoặc "-").
- **Ánh xạ**: Cập nhật `CreateOrderRequest.items.quantity`.
- **Chi tiết**: Giá trị số lượng mới sẽ được ghi vào `items.quantity`.

#### Chỉnh sửa đơn giá
- **Mô tả**: Người dùng nhập hoặc chỉnh sửa giá của sản phẩm.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.items.price`.
- **Chi tiết**: Giá trị giá mới được cập nhật vào `items.price`.

#### Thêm modifier (tùy chọn bổ sung)
- **Mô tả**: Người dùng thêm các tùy chọn bổ sung cho sản phẩm (ví dụ: topping).
- **Ánh xạ**: Thêm vào `CreateOrderRequest.items.modifierGroups`.
- **Chi tiết**: Mỗi tùy chọn bổ sung được lưu dưới dạng một nhóm trong `items.modifierGroups`.

#### Thêm khuyến mại cho sản phẩm
- **Mô tả**: Người dùng áp dụng giảm giá cho một sản phẩm cụ thể.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.items.discount`, nếu có `promotionName` từ output thì thêm `items.discountInfo: Array<{amount: discountValue, name: promotionName}>`.
- **Chi tiết**: Giảm giá có thể được lưu trực tiếp vào `discount` hoặc chi tiết hơn trong `discountInfo`.

#### Thêm ghi chú cho sản phẩm
- **Mô tả**: Người dùng nhập ghi chú cho sản phẩm.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.items.note` hoặc `items.internalNote`.
- **Chi tiết**: Ghi chú công khai lưu vào `note`, ghi chú nội bộ lưu vào `internalNote`.

---

### 2. Thông Tin Khách Hàng
#### Nhập số điện thoại
- **Mô tả**: Người dùng nhập hoặc chọn số điện thoại của khách hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.customer.phoneNumber`.
- **Chi tiết**: Số điện thoại được lưu vào `customer.phoneNumber`.

#### Nhập tên khách hàng
- **Mô tả**: Người dùng nhập tên khách hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.customer.name`.
- **Chi tiết**: Tên được lưu vào `customer.name`.

#### Nhập địa chỉ
- **Mô tả**: Người dùng nhập địa chỉ của khách hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.customer.address`.
- **Chi tiết**: Địa chỉ được lưu vào `customer.address`.

#### Nhập ghi chú khách hàng
- **Mô tả**: Người dùng thêm ghi chú liên quan đến khách hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.customer.note`.
- **Chi tiết**: Ghi chú được lưu vào `customer.note`.

#### Chọn khách hàng từ danh sách
- **Mô tả**: Người dùng chọn một khách hàng đã có sẵn.
- **Ánh xạ**: Cập nhật toàn bộ `CreateOrderRequest.customer`.
- **Chi tiết**: Các trường như `customerId`, `name`, `phoneNumber`, `address` được điền tự động dựa trên dữ liệu khách hàng đã chọn.

---

### 3. Thông Tin Vận Chuyển
#### Nhập thông tin người nhận
- **Mô tả**: Người dùng nhập thông tin người nhận hàng (có thể khác khách hàng).
- **Ánh xạ**: Cập nhật `CreateOrderRequest.delivery.physicalDelivery.deliveryInfo`, ví dụ `deliveryInfo.name`, `deliveryInfo.phoneNumber`, `deliveryInfo.address`.
- **Chi tiết**: Các trường này lưu thông tin người nhận riêng biệt.

#### Nhập thông tin gói hàng
- **Mô tả**: Người dùng nhập các thông tin liên quan đến gói hàng.
- **Ánh xạ**:
  - `deliveryInfo.weight`: Trọng lượng gói hàng.
  - `deliveryInfo.dimensions`: Kích thước (chiều cao, chiều dài, chiều rộng).
  - `deliveryInfo.pickupAddress`: Địa chỉ lấy hàng.
  - `deliveryInfo.tryOn`: Quy định thử hàng (có/không).
  - `deliveryInfo.deliveryFeeForCustomer`: Phí giao hàng thu của khách.
  - `deliveryInfo.returnFeeForPartner`: Phí giao trả cho đối tác.
  - `deliveryInfo.shippingPayer`: Đối tượng trả phí vận chuyển (khách hàng/đối tác).
  - `deliveryInfo.deliveryMethod`: Hình thức giao hàng.
  - `deliveryInfo.pickupTime`: Thời gian lấy hàng.
  - `deliveryInfo.returnFee`: Phí hoàn đơn hàng.
  - `deliveryInfo.autoSendToCarrier`: Tự động gửi đơn cho đơn vị vận chuyển (true/false).
  - `deliveryInfo.note`: Ghi chú vận chuyển.

#### Chọn đối tác giao hàng
- **Mô tả**: Người dùng chọn đơn vị vận chuyển (ví dụ: GHN, GHTK).
- **Ánh xạ**: Cập nhật `CreateOrderRequest.physicalDelivery.carrier`.
- **Chi tiết**: Tên hoặc ID của đối tác giao hàng được lưu vào `carrier`.

---

### 4. Order Summary và Thanh Toán
#### Chọn nhân viên bán hàng
- **Mô tả**: Người dùng chọn nhân viên phụ trách đơn hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.createdBy`.
- **Chi tiết**: ID của nhân viên được lưu vào `createdBy`.

#### Chọn kênh bán hàng
- **Mô tả**: Người dùng chọn kênh bán hàng (ví dụ: online, tại cửa hàng).
- **Ánh xạ**: Cập nhật `CreateOrderRequest.saleChannel`.
- **Chi tiết**: Kênh bán hàng được lưu vào `saleChannel`.

#### Chọn ngày giờ tạo đơn
- **Mô tả**: Người dùng chọn thời gian tạo đơn hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.createdAt`.
- **Chi tiết**: Thời gian được lưu vào `createdAt`.

#### Thêm thẻ (tags)
- **Mô tả**: Người dùng thêm thẻ để phân loại đơn hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.tags`.
- **Chi tiết**: Danh sách thẻ được lưu vào `tags`.

#### Thêm ghi chú toàn đơn
- **Mô tả**: Người dùng nhập ghi chú cho toàn bộ đơn hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.note` hoặc `CreateOrderRequest.internalNote`.
- **Chi tiết**: Ghi chú công khai lưu vào `note`, nội bộ lưu vào `internalNote`.

#### Thêm giảm giá toàn đơn
- **Mô tả**: Người dùng áp dụng giảm giá cho toàn đơn hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.discount` hoặc `CreateOrderRequest.discountInfo`.
- **Chi tiết**: Giảm giá được lưu vào `discount` hoặc chi tiết hơn trong `discountInfo`.

#### Nhập phụ thu
- **Mô tả**: Người dùng thêm phí phụ thu vào đơn hàng.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.customerFare.surchargeFee`.
- **Chi tiết**: Số tiền phụ thu được lưu vào `customerFare.surchargeFee`.

#### Nhập khách thanh toán
- **Mô tả**: Người dùng nhập số tiền khách hàng đã thanh toán.
- **Ánh xạ**: Cập nhật `CreateOrderRequest.payment.paidAmount`.
- **Chi tiết**: Giá trị thanh toán được lưu vào `paidAmount`.

#### Chọn phương thức thanh toán
- **Mô tả**: Người dùng chọn cách thanh toán (tiền mặt, chuyển khoản, v.v.).
- **Ánh xạ**: Cập nhật `CreateOrderRequest.payment.payments`.
- **Chi tiết**: Thông tin phương thức thanh toán được lưu trong `payments`.

#### Chọn thời gian giao hàng
- **Mô tả**: Người dùng chọn thời gian dự kiến giao hàng. Nếu user lựa chọn Giao hàng sau thì cập nhật `scheduledOrderInfo`
- **Ánh xạ**: Cập nhật `CreateOrderRequest.scheduledOrderInfo`.
- **Chi tiết**: Thời gian giao hàng được lưu vào `scheduledOrderInfo.expectedDeliveryTime`.

#### Chọn trạng thái đơn hàng
- **Mô tả**: Người dùng chọn trạng thái hiện tại của đơn hàng (đang xử lý, đã giao, v.v.).
- **Ánh xạ**: Cập nhật `CreateOrderRequest.status`.
- **Chi tiết**: Trạng thái được lưu vào `status`.

---


## **Phân Chia Component và Service**

### **1. Component Quản Lý Tab**
- **Tên**: `OrderTabsComponent`
- **Chức năng**: 
  - Quản lý danh sách các tab đơn hàng, hiển thị thanh tab và nút thêm tab mới.
  - Mỗi tab chứa một đơn hàng riêng biệt.
- **Giao diện**: 
  - Sử dụng `mat-tab-group` để hiển thị các tab.
  - Nút "+" (`mat-icon: add`) để thêm tab mới.
  - Biểu tượng "x" (`mat-icon: close`) trên mỗi tab để đóng tab.
- **Trách nhiệm**: 
  - Hiển thị danh sách tab và chuyển đổi giữa các tab.
  - Gửi sự kiện khi thêm/xóa tab hoặc chuyển đổi tab.
- **Input**: 
  - `orders: CreateOrderRequest[]` - Danh sách các đơn hàng hiện tại.
- **Output**: 
  - `addTab: EventEmitter<void>` - Phát ra khi người dùng thêm tab mới.
  - `removeTab: EventEmitter<number>` - Phát ra khi đóng tab, kèm theo index của tab.
  - `tabChange: EventEmitter<number>` - Phát ra khi chuyển đổi tab, kèm theo index của tab mới.
- **Service liên quan**: `OrderTabService`
  - Quản lý danh sách các đơn hàng (`CreateOrderRequest[]`) và trạng thái tab active.

---

### **2. Component Nội Dung Tab**
- **Tên**: `OrderTabContentComponent`
- **Chức năng**: 
  - Hiển thị nội dung của tab đang active, chia thành hai cột: cột trái (nhập liệu) và cột phải (tóm tắt).
- **Giao diện**: 
  - Sử dụng Flexbox để chia thành hai cột.
  - Cột trái: Chứa các phần nhập liệu (thông tin đơn hàng, khách hàng, vận chuyển).
  - Cột phải: Chứa phần tóm tắt đơn hàng (order summary).
- **Trách nhiệm**: 
  - Tổ chức layout tổng thể của tab.
  - Chuyển dữ liệu đơn hàng tới các component con.
- **Input**: 
  - `order: CreateOrderRequest` - Dữ liệu đơn hàng của tab hiện tại.
- **Output**: 
  - `orderUpdated: EventEmitter<CreateOrderRequest>` - Phát ra khi dữ liệu đơn hàng thay đổi.

---

### **2.1. Cột Trái: Các Component Nhập Liệu**

#### **2.1.1. Thông Tin Đơn Hàng**
- **Tên**: `OrderDetailsComponent`
- **Chức năng**: 
  - Quản lý việc nhập thông tin chi tiết của đơn hàng (sản phẩm, số lượng, giá, v.v.).
- **Giao diện**: 
  - Một `mat-expansion-panel` (mặc định mở) chứa ba phần con:
    - Ô tìm kiếm sản phẩm.
    - Danh sách sản phẩm đã chọn.
    - Nút thêm sản phẩm tùy chọn và ghi chú.
- **Input**: 
  - `items: OrderItemDetails[]` - Danh sách sản phẩm trong đơn hàng.
- **Output**: 
  - `itemsUpdated: EventEmitter<OrderItemDetails[]>` - Phát ra khi danh sách sản phẩm thay đổi.
- **Các component con**:
  1. **OrderSearchComponent**
     - **Chức năng**: Hiển thị ô tìm kiếm và gọi `ProductSelectionComponent` trong `mat-menu`.
     - **Giao diện**: Ô nhập liệu (`matInput`) với `mat-menu` chứa `ProductSelectionComponent`.
     - **Input**: `productList: ProductList` - Danh sách sản phẩm từ mock data.
     - **Output**: `productSelected: EventEmitter<OrderItemDetails>` - Phát ra khi chọn sản phẩm.
  2. **OrderItemListComponent**
     - **Chức năng**: Hiển thị danh sách sản phẩm đã chọn và hỗ trợ chỉnh sửa.
     - **Giao diện**: Một bảng với các cột: xóa, thông tin sản phẩm, số lượng, đơn giá, thành tiền, tùy chọn, more.
     - **Input**: `items: OrderItemDetails[]` - Danh sách sản phẩm.
     - **Output**: 
       - `itemUpdated: EventEmitter<OrderItemDetails>` - Phát ra khi chỉnh sửa một sản phẩm.
       - `itemRemoved: EventEmitter<number>` - Phát ra khi xóa một sản phẩm (kèm index).
     - **Các component con phụ**:
       - **OrderItemRowComponent**: Hiển thị một hàng sản phẩm với các trường chỉnh sửa inline (tên, số lượng, giá).
       - **OrderModifiersButtonComponent**: Nút mở `ProductModifiersSheetComponent`.
       - **OrderMoreMenuComponent**: Nút "more" mở `mat-menu` với tùy chọn khuyến mại và ghi chú.
  3. **OrderCustomItemComponent**
     - **Chức năng**: Thêm sản phẩm tùy chọn không có trong danh sách mặc định.
     - **Giao diện**: Nút "Thêm sản phẩm tùy chọn" và một hàng nhập liệu khi nhấn nút (tên, số lượng, giá).
     - **Output**: `customItemAdded: EventEmitter<OrderItemDetails>` - Phát ra khi thêm sản phẩm tùy chọn.
- **Service liên quan**: `OrderItemService`
  - Quản lý logic thêm, sửa, xóa sản phẩm; tính toán thành tiền; mở dialog/bottom sheet.

#### **2.1.2. Thông Tin Khách Hàng**
- **Tên**: `CustomerInfoComponent`
- **Chức năng**: 
  - Nhập và hiển thị thông tin khách hàng.
- **Giao diện**: 
  - Một `mat-expansion-panel` (mặc định mở) chứa các trường: số điện thoại, tên, địa chỉ, ghi chú, và thông tin bổ sung (điểm số, tags).
- **Input**: 
  - `customer: OrderCustomer` - Thông tin khách hàng hiện tại.
- **Output**: 
  - `customerUpdated: EventEmitter<OrderCustomer>` - Phát ra khi thông tin khách hàng thay đổi.
- **Các component con**:
  1. **CustomerAutocompleteComponent**
     - **Chức năng**: Ô nhập liệu với autocomplete cho số điện thoại và tên.
     - **Giao diện**: `mat-form-field` với `mat-autocomplete` hiển thị `mockCustomerAutocompleteList`.
     - **Output**: `customerSelected: EventEmitter<OrderCustomer>` - Phát ra khi chọn khách hàng.
  2. **CustomerAddressComponent**
     - **Chức năng**: Nhập địa chỉ khách hàng.
     - **Giao diện**: Sử dụng `InputPlaceComponent`.
     - **Output**: `addressUpdated: EventEmitter<Place>` - Phát ra khi địa chỉ thay đổi.
  3. **CustomerTagsComponent**
     - **Chức năng**: Hiển thị tags của khách hàng.
     - **Giao diện**: `mat-chip-list` hiển thị danh sách tags.
- **Service liên quan**: `CustomerService`
  - Quản lý logic chọn khách hàng, cập nhật thông tin, lấy danh sách gợi ý.

#### **2.1.3. Thông Tin Vận Chuyển**
- **Tên**: `ShippingInfoComponent`
- **Chức năng**: 
  - Nhập thông tin vận chuyển và gói hàng.
- **Giao diện**: 
  - Một `mat-expansion-panel` (mặc định mở) chứa hai phần:
    - Thông tin người nhận.
    - Thông tin gói hàng.
- **Input**: 
  - `deliveryInfo: DeliveryInfo` - Thông tin vận chuyển hiện tại.
- **Output**: 
  - `deliveryInfoUpdated: EventEmitter<DeliveryInfo>` - Phát ra khi thông tin vận chuyển thay đổi.
- **Các component con**:
  1. **ReceiverInfoComponent**
     - **Chức năng**: Nhập thông tin người nhận (copy từ khách hàng, có thể chỉnh sửa).
     - **Giao diện**: Các trường tương tự `CustomerInfoComponent`.
     - **Input**: `customer: OrderCustomer` - Dữ liệu khách hàng để copy.
     - **Output**: `receiverUpdated: EventEmitter<{name: string, phoneNumber: string, address: Place}>`.
  2. **PackageInfoComponent**
     - **Chức năng**: Nhập thông tin gói hàng (trọng lượng, kích thước, v.v.).
     - **Giao diện**: Các ô nhập liệu và `mat-select` cho các tùy chọn (đối tác giao hàng, thử hàng, v.v.).
     - **Output**: `packageInfoUpdated: EventEmitter<DeliveryInfo>` - Phát ra khi thông tin gói hàng thay đổi.
- **Service liên quan**: `ShippingService`
  - Quản lý logic tính phí vận chuyển, cập nhật thông tin gói hàng, chọn đối tác giao hàng.

---

### **2.2. Cột Phải: Order Summary**
- **Tên**: `OrderSummaryComponent`
- **Chức năng**: 
  - Hiển thị tóm tắt đơn hàng và quản lý thanh toán.
- **Giao diện**: 
  - Một block thanh toán với các hàng động (tổng tiền, giảm giá, v.v.).
  - Một block nút thanh toán.
- **Input**: 
  - `order: CreateOrderRequest` - Dữ liệu đơn hàng để hiển thị tóm tắt.
- **Output**: 
  - `paymentUpdated: EventEmitter<OrderPayment>` - Phát ra khi thông tin thanh toán thay đổi.
  - `submitOrder: EventEmitter<CreateOrderRequest>` - Phát ra khi nhấn nút thanh toán.
- **Các component con**:
  1. **PaymentDetailsComponent**
     - **Chức năng**: Hiển thị và nhập thông tin thanh toán (nhân viên, kênh bán hàng, phương thức thanh toán).
     - **Giao diện**: Các `mat-select`, ô nhập liệu, và radio button cho phương thức thanh toán.
     - **Output**: 
       - `paymentMethodUpdated: EventEmitter<Payment>` - Phát ra khi chọn phương thức thanh toán.
       - `mixedPaymentUpdated: EventEmitter<Payment[]>` - Phát ra từ `MixedPaymentDialogComponent`.
  2. **SummaryRowsComponent**
     - **Chức năng**: Hiển thị các hàng tóm tắt (tổng tiền, giảm giá, khách cần trả, v.v.).
     - **Giao diện**: Danh sách các hàng text hoặc ô nhập liệu.
  3. **PaymentButtonComponent**
     - **Chức năng**: Hiển thị nút thanh toán và tổng số tiền.
     - **Giao diện**: Một dòng text lớn và nút `mat-raised-button`.
     - **Input**: `isValid: boolean` - Kiểm tra đơn hàng có hợp lệ để enable nút không.
     - **Output**: `submitClicked: EventEmitter<void>` - Phát ra khi nhấn nút.
- **Service liên quan**: `OrderSummaryService`
  - Quản lý logic tính toán tổng tiền, giảm giá, phí vận chuyển, kiểm tra validation.

---

## **Các Service Chính**
1. **OrderTabService**
   - Quản lý danh sách tab và trạng thái active.
   - Phương thức: `addTab()`, `removeTab(index)`, `getActiveTab()`, `setActiveTab(index)`.

2. **OrderItemService**
   - Quản lý danh sách sản phẩm: thêm, sửa, xóa, tính thành tiền.
   - Phương thức: `addItem()`, `updateItem()`, `removeItem()`, `calculateItemTotal()`.

3. **CustomerService**
   - Quản lý thông tin khách hàng: chọn từ danh sách, cập nhật thông tin.
   - Phương thức: `getCustomerSuggestions()`, `updateCustomer()`.

4. **ShippingService**
   - Quản lý thông tin vận chuyển: tính phí, cập nhật gói hàng.
   - Phương thức: `calculateShippingFee()`, `updateDeliveryInfo()`.

5. **OrderSummaryService**
   - Quản lý tóm tắt đơn hàng: tính tổng tiền, kiểm tra validation.
   - Phương thức: `calculateSummary()`, `validateOrder()`, `buildCreateOrderRequest()`.

---

## **Cơ Chế Hoạt Động**
- **Component chỉ hiển thị UI**: Mọi thay đổi từ người dùng (như chọn sản phẩm, nhập thông tin) sẽ phát ra sự kiện (`@Output`) tới component cha hoặc gọi service tương ứng.
- **Service xử lý logic**: Các service nhận dữ liệu từ component, xử lý nghiệp vụ (tính toán, validation), và trả về kết quả hoặc cập nhật trạng thái.
- **Xây dựng `CreateOrderRequest`**: `OrderSummaryService` sẽ tổng hợp dữ liệu từ các service khác để tạo đối tượng hoàn chỉnh khi cần gửi đi.
