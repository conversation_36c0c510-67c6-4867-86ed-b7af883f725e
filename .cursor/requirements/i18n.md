Bạn là Cursor AI, hỗ trợ rà soát và quản lý key dịch trong dự án ERP Angular 19 với `ngx-translate`. Dự án sử dụng cấu trúc i18n dựa trên:
- **Shared** (`public/assets/i18n/shared/<lang>.json`): Key chung (ví dụ: `COMMON.SAVE`).
- **First path** (`public/assets/i18n/<module>/<lang>.json`): Key cho giao diện chung (`panel`, `dialog`, `mat-bottom-sheet`) của module (thuộc `NAVIGATION_BLOCK`: `cashier`, `warehouse`, `orders`, ...).
- **Full path** (`public/assets/i18n/<module>/<sub-path>/<lang>.json`): Key cho nội dung chính của trang theo router (ví dụ: `cashier/order`, `warehouse/goods-receipt`).

Nhiệm vụ của bạn là rà soát toàn bộ dự án, tr<PERSON>ch xuất key dịch, sửa lỗi thiếu/trùng key, và đặt key vào đúng file JSON. Tất cả key phải ở định dạng **SCREAMING_SNAKE_CASE** (ví dụ: `CASHIER_ORDER_TITLE`).

**Hướng dẫn chi tiết:**

QUAN TRỌNG: ĐỌC KỸ HƯỚNG DẪN TỪ docs/ngx-translate-integration.md


### Bước 1: Trích xuất key bằng `ngx-translate-extract`
1. Chạy lệnh:
   ```bash
   npm run extract-i18n
   ```
   - Lệnh này trích xuất key từ template (`*.html`) và TypeScript (`*.ts`) trong `src/`, lưu vào `public/assets/i18n/`.
   - File cấu hình `extract.config.json`:
     ```json
     {
       "input": ["./src"],
       "output": "./public/assets/i18n",
       "format": "json",
       "sort": true,
       "clean": true,
       "patterns": ["**/*.html", "**/*.ts"],
       "marker": "translate"
     }
     ```

2. Kiểm tra file JSON được tạo (ví dụ: `public/assets/i18n/en.json`, `vi.json`).
3. Di chuyển key vào file đúng:
   - Key bắt đầu bằng `COMMON` (như `COMMON.SAVE`) → `shared/<lang>.json`.
   - Key giao diện chung (như `CASHIER.PANEL.TITLE`) → `<module>/<lang>.json`.
   - Key trang chính (như `CASHIER.ORDER.TITLE`) → `<module>/<sub-path>/<lang>.json`.

### Bước 2: Rà soát template và TypeScript
1. **Tìm key hiện có:**
   - Quét `*.html` để tìm key trong pipe `translate` hoặc directive `translate`:
     - Ví dụ: `{{ 'CASHIER.ORDER.TITLE' | translate }}`, `[translate]="'CASHIER.PANEL.TITLE'"`.
   - Quét `*.ts` để tìm key trong `TranslateService.get()`:
     - Ví dụ: `translate.get('CASHIER.ORDER.TITLE')`.

2. **Tìm chuỗi hard-coded (chưa dịch):**
   - Tìm các chuỗi văn bản trong thẻ HTML hoặc TypeScript không sử dụng `translate`.
     - Ví dụ: `<h1>Create Order</h1>` hoặc `console.log('Confirm Order')`.
   - Đề xuất key mới:
     - Nếu trong `<mat-panel>`, `<mat-dialog>`, `<mat-bottom-sheet>` → tạo key như `CASHIER.PANEL.TITLE` trong `<module>/<lang>.json`.
     - Nếu trong tiêu đề trang (`<h1>`, `<mat-card-title>`) → tạo key như `CASHIER.ORDER.TITLE` trong `<module>/<sub-path>/<lang>.json`.
     - Nếu là nút chung (`Save`, `Cancel`) → tạo key như `COMMON.SAVE` trong `shared/<lang>.json`.

3. **Xác định module và sub-path:**
   - Dựa trên đường dẫn file hoặc router:
     - File trong `src/app/features/cashier/order/` → module `cashier`, sub-path `order`.
     - URL `/cashier/order` → module `cashier`, sub-path `order`.
   - Kiểm tra `NAVIGATION_BLOCK` (`salehub_shared_contracts`) để xác định module hợp lệ.

### Bước 3: Sửa lỗi thiếu key
1. So sánh key trong code với file JSON:
   - Nếu key (như `CASHIER.ORDER.TITLE`) có trong template nhưng thiếu trong `cashier/order/<lang>.json`:
     - Thêm vào `cashier/order/en.json` và `vi.json`.
     - Ví dụ:
       ```json
       {
         "CASHIER": {
           "ORDER": {
             "TITLE": "Create Order"
           }
         }
       }
       ```
       ```json
       {
         "CASHIER": {
           "ORDER": {
             "TITLE": "Tạo Đơn Hàng"
           }
         }
       }
       ```

2. Tạo folder/file nếu chưa tồn tại:
   - Ví dụ: Nếu thiếu `cashier/order/`, tạo folder và file `en.json`, `vi.json`.

### Bước 4: Sửa lỗi trùng key
1. Kiểm tra trùng key trong cùng file JSON hoặc giữa các file:
   - Ví dụ: `CASHIER.PANEL.TITLE` xuất hiện trong cả `cashier/en.json` và `shared/en.json`.
2. Quyết định vị trí đúng:
   - Key giao diện chung (`panel`, `dialog`) → giữ trong `<module>/<lang>.json`.
   - Key trang chính → giữ trong `<module>/<sub-path>/<lang>.json`.
   - Key chung → giữ trong `shared/<lang>.json`.
3. Xóa key trùng ở vị trí sai:
   - Ví dụ: Xóa `CASHIER.PANEL.TITLE` khỏi `shared/en.json`, giữ trong `cashier/en.json`.

### Bước 5: Chuẩn hóa key
1. Đảm bảo key ở **SCREAMING_SNAKE_CASE**:
   - Nếu phát hiện key sai (như `cashier_order_title` hoặc `CashierOrderTitle`), sửa thành `CASHIER_ORDER_TITLE`.
   - Cập nhật đồng thời trong template/TypeScript và file JSON.
2. Đồng bộ `en.json` và `vi.json`:
   - Nếu key có trong `en.json` nhưng thiếu trong `vi.json`, thêm bản dịch tiếng Việt.

### Bước 6: Hỗ trợ ngành hàng và đa kênh
1. Ngành hàng (thời trang, thực phẩm):
   - Key như `PRODUCTS.FASHION.SIZE`:
     - Giao diện chung → `products/<lang>.json`.
     - Trang chính → `products/fashion/<lang>.json`.
2. Tích hợp đa kênh (Shopee, Lazada):
   - Key như `ORDERS.ECOMMERCE.SYNC_SHOPEE` → `orders/ecommerce/<lang>.json`.

### Bước 7: Kiểm tra và debug
1. Chạy ứng dụng (`ng serve`) và kiểm tra UI:
   - Truy cập các URL (`/cashier/order`, `/warehouse/goods-receipt`, `/orders/ecommerce`).
   - Xác nhận bản dịch hiển thị đúng (key từ `shared`, **first path**, **full path**).
2. Kiểm tra console:
   - Nếu thấy `console.warn('Full path translation file not found: ...')`, tạo folder/file JSON thiếu.
3. Chạy lại `npm run extract-i18n` để xác nhận tất cả key đã được trích xuất.

**Nhiệm vụ cụ thể:**
- Quét toàn bộ `src/` để tìm key dịch và chuỗi hard-coded.
- Đề xuất key mới cho chuỗi hard-coded, đặt vào file JSON đúng (**first path**, **full path**, **shared**).
- Tạo folder/file JSON nếu thiếu (như `cashier/order/<lang>.json`).
- Sửa key thiếu/trùng, chuẩn hóa **SCREAMING_SNAKE_CASE**.
- Đồng bộ `en.json` và `vi.json` cho tất cả key.
- Chạy `npm run extract-i18n` để trích xuất và tổ chức key.
```

---

### 2. Quy trình thực hiện

Dưới đây là các bước chi tiết để Cursor AI thực hiện rà soát và đặt key đúng:

1. **Chuẩn bị môi trường:**
   - Kiểm tra `extract.config.json` và script `extract-i18n` trong `package.json`:
     ```json
     "scripts": {
       "extract-i18n": "ngx-translate-extract --config extract.config.json"
     }
     ```

2. **Chạy `ngx-translate-extract`:**
   - Chạy lệnh:
     ```bash
     npm run extract-i18n
     ```
   - Kiểm tra file JSON tạm thời được tạo trong `public/assets/i18n/` (như `en.json`, `vi.json`).

3. **Rà soát code:**
   - **Template (`*.html`)**:
     - Tìm key trong `{{ '...' | translate }}` hoặc `[translate]="'...'"`.
     - Tìm chuỗi hard-coded:
       - Trong `<mat-panel>`, `<mat-dialog>`, `<mat-bottom-sheet>` → đề xuất key cho **first path** (`<module>/<lang>.json`).
       - Trong `<h1>`, `<mat-card-title>` → đề xuất key cho **full path** (`<module>/<sub-path>/<lang>.json`).
       - Trong nút (`<button>Save</button>`) → đề xuất key cho `shared/<lang>.json`.
   - **TypeScript (`*.ts`)**:
     - Tìm key trong `translate.get('...')` hoặc `translate.instant('...')`.
     - Tìm chuỗi hard-coded trong `console.log`, `alert`, hoặc biến văn bản.

4. **Tổ chức key:**
   - Dựa trên đường dẫn file hoặc router:
     - File trong `src/app/features/cashier/order/` → key trang chính vào `cashier/order/<lang>.json`.
     - File trong `src/app/shared/components/panel/` → key giao diện chung vào `<module>/<lang>.json`.
   - Ví dụ:
     - `<h1>Create Order</h1>` trong `cashier-order.component.html` → tạo `CASHIER.ORDER.TITLE` trong `cashier/order/<lang>.json`.
     - `<mat-panel-title>Panel</mat-panel-title>` → tạo `CASHIER.PANEL.TITLE` trong `cashier/<lang>.json`.

5. **Sửa lỗi thiếu key:**
   - Nếu key trong template/TypeScript không có trong file JSON:
     - Thêm vào file đúng (**first path**, **full path**, hoặc `shared`).
     - Tạo folder/file nếu thiếu (ví dụ: `cashier/order/`).

6. **Sửa lỗi trùng key:**
   - Kiểm tra file JSON bằng cách tìm key trùng (như `CASHIER.ORDER.TITLE` trong cả `cashier/en.json` và `cashier/order/en.json`).
   - Giữ key ở vị trí đúng và xóa ở vị trí sai.

7. **Chuẩn hóa key:**
   - Sửa key sai định dạng (như `cashier_order_title`) thành `CASHIER_ORDER_TITLE`.
   - Đảm bảo đồng bộ `en.json` và `vi.json`.

8. **Kiểm tra lại:**
   - Chạy `ng serve` và truy cập các URL (`/cashier/order`, `/warehouse/goods-receipt`).
   - Kiểm tra console để phát hiện file JSON thiếu.
   - Chạy lại `npm run extract-i18n` để xác nhận.

---

### 3. Ví dụ cụ thể

#### Template ban đầu (`cashier-order.component.html`):
```html
<h1>Create Order</h1>
<mat-panel>
  <mat-panel-title>Cashier Panel</mat-panel-title>
</mat-panel>
<button mat-button>Cancel</button>
```

#### Sau khi Cursor AI rà soát:
1. **Chuỗi hard-coded được thay bằng key:**
   ```html
   <h1>{{ 'CASHIER.ORDER.TITLE' | translate }}</h1>
   <mat-panel>
     <mat-panel-title>{{ 'CASHIER.PANEL.TITLE' | translate }}</mat-panel-title>
   </mat-panel>
   <button mat-button>{{ 'COMMON.CANCEL' | translate }}</button>
   ```

2. **File JSON được cập nhật:**
   - `cashier/order/en.json` (full path):
     ```json
     {
       "CASHIER": {
         "ORDER": {
           "TITLE": "Create Order"
         }
       }
     }
     ```
     ```json
     {
       "CASHIER": {
         "ORDER": {
           "TITLE": "Tạo Đơn Hàng"
         }
       }
     }
     ```
   - `cashier/en.json` (first path):
     ```json
     {
       "CASHIER": {
         "PANEL": {
           "TITLE": "Cashier Panel"
         }
       }
     }
     ```
     ```json
     {
       "CASHIER": {
         "PANEL": {
           "TITLE": "Bảng Điều Khiển Thu Ngân"
         }
       }
     }
     ```
   - `shared/en.json`:
     ```json
     {
       "COMMON": {
         "CANCEL": "Cancel"
       }
     }
     ```
     ```json
     {
       "COMMON": {
         "CANCEL": "Hủy"
       }
     }
     ```

#### TypeScript ban đầu (`cashier-order.component.ts`):
```typescript
console.log('Order created successfully');
```

#### Sau khi Cursor AI rà soát:
```typescript
this.translate.get('CASHIER.ORDER.SUCCESS').subscribe((msg) => {
  console.log(msg);
});
```
- Thêm vào `cashier/order/en.json`:
  ```json
  {
    "CASHIER": {
      "ORDER": {
        "SUCCESS": "Order created successfully"
      }
    }
  }
  ```

---

### 4. Hướng dẫn kiểm tra

1. **Kiểm tra file i18n:**
   - Mở `public/assets/i18n/` và xác nhận:
     - `cashier/`, `warehouse/` có key giao diện chung (`CASHIER.PANEL.TITLE`).
     - `cashier/order/`, `warehouse/goods-receipt/` có key trang chính (`CASHIER.ORDER.TITLE`).
     - `shared/` có key chung (`COMMON.SAVE`).

2. **Kiểm tra code:**
   - Quét `*.html` và `*.ts` để đảm bảo không còn chuỗi hard-coded.
   - Xác nhận key dùng **SCREAMING_SNAKE_CASE**.

3. **Kiểm tra UI:**
   - Chạy `ng serve` và truy cập `/cashier/order`, `/orders/ecommerce`.
   - Xác nhận bản dịch hiển thị đúng và đồng bộ giữa `en` và `vi`.

4. **Kiểm tra console:**
   - Nếu thấy `console.warn('Full path translation file not found: ...')`, kiểm tra folder/file JSON thiếu.

5. **Kiểm tra `ngx-translate-extract`:**
   - Chạy `npm run extract-i18n` và xác nhận key được tổ chức đúng.

---

### 5. Hỗ trợ ERP và ngành hàng

- **Module:** Hỗ trợ `cashier`, `warehouse`, `orders`, với key giao diện chung trong `<module>/<lang>.json` và key trang chính trong `<module>/<sub-path>/<lang>.json`.
- **Ngành hàng:**
  - Thời trang: `PRODUCTS.FASHION.SIZE` trong `products/fashion/<lang>.json`.
  - Thực phẩm: `PRODUCTS.FOOD.EXPIRY_DATE` trong `products/food/<lang>.json`.
- **Đa kênh:** `ORDERS.ECOMMERCE.SYNC_SHOPEE` trong `orders/ecommerce/<lang>.json`.

**Ví dụ cho `/orders/ecommerce`:**
- `orders/en.json` (first path):
  ```json
  {
    "ORDERS": {
      "PANEL": {
        "TITLE": "Orders Panel"
      }
    }
  }
  ```
- `orders/ecommerce/en.json` (full path):
  ```json
  {
    "ORDERS": {
      "ECOMMERCE": {
        "TITLE": "E-commerce Orders",
        "SYNC_SHOPEE": "Sync with Shopee"
      }
    }
  }
  ```
