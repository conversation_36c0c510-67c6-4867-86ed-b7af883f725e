Tôi cần bạn viết mã Angular cho component `ProductFilterDialogComponent` để lọc danh sách sản phẩm trong hệ thống kiểm kho, dựa trên yêu cầu chi tiết dưới đây. <PERSON>ui lòng sử dụng các tài liệu tham khảo đã cung cấp (các file TypeScript và mock data) để đảm bảo tính nhất quán với cấu trúc dữ liệu và codebase hiện tại. Mã phải được viết rõ ràng, tuân thủ Angular best practices, sử dụng `ChangeDetectionStrategy.OnPush`, và tích hợp với các shared components và mock data. Dưới đây là yêu cầu chi tiết và hướng dẫn từng bước:

### Yêu cầu chung
1. **Tên component**: `ProductFilterDialogComponent`
2. **Đường dẫn**: `src/app/features/warehouse/dialogs/product-filter-dialog/product-filter-dialog.component.ts`
3. **Standalone**: Component phải là standalone, import các module cần thiết.
4. **Change Detection**: Sử dụng `ChangeDetectionStrategy.OnPush`.
5. **Ngôn ngữ**: Sử dụng tiếng Việt cho các comment và tên biến (nếu phù hợp), nhưng đảm bảo các tên interface, type, và service theo chuẩn tiếng Anh từ các tài liệu tham khảo.



### Yêu cầu chi tiết
1. **Tên**: `ProductFilterDialogComponent`
2. **Đường dẫn**: `src/app/features/warehouse/dialogs/product-filter-dialog/product-filter-dialog.component.ts`
3. **Standalone**: Có.
4. **Inputs**:
   - `current: { category: string[], warehouseLocation: EmbeddedWarehouseLocation }`: Trạng thái bộ lọc hiện tại, chứa danh sách ID nhóm hàng đã chọn và ID vị trí kho đã chọn.
   - `warehouseId: string`: ID của kho đã chọn để lọc danh sách vị trí kho trong `mockWarehouseLocations`.
5. **Outputs**:
   - `filteredProducts: EventEmitter<ProductListItem[]>`: Phát ra danh sách sản phẩm đã lọc từ `mockProductList`.
6. **Giao diện**:
   - **Danh sách nhóm hàng**:
     - Sử dụng `MatCheckbox` để hiển thị và cho phép chọn nhiều nhóm hàng từ `mockCategoryList`.
     - Khởi tạo các checkbox với trạng thái checked nếu ID nhóm hàng có trong `current.category`.
   - **Vị trí kho**:
     - Sử dụng `MatSelect` để hiển thị danh sách vị trí kho từ `mockWarehouseLocations`, chỉ hiển thị các vị trí có `warehouseId` khớp với input `warehouseId`.
     - Hiển thị vị trí kho đã chọn từ `current.warehouseLocation` (nếu có).
     - Cho phép chọn một vị trí kho duy nhất hoặc không chọn (giá trị `null`).
   - **Tùy chọn**:
     - `MatCheckbox`: "Chỉ kiểm hàng còn tồn kho" (lọc sản phẩm có `warehouseStock[warehouseId] > 0`).
     - `MatCheckbox`: "Chỉ kiểm hàng đang kinh doanh" (lọc sản phẩm có `status: 'active'`).
   - **Buttons**:
     - **Hủy**: Đóng dialog mà không phát ra sự kiện (`MatDialogRef.close()`).
     - **Xác nhận**: Phát ra danh sách sản phẩm đã lọc qua `filteredProducts` và đóng dialog.
7. **Logic**:
   - **Khởi tạo**:
     - Khởi tạo trạng thái bộ lọc với giá trị từ `current`:
       - `selectedCategories: string[] = current.category || []`.
       - `selectedWarehouseLocation: EmbeddedWarehouseLocation | null = current.warehouseLocation || null`.
       - `onlyInStock: boolean = false`.
       - `onlyActive: boolean = false`.
     - Lọc `mockWarehouseLocations` để chỉ hiển thị các vị trí có `warehouseId` khớp với input `warehouseId`.
   - **Lọc sản phẩm**:
     - Lấy danh sách sản phẩm từ `mockProductList`.
     - Áp dụng các bộ lọc:
       - **Nhóm hàng**: Nếu `selectedCategories` không rỗng, chỉ lấy sản phẩm có ít nhất một `categoryIds` nằm trong `selectedCategories`.
       - **Vị trí kho**: Nếu `selectedWarehouseLocation` được chọn, chỉ lấy sản phẩm có `locations._id` chứa `EmbeddedWarehouseLocation._id` khớp với `selectedWarehouseLocation` (giả định `Product.locations` chứa danh sách `WarehouseLocation` từ `mockWarehouseLocations`).
       - **Chỉ kiểm hàng còn tồn kho**: Nếu `onlyInStock` là `true`, chỉ lấy sản phẩm có `warehouseStock[warehouseId] > 0`.
       - **Chỉ kiểm hàng đang kinh doanh**: Nếu `onlyActive` là `true`, chỉ lấy sản phẩm có `status: 'active'`.
     - Trả về danh sách sản phẩm phù hợp qua `filteredProducts`.
   - **Xử lý sự kiện**:
     - Khi nhấn **Hủy**, đóng dialog mà không phát ra dữ liệu.
     - Khi nhấn **Xác nhận**, gọi hàm lọc sản phẩm và phát ra kết quả qua `filteredProducts`.

### Cấu trúc file
1. **`product-filter-dialog.component.ts`**:
   - Khai báo component với `@Input`, `@Output`, và dependencies (`MatDialogRef`, `MAT_DIALOG_DATA`).
   - Implement logic lọc sản phẩm và quản lý trạng thái bộ lọc.
2. **`product-filter-dialog.component.html`**:
   - Template với `MatCheckbox` cho nhóm hàng, `MatSelect` cho vị trí kho, `MatCheckbox` cho tùy chọn, và các button Hủy/Xác nhận.
3. **`product-filter-dialog.component.scss`**:
   - Style responsive sử dụng flex hoặc grid, tùy chỉnh Angular Material theme nếu cần.
