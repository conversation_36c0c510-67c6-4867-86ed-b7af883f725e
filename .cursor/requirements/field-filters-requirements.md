

### Yêu cầu chính
Tôi muốn tạo một **field filter component** trong dự án ERP, nhận một **array các field** qua `@Input` và hiển thị giao diện cho phép người dùng lọc dữ liệu theo từng field. Mỗi field có **loại dữ liệu (field type)** kh<PERSON>c nhau, dẫn đến các **tùy chọn lọc (filter options)** và **input bổ sung** khác nhau. Giao diện cần hiển thị:
- Một danh sách các field, mỗi field có **checkbox** (dấu tick) phía trước và **label** của field.
- Khi người dùng nhấn vào checkbox, một **bảng filter value** (panel hoặc dialog) sẽ hiện ra để chọn **filter operator** và nhập **gi<PERSON> trị lọc** (nế<PERSON> cần).
- T<PERSON><PERSON> thuộc vào **field type**, các **filter operator** và **input bổ sung** sẽ khác nhau.
- Giao diện phải **responsive**, sử dụng **Bootstrap** và tích hợp **i18n** (ngx-translate) cho các chuỗi văn bản.

---

### Chi tiết yêu cầu

#### 1. **Input của component**
- Component nhận một **array các field** qua `@Input`, với mỗi field tuân theo interface `Field` được định nghĩa trong [text](../../src/domain/entities/field.entity.ts).
- Interface `Field` bao gồm các loại:
  - **Text/Email/Phone/Url/Textarea**
  - **Number/Decimal/Currency/Percent**
  - **Date/DateTime**
  - **Picklist/MultiPicklist**
  - **Checkbox**
- Mỗi field có các thuộc tính như `_id`, `label`, `type`, `value`, và `constraints` (ví dụ: `maxLength`, `picklistValues`).

#### 2. **Giao diện hiển thị**
- **Danh sách field**:
  - Hiển thị danh sách các field, mỗi field có:
    - **Checkbox**: Cho phép bật/tắt lọc cho field đó.
    - **Label**: Tên field (lấy từ `field.label`), sử dụng i18n nếu cần.
  - Ví dụ giao diện:
    ```
    [ ] Product Name
    [ ] Price
    [ ] Created Date
    ```
- **Bảng filter value**:
  - Khi người dùng nhấn vào checkbox, một **panel** hoặc **dialog** xuất hiện bên dưới hoặc bên cạnh field, chứa:
    - Một **selectbox** hiển thị danh sách **filter operators** tương ứng với loại field.
    - Một hoặc nhiều **input** (nếu cần) để nhập giá trị lọc.
  - Giao diện phải **responsive**, sử dụng **Bootstrap** (ví dụ: grid system, classes như `form-control`, `form-select`).
  - Các chuỗi văn bản (label, filter operators) phải sử dụng **i18n** với key theo chuẩn `SCREAMING_SNAKE_CASE`.

#### 3. **Filter operators theo field type**
Mỗi loại field có các **filter operators** riêng, được hiển thị trong selectbox. Một số operator yêu cầu **input bổ sung** (như textbox, datepicker, chip-list).

##### a. **Text/Email/Phone/Url/Textarea**
- **Filter operators** (selectbox):
  - `is`
  - `isn't`
  - `contains`
  - `doesn't contain`
  - `starts with`
  - `ends with`
  - `is empty`
  - `is not empty`
- **Input bổ sung**:
  - Khi chọn `is`, `isn't`, `contains`, `doesn't contain`, `starts with`, `ends with`: Hiển thị một **textbox** để nhập giá trị (kiểu `string`).
  - Khi chọn `is empty`, `is not empty`: **Không hiển thị input bổ sung**.
- Ví dụ:
  - Nếu chọn `contains` cho field `Product Name`, hiển thị:
    ```
    [x] Product Name
        [contains] [_________]
    ```

##### b. **Number/Decimal/Currency/Percent**
- **Filter operators** (selectbox):
  - `=`
  - `!==`
  - `<`
  - `<=`
  - `>`
  - `>=`
  - `between`
  - `not between`
  - `is empty`
  - `is not empty`
- **Input bổ sung**:
  - Khi chọn `=`, `!==`, `<`, `<=`, `>`, `>=`: Hiển thị một **textbox** để nhập giá trị số.
  - Khi chọn `between`, `not between`: Hiển thị **hai textbox** để nhập khoảng giá trị (min, max).
  - Khi chọn `is empty`, `is not empty`: **Không hiển thị input bổ sung**.
- Ví dụ:
  - Nếu chọn `between` cho field `Price`, hiển thị:
    ```
    [x] Price
        [between] [_________] and [_________]
    ```

##### c. **Date/DateTime**
- **Filter operators** (selectbox):
  - `age in`
  - `due in`
  - `previous`
  - `next`
  - `on`
  - `before`
  - `after`
  - `between`
  - `not between`
  - `today`
  - `tomorrow`
  - `till yesterday`
  - `starting tomorrow`
  - `yesterday`
  - `this week`
  - `this month`
  - `previous week`
  - `previous month`
  - `this year`
  - `current fy` (năm tài chính hiện tại)
  - `current fq` (quý tài chính hiện tại)
  - `previous year`
  - `previous fy`
  - `previous fq`
  - `next year`
  - `next fq`
  - `is empty`
  - `is not empty`
- **Input bổ sung**:
  - Khi chọn `age in`, `due in`, `previous`, `next`:
    - Hiển thị một **textbox số** (nhập số) và một **selectbox** với các tùy chọn: `days`, `weeks`, `months`, `years`.
    - Ví dụ: `age in 2 days`.
  - Khi chọn `on`, `before`, `after`:
    - Hiển thị một **datepicker** để chọn ngày.
  - Khi chọn `between`, `not between`:
    - Hiển thị **hai datepicker** để chọn khoảng ngày.
  - Khi chọn `today`, `tomorrow`, `till yesterday`, `starting tomorrow`, `yesterday`, `this week`, `this month`, `previous week`, `previous month`, `this year`, `current fy`, `current fq`, `previous year`, `previous fy`, `previous fq`, `next year`, `next fq`, `is empty`, `is not empty`:
    - **Không hiển thị input bổ sung**.
- Ví dụ:
  - Nếu chọn `age in` cho field `Created Date`, hiển thị:
    ```
    [x] Created Date
        [age in] [2] [days]
    ```

##### d. **Picklist/MultiPicklist**
- **Filter operators** (selectbox):
  - `is`
  - `is not`
  - `is empty`
  - `is not empty`
- **Input bổ sung**:
  - Khi chọn `is`, `is not`:
    - Hiển thị một **mat-chip-list** với các gợi ý từ `constraints.picklistValues`.
    - Người dùng có thể chọn một hoặc nhiều giá trị (tùy thuộc vào `Picklist` hoặc `MultiPicklist`).
  - Khi chọn `is empty`, `is not empty`: **Không hiển thị input bổ sung**.
- Ví dụ:
  - Nếu chọn `is` cho field `Category` (Picklist), hiển thị:
    ```
    [x] Category
        [is] [Shirt] [Pants] [Shoes]
    ```

##### e. **Checkbox**
- **Filter operators** (selectbox):
  - `is`
- **Input bổ sung**:
  - Hiển thị một **selectbox** với hai tùy chọn: `selected`, `not selected`.
  - **Không hiển thị input bổ sung**.
- Ví dụ:
  - Nếu chọn `is` cho field `In Stock`, hiển thị:
    ```
    [x] In Stock
        [is] [selected]
    ```

#### 4. **Tích hợp i18n**
- Tất cả chuỗi văn bản (label, filter operators) phải sử dụng **ngx-translate** với key định dạng **SCREAMING_SNAKE_CASE**.
- Các file i18n được đặt trong `src/infra/i18n/shared/field-filters/<lang>.json` (ví dụ: `src/infra/i18n/shared/field-filters/en.json` và `vi.json`).


#### 5.1. **Cấu trúc component**


- **`FieldFiltersComponent` (Component chính)**:
  - **Vai trò**: 
    - Nhận `@Input fields: Field[]` từ `src/domain/entities/field.entity.ts`.
    - Hiển thị danh sách các field với checkbox và label.
    - Quản lý trạng thái tổng thể của các filter (danh sách field được chọn và giá trị lọc).
    - Emit sự kiện khi filter thay đổi thông qua `@Output filterChange`.
  - **Giao diện**: 
    - Danh sách các field sử dụng `mat-list` hoặc `Bootstrap list-group`.
    - Mỗi field hiển thị checkbox và label, khi checkbox được chọn, hiển thị `FilterFieldComponent`.
  - **Tích hợp**: 
    - Sử dụng `FilterFieldComponent` để render từng field.
    - Gọi `FieldFiltersService` để quản lý trạng thái và logic lọc.
  - **Change Detection**: Sử dụng `OnPush` để tối ưu hiệu suất.

- **`FilterFieldComponent` (Component con)**:
  - **Vai trò**:
    - Hiển thị giao diện lọc cho một field cụ thể (checkbox, label, selectbox cho filter operator, và input bổ sung).
    - Xử lý logic hiển thị filter operator và input dựa trên `field.type`.
  - **Giao diện**:
    - Checkbox và label cho field.
    - Khi checkbox được chọn, hiển thị một `mat-expansion-panel` hoặc `Bootstrap collapse` chứa:
      - Selectbox cho filter operator (ví dụ: `is`, `contains`, `between`).
      - Input bổ sung (textbox, datepicker, chip-list, v.v.) dựa trên `field.type`.
  - **Tích hợp**:
    - Nhận `@Input field: Field` và `@Output filterChange` để emit giá trị lọc.
    - Sử dụng directive `DynamicFilterInputDirective` để render input động.

- **`DynamicFilterInputDirective` (Directive)**:
  - **Vai trò**:
    - Tạo động các input bổ sung (textbox, datepicker, chip-list, v.v.) dựa trên `field.type` và filter operator.
    - Tăng tính tái sử dụng bằng cách tách logic render input khỏi `FilterFieldComponent`.
  - **Giao diện**:
    - Không có giao diện trực tiếp, chỉ render component input tương ứng (ví dụ: `TextFilterInputComponent`, `DateFilterInputComponent`).
  - **Tích hợp**:
    - Sử dụng `ComponentFactoryResolver` hoặc `ViewContainerRef` để tạo component động.
    - Nhận `field.type` và `operator` để quyết định component input nào sẽ được render.

- **`TextFilterInputComponent` (Component con)**:
  - **Vai trò**:
    - Hiển thị input cho các field loại `Text/Email/Phone/Url/Textarea`.
    - Xử lý các operator như `is`, `contains`, `starts with`, v.v.
  - **Giao diện**:
    - Textbox cho các operator như `is`, `contains`.
    - Không hiển thị input cho `is empty`, `is not empty`.
  - **Tích hợp**:
    - Nhận `@Input operator` và `@Input value`, emit giá trị qua `@Output valueChange`.

- **`NumberFilterInputComponent` (Component con)**:
  - **Vai trò**:
    - Hiển thị input cho các field loại `Number/Decimal/Currency/Percent`.
    - Xử lý các operator như `=`, `<`, `between`, v.v.
  - **Giao diện**:
    - Một textbox cho `=`, `<`, `>`, v.v.
    - Hai textbox cho `between`, `not between`.
  - **Tích hợp**:
    - Tương tự `TextFilterInputComponent`.

- **`DateFilterInputComponent` (Component con)**:
  - **Vai trò**:
    - Hiển thị input cho các field loại `Date/DateTime`.
    - Xử lý các operator như `age in`, `on`, `between`, `today`, v.v.
  - **Giao diện**:
    - Textbox số + selectbox (`days`, `weeks`) cho `age in`, `due in`.
    - Datepicker cho `on`, `before`, `after`.
    - Hai datepicker cho `between`, `not between`.
  - **Tích hợp**:
    - Tương tự `TextFilterInputComponent`.

- **`PicklistFilterInputComponent` (Component con)**:
  - **Vai trò**:
    - Hiển thị input cho các field loại `Picklist/MultiPicklist`.
    - Xử lý các operator như `is`, `is not`.
  - **Giao diện**:
    - `mat-chip-list` để chọn giá trị từ `constraints.picklistValues`.
  - **Tích hợp**:
    - Tương tự `TextFilterInputComponent`.

- **`CheckboxFilterInputComponent` (Component con)**:
  - **Vai trò**:
    - Hiển thị input cho field loại `Checkbox`.
    - Xử lý operator `is` với giá trị `selected` hoặc `not selected`.
  - **Giao diện**:
    - Selectbox với hai tùy chọn: `selected`, `not selected`.
  - **Tích hợp**:
    - Tương tự `TextFilterInputComponent`.


### 5.2. **Folder Structure**


src/infra/shared/components/field-filters/
├── filters/
│   ├── field-filters.component.ts           # Component chính
│   ├── field-filters.component.html         # Template HTML
│   ├── field-filters.component.scss         # Styles SCSS (Bootstrap)
│   └── field-filters.component.spec.ts      # Unit test
├── filter-field/
│   ├── filter-field.component.ts            # Component con cho từng field
│   ├── filter-field.component.html          # Template HTML
│   ├── filter-field.component.scss          # Styles SCSS
│   └── filter-field.component.spec.ts       # Unit test
├── dynamic-filter-input/
│   ├── dynamic-filter-input.directive.ts    # Directive render input động
│   └── dynamic-filter-input.directive.spec.ts # Unit test
├── text-filter-input/
│   ├── text-filter-input.component.ts       # Component cho Text/Email/Phone/Url/Textarea
│   ├── text-filter-input.component.html     # Template HTML
│   ├── text-filter-input.component.scss     # Styles SCSS
│   └── text-filter-input.component.spec.ts  # Unit test
├── number-filter-input/
│   ├── number-filter-input.component.ts     # Component cho Number/Decimal/Currency/Percent
│   ├── number-filter-input.component.html   # Template HTML
│   ├── number-filter-input.component.scss   # Styles SCSS
│   └── number-filter-input.component.spec.ts # Unit test
├── date-filter-input/
│   ├── date-filter-input.component.ts       # Component cho Date/DateTime
│   ├── date-filter-input.component.html     # Template HTML
│   ├── date-filter-input.component.scss     # Styles SCSS
│   └── date-filter-input.component.spec.ts  # Unit test
├── picklist-filter-input/
│   ├── picklist-filter-input.component.ts   # Component cho Picklist/MultiPicklist
│   ├── picklist-filter-input.component.html # Template HTML
│   ├── picklist-filter-input.component.scss # Styles SCSS
│   └── picklist-filter-input.component.spec.ts # Unit test
├── checkbox-filter-input/
│   ├── checkbox-filter-input.component.ts   # Component cho Checkbox
│   ├── checkbox-filter-input.component.html # Template HTML
│   ├── checkbox-filter-input.component.scss # Styles SCSS
│   └── checkbox-filter-input.component.spec.ts # Unit test
├── services/
│   ├── field-filters.service.ts             # Service quản lý trạng thái và logic lọc
│   └── field-filters.service.spec.ts        # Unit test
├── models/
│   ├── api/
│   │   └── field-filter.dto.ts                   # DTO cho API
│   └── view/
│       └── field-filter-view.model.ts            # View Model cho UI



#### 7. **Tối ưu hóa và bảo trì**
- **Responsive**: Sử dụng **Bootstrap grid** (ví dụ: `row`, `col-md-6`) để đảm bảo giao diện tương thích trên mọi thiết bị.
- **OnPush Change Detection**: Áp dụng cho `FiltersComponent` và `FilterFieldComponent`.
- **Lazy Loading**: Đảm bảo feature `filters` được lazy-loaded trong `filters.routes.ts`.
- **i18n**: Tất cả chuỗi văn bản phải sử dụng `ngx-translate` với key đúng cấu trúc.
- **Bảo mật**: Validate input người dùng (ví dụ: kiểm tra giá trị số hợp lệ, ngày hợp lệ).
- **Hiệu suất**: Sử dụng `trackBy` trong `*ngFor` để tối ưu render danh sách field.

#### 8. **Test giao diện**
- Test giao diện tại `http://localhost:4200/#/test` bằng cách thêm logic vào `@test-theme.component.ts`.
- Kiểm tra trên **Chrome** qua cổng 4200, đảm bảo không có lỗi giao diện (sử dụng **MCP server** để debug).
- Chạy `ng build` để kiểm tra lỗi biên dịch và sửa nếu có.



#### 10. **Quy trình thực hiện**
1. **Kiểm tra interface**:
   - Sử dụng `Field` từ `src/domain/entities/field.entity.ts`, không tự định nghĩa interface.
   - Nếu cần cấu trúc chi tiết của `Field`, hỏi lại người dùng.
2. **Tạo file i18n**:
3. **Tạo component/service**:
4. Nếu cần tạo thêm interface/model, viết thêm vào models/view/field-filter-view.model.ts 
5. **Test giao diện**:
   - Thêm logic test vào `@test-theme.component.ts`.
   - Kiểm tra trên `http://localhost:4200/#/test`.
6. **Kiểm tra lỗi**:
   - Chạy `ng build` để kiểm tra lỗi biên dịch.
   - Debug giao diện trên Chrome qua MCP server (cổng 4200).
7. **Lưu context**:
   - Ghi lại tiến độ vào `.cursor/context/filters-task.md` để theo dõi.

---

### Ví dụ minh họa giao diện
Giả sử `@Input fields` chứa:
```typescript
[
  { _id: 1, label: "Product Name", type: "text", value: "" },
  { _id: 2, label: "Price", type: "currency", value: "" },
  { _id: 3, label: "Created Date", type: "date", value: "" },
  { _id: 4, label: "Category", type: "picklist", value: "", constraints: { picklistValues: ["Shirt", "Pants", "Shoes"] } },
  { _id: 5, label: "In Stock", type: "checkbox", value: false }
]
```

Giao diện hiển thị:
```
[ ] Product Name
[x] Price
    [between] [100] and [500]
[ ] Created Date
[x] Category
    [is] [Shirt] [Pants]
[ ] In Stock
```

Khi nhấn checkbox của `Created Date`, panel hiện ra:
```
[x] Created Date
    [age in] [2] [days]
```
