Interface sử dụng: [text](../../../shared_contracts/js/dist/requests/sales/create_order.js)
Mock data sử dụng: [text](../../src/app/mock/sales/create_order.ts)



Định nghĩa:
- Block product options:  thêm các sản phẩm topping vào sản phẩm đã chọn, load từ Product Options List. Ví dụ với mô hình FnB, khi khách hàng chọn ăn phở gà, thêm topping thịt bò 10k chẳng hạn. (đã làm xong [text](../../src/app/features/sales/components/product-modifiers-sheet/product-modifiers-sheet.component.ts))
	- options List này là 1 MatBottomSheet
	- Mỗi 1 Product trong Product List sẽ có 1 list Product options List riêng, có cái sẽ không có
	- Các item trong Product options List không được phép có options nữa (chỉ 1 level)
	- MatBottomSheet Product options List cũng sẽ có các nút "+", "-" để tăg giảm số lượng, và cái nào đã được chọn cũng sẽ có class selected
	- Khi được chọn xong, item product bên ngoài sẽ được đổi tên thành: X và N sản phẩm khác. Ví dụ khi khách chọn món phở bò kêu thêm gà và cocacola. Thì item Phở bò bên ngoài (Đã selected) sẽ đổi tên thành Phở bò và 2 sản phẩm khác. Sau đó clone chính cái item Phở bò để có thể chọn tiếp. Ví dụ 1 bàn khách vào 3 người, 1 người gọi  Phở bò và 2 sản phẩm khác, nhưng 2 người khác chỉ gọi Phở bò. Thì sau khi thêm "Phở bò và 2 sản phẩm khác", phải clone item product chính là  "Phở bò" để nhấn chọn "Phở bò" tiếp.
- Block chọn sản phẩm từ danh sách sản phẩm (giống trong các hệ thống Point Of sale) (đã làm xong [text](../../src/app/features/sales/components/product-selection/product-selection.component.ts)):
	- Load ra toàn bộ sản phẩm từ mock Product List.
	- Có 2 dấu mũi tên ">" và "<" để navigate.
	- Dùng module "embla-carousel-angular" để swipe, có thể vuốt trái vuốt phải sang thay cho các ">" và "<"
	- Trong mỗi một Product List Item sẽ có 1 nút clone item bên phải trên cùng. Khi user nhấn vào nút này, sẽ có 1 item giống hệt item này sinh ra trong array product list (nhưng không được selected). Việc này nhằm mục đích khi user nhấn chọn 1 item nhưng thay đổi tên sản phẩm hoặc giá sản phẩm, rồi sau đó lại muốn tiếp tục chọn sản phẩm gốc này. Ví dụ sản phẩm X giá N, sau khi chọn user thay đổi tên X thành X1 và giá N1. Sau đó user lại muốn chọn lại sản phẩm X. Khi user thay đổi giá hoặc tên sản phẩm, tự động clone 1 sản phẩm theo sản phẩm đầu tiên ngay bên cạnh (tức nằm bên cành array index)
	- Những sản phẩm nào đã chọn sẽ được thêm class selected. Kèm theo số lượng đã chọn và thêm 3 
		- button "+" và "-" để tăng giảm số lượng chọn.
		- button add options để thêm hiện ra Block options (chỉ có button khi trong product item có list options , vì sẽ có product item có options , có cái không)
	- Phía trên cùng có :
		- Bên trái filter như: 
			- Tất cả, 
			- select form: Lọc theo danh mục load từ mock CategoryList
			- select form: Lọc theo thương hiệu load từ mock BrandList
		- Bên phải: Thêm mới sản phẩm
			- User nhấn vào load product form từ src/app/features/product/pages/product-form/product-form.component.ts
			- Khi user hoàn thành form và sản phẩm được tạo mới, append vào mock Product List
- Dialog khuyến mại (đã làm xong [text](../../src/app/features/sales/dialogs/promotion-dialog/promotion-dialog.component.ts))
	- Đơn giá: Đơn giá hiện tại
	- được chọn 1 trong 3 dưới đây:
		- Theo Số tiền: text field
		- Theo %: text field
		- Theo coupon: text field
	- Nội dung khuyến mãi: text field
	- Giá cuối: Giá đã trừ sau giảm giá
	- -> nếu user nhập coupon và nhấn enter, hiện ra thông tin coupon và giảm giá đi kèm với coupon
- Dialog ghi chú gồm các row (đã làm xong [text](../../src/app/features/sales/dialogs/note-dialog/note-dialog.component.ts)):
	- Select box chọn: Ghi chú nội bộ hoặc Ghi chú công khai (ghi chú công khai sẽ hiện ra bill lúc in ra)
	- Textarea để nhập ghi chú
	- -> sau khi nhập ghi chú sẽ tạo ra 1 row bên dưới row  (*1), trong đó nếu là ghi chú nội bộ sẽ là chữ đỏ (danger), ghi chú công khai là chữ nâu (alert)
- Dialog Thanh toán hỗn hợp (đã làm xong [text](../../src/app/features/sales/dialogs/mixed-payment-dialog/mixed-payment-dialog.component.ts))
	- Input đầu vào là tổng số tiền cần thanh toán
	- Các row phương thức thanh toán
		- Số tiền:  Input number, khi user click vào ô input ngay lập tức hiện ra autocomplete gồm 8 giá trị nhanh, chia làm 2 hàng, mỗi hàng 4 giá trị. Ví dụ khi tổng đơn hàng là 1625, các giá trị nhanh là: 1625, 1630, 1650, 1700, 2000. Khi user nhấn vào các giá trị nhanh, input nhận luôn giá trị
		- Phương thức thanh toán: select list
			- tiền mặt
			- Chuyển khoản: Khi bấm vào hiện ra 1 block bên dưới
				- Bên trái: QR code thanh toán
				- Bên phải:
					- Select list các tài khoản ngân hàng từ mock Tài khoản ngân hàng
			- thẻ tín dụng
			- ví điện tử
	- Button: Thêm phương thức thanh toán, khi nhấn vào sinh 1 row phương thức thanh toán
	- Khách cần trả: input đầu vào
	- Khách thanh toán: Tổng giá trị các phương thức thanh toán
	- Khách còn nợ: Tổng giá trị các phương thức thanh toán - input đầu vào

--

Mục đích cuối là build thành 1 object CreateOrderRequest hoàn chỉnh. Tôi cần với mỗi info dưới đây bạn tham chiếu vào các field của CreateOrderRequest. Ví dụ sau khi sửa xong thông tin customer thì sẽ tham chiếu vào CreateOrderRequest.customer chẳng hạn.

- Cần chia làm các component
	- 1 component để chia các tab
	- Trong từng tab:
		- Input thông tin đơn hàng, 
		- khách hàng, 
		- vận chuyển, 
		- order summary
- Một component lớn phải được chia thành các component con nhỏ hơn, mỗi component chỉ chịu trách nhiệm cho một phần giao diện cụ thể. 
- **Đẩy logic xuống service**: Component chỉ dùng để tương tác với UI, logic nghiệp vụ phải được đẩy xuống service.
- Mục đích cuối là mỗi tab build thành 1 object CreateOrderRequest hoàn chỉnh, đặt tên biến là order



--
Giao diện:

Region 1: Trên cùng các tab và dấu + để thêm tab mới. Mỗi tab là 1 Order
Region 2 chia làm 2 cột: Là tab đang active
- Cột 1 bên trái: Input của thông tin đơn hàng, khách hàng, vận chuyển
- Cột 2 bên phải: Order summary

Cột 1 bên trái: Input của thông tin đơn hàng, khách hàng, vận chuyển. Toàn bộ cột được bao bởi  ngx-scrollbar để scroll inside
1. Main Row 1: Input của thông tin đơn hàng, Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định mở; gồm 3 row chính:
	1. Row 1: Ô search sản phẩm. Ngay khi nhấn vào ô tìm kiếm sản phẩm, sẽ xổ xuống Block ProductSelectionComponent để chọn sản phẩm (dùng mat-menu). Truyền input {list: mockProductList, data: order.items} vào ProductSelectionComponent 
	2. Row 2: Danh sách sản phẩm đã chọn, Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định mở: Từ ProductSelectionComponent sẽ output ra OrderItemBaseDetails[], listen và render ra các order item.
		- Gồm các 6 col: (*1)
			- (icon) delete: Nhấn vào xóa row và remove item ra khỏi list những sản phẩm đã chọn
			- Sản phẩm: gồm các row
				- Dòng 1: Tên sản phẩm, ví dụ "Quần Gucci". Là text field để sửa nhanh tên sản phẩm. Ví dụ sửa thành "Quần Gucci giảm giá", nhấn enter là sửa luôn tên
				- Dòng 2: Mã SKU (không sửa)
				- Dòng 3: hiện variant (nếu có) với sản phầm nhiều biến thể. Ví dụ màu: Xanh, Size: XL. Các màu "Xanh", size "xl" làm thành 1 link có thể click được. Khi click vào hiện ra 1 mat-bottom-sheet [text](../../src/app/features/sales/dialogs/variant-selector-bottom-sheet/variant-selector-bottom-sheet.component.ts). Khi cập nhật VariantSelectorBottomSheetComponent, cập nhật giá và các thông tin bên ngoài
				- Dòng 4: hiện đơn vị tín ví dụ cái (nếu có) với sản phẩm có nhiều đơn vị tính. Chỗ "cái"làm thành 1 link có thể click được. Khi click vào hiện ra 1 mat-bottom-sheet [text](../../src/app/features/sales/dialogs/variant-selector-bottom-sheet/variant-selector-bottom-sheet.component.ts). Khi cập nhật VariantSelectorBottomSheetComponent, cập nhật giá và các thông tin bên ngoài
			- Số lượng: có 2 nút "+", "-" và số lượng đã chọn ở giữa. Số lượng là 1 text field
			- Đơn giá (*2): Là 1 text field đơn giá mặc định. Khi user sửa, ví dụ đơn giá hiện tại là 400. Khi user nhấn vào 400 thì nó sẽ thành text field để sửa nhanh giá.  Ví dụ sửa thành 500, nhấn enter là sửa luôn giá
			- Thành tiền: Tự động nhân giữa số lượng và đơn giá
			- Button Thêm sản phẩm tùy chọn: hiện ra bottom sheet [text](../../src/app/features/sales/components/product-modifiers-sheet/product-modifiers-sheet.component.ts)
			- (icon) more: click vào ra 1 mat-menu:
				- Thêm khuyến mại: Nhấn vào ra 1 Dialog khuyến mại, chú ý khuyến mại này là cho product item, không phải cả đơn hàng-> Sau khi giảm giá, phía dưới dòng đơn giá  (*2), sẽ hiện 1 dòng chữ đỏ là giảm bao nhiêu
				- Thêm ghi chú: Nhấn vào ra Dialog ghi chú, chú ý ghi chú này là cho product item, không phải cả đơn hàng -> sau khi nhập ghi chú sẽ tạo ra 1 row bên dưới row  (*1), trong đó nếu là ghi chú nội bộ sẽ là chữ đỏ (danger), ghi chú công khai là chữ nâu (alert)
		- Nếu row nào có ModifierGroups (các sản phẩm gọi thêm), mỗi sản phẩm sẽ được thụt vào so với sản phẩm parent. Còn render ra tưng row sản phẩm con giống sản phẩm cha.
	3. Row 3: 2 cột trái phải
		- Cột trái: Button "Thêm sản phẩm tùy chọn". Mục đích là thêm sản phẩm tùy chọn không nằm trong product list, chỉ áp dụng với đơn hàng này, không có quản lý kho hay có các nghiệp vụ phức tạp (ví dụ như phí dịch vụ, phí gói hàng...)
			- Khi nhấn vào button này row 3 sẽ tự sinh thêm 1 row gồm các col
				- Tên sản phẩm: text field
				- Số lượng
				- Đơn giá
				- Thành tiền
		- Cột phải:
			- Thêm ghi chú toàn đơn: Nhấn vào ra Dialog ghi chú, chú ý ghi chú này là cho cả đơn hàng -> sau khi nhập ghi chú sẽ tạo ra 1 row bên dưới row dưới cùng, trong đó nếu là ghi chú nội bộ sẽ là chữ đỏ (danger), ghi chú công khai là chữ nâu (alert)
			- Thêm khuyến mãi toàn đơn: Nhấn vào ra 1 Dialog khuyến mại, chú ý khuyến mại này là cho cả đơn hàng-> Sau khi giảm giá, phía bên cột bên phải "Order summary" sẽ sinh ra field khuyến mại
2. Main Row 2: Thông tin khách hàng, Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định mở; gồm các row:
	1. Số điện thoại: Auto complete khi user đánh ít nhất 3 chữ thì hiện ra mockCustomerAutocompleteList để user chọn. Nếu user chọn customer từ list, tự động điền các field còn lại
	2. Tên khách hàng: Auto complete khi user đánh ít nhất 3 chữ thì hiện ra mockCustomerAutocompleteList để user chọn. Nếu user chọn customer từ list, tự động điền các field còn lại
	3. Địa chỉ: load từ [text](../../src/app/shared/components/input/input-place/input-place.component.ts)
	4. Ghi chú khách hàng: textbox
	5. Khi user chọn khách hàng từ list, hiện các row:
		1. chấm điểm khách hàng. Ở đây  hiển thị là Chấm điểm khách hàng: 9/10 (màu xanh)
		2. Hệ thống tự chấm điểm khách hàng với số lần thành công: 90%; Hoàn hàng: 5%; Trạng thái khác: 5%
		3. tags: ví dụ khách thường xuyên, lâu không đặt
3. Main Row 3: Thông tin vận chuyển, Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định mở; gồm các row chính:
	1. Thông tin người nhận: Là 1 Expansion-panel: https://material.angular.io/components/expansion/overview, mặc định đóng. Copy từ thông tin khách hàng bên trên (nhằm mục đích khi khách hàng là 1 người nhưng người nhận là người khác, như họ gửi hộ chẳng hạn). User vẫn có thể thay đổi thông tin người nhận nếu 2 thông tin này khác nhau. Các field bên trong giống trong main row 2 - thông tin khách hàng
	2. Thông tin gói hàng
		1. Khoảng cách: textbox number
		2. Trọng lượng (gram): number text box
		3. Kích thước (cm): 3 textbox cùng 1 hàng: Chiều cao - Chiều dài - Chiều rộng
		4. Địa chỉ lấy hàng: Select các địa điểm ví dụ chi nhánh 1, chi nhánh 2
		5. Tự vận chuyển: toggle, nếu on thì disable đối tác giao hàng
		6. 1 radio button: Giao ngay hoặc giao sau
		7. Đối tác giao hàng
			- tùy vào chọn giao ngay hay giao sau load ra list danh sách các đối tác giao hàng
			- với mỗi đói tác giao hàng có cả phí đã tính sẵn, tạm thời load từ mock ra
		9. Thử hàng: select options:
			1. Cho xem hàng, không cho thử
			2. Cho phép thử
			3. Không cho xem hàng
			4. Cho xem, không lấy thu ship
		10. Phí giao hàng thu khách: textbox default theo đơn vị giao hàng
		11. Phí giao trả đối tác: textbox default theo đơn vị giao hàng
		12. Đối tượng trả phí ship: radio button: Khách trả, Shop trả
		13. Hình thức giao hàng: radio button: Bưu tá đến lấy, Gửi hàng tại bưu cục
		14. Thời gian lấy hàng: select: Cả ngày; 8h-12h; 13-17h
		15. Phí hoàn đơn hàng: textbox
		16. Ghi chú vận chuyển: textbox
		17. Tự động gửi đơn sang đơn vị vận chuyển: Toggle default true


Cột 2 bên phải: Order summary/ Thanh toán. Toàn bộ cột được bao bởi  ngx-scrollbar để scroll inside
- Block Thanh Toán gồm các row
	- Row 1:
		- Bên trái: 
			- Select box chọn nhân viên bán hàng từ mockEmployeeList
			- selectbox Kênh bán hàng: mockSaleChannelList
		- Bên phải: input chọn ngày giờ (là createdAt)
	- Row 2: Các button cùng hàng
		- Thêm thẻ: Khi user nhấn vào thêm thẻ, sinh 1 row [text](../../src/app/shared/components/chip-form-field/chip-form-field.component.ts) bên dưới
		- Ghi chú
		- Giảm giá
  - Các row tự sinh:
    - Tổng tiền hàng: 
    - Giảm giá: 
      - Click vào nút giảm giá ra Dialog khuyến mại
    - Phí vận chuyển
    - Phụ thu
    - Khách cần trả
	- Khách thanh toán: Input number, khi user click vào ô input ngay lập tức hiện ra autocomplete gồm 8 giá trị nhanh, chia làm 2 hàng, mỗi hàng 4 giá trị. Ví dụ khi tổng đơn hàng là 1625, các giá trị nhanh là: 1625, 1630, 1650, 1700, 2000. Khi user nhấn vào các giá trị nhanh, input nhận luôn giá trị
	- Phương thức thanh toán: 
		- radio button (1)
			- Tiền mặt
			- Chuyển khoản: Khi bấm vào hiện ra 1 block bên dưới
				- Bên trái: QR code thanh toán
				- Bên phải:
					- Select list các tài khoản ngân hàng từ mockBankList
		- Dấu 3 chấm khi bấm vào ra 1 mat-menu
			- Thẻ tín dụng: Nếu chọn thì cho dấu tick đằng trước
			- Ví điện tử: Nếu chọn thì cho dấu tick đằng trước
			- Thanh toán hỗn hợp: Nếu chọn thì cho dấu tick đằng trước
				- Bấm vào ra dialog Thanh toán hỗn hợp
				- Khi khách chọn phương thức thanh toán hôn hợp và nhấn ok, sinh 1 row bên dưới:
					- Bên trái: Thanh toán hỗn hợp
					- Bên phải:
						- x tiền mặt
						- x chuyển khoản
						- button pencil để edit, nếu bấm vào ra dialog Thanh toán hỗn hợp
  - Các row tự sinh:
	- Trả lại khách
	- Công nợ khách
	- Dư nợ hiện tại
	- Thời gian giao hàng: Selectbox: càng sớm càng tốt | giao hàng sau. Nếu user chọn giao hàng sau sinh 1 row ở dưới là datetime field -> set trạng thái là preorder, thêm vào các field scheduledOrderInfo.

	- Trạng thái: Select box theo mockOrderStatuses
- Block Nút thanh toán: Tổng số tiền: Chỉ khi tất cả valid mới hiện ra, mặc định disable




