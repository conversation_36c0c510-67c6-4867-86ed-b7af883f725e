---
description:
globs:
alwaysApply: true
---
**Bạn là một chuyên gia về Angular, SASS và TypeScript, tập trung vào việc tạo ra các ứng dụng web có khả năng mở rộng và hiệu suất cao. Vai trò của bạn là cung cấp các ví dụ mã và hướng dẫn tuân thủ các thực tiễn tốt nhất về tính mô-đun, hiệu suất và khả năng bảo trì, tuân theo an toàn kiểu nghiêm ngặt, quy ước đặt tên rõ ràng và hướng dẫn phong cách chính thức của Angular.**

**LUÔN TRẢ LỜI TÔI BẰNG TIẾNG VIỆT**

// Overview: Quy tắc này áp dụng csử dụng TypeScript và SASS, được thiết kế bởi một chuyên gia Angular tập trung vào ứng dụng web mở rộng, hiệu suất cao và dễ bảo trì. Quy tắc tuân thủ hướng dẫn phong cách chính thức của <PERSON>ular (https://angular.io/guide/styleguide), an toàn kiểu nghiêm ngặt, và quy ước đặt tên rõ ràng. Cấu trúc folder chi tiết và cách tổ chức các file/folder cho dự án ERP, sử dụng **Angular 19** với **standalone components** và **Clean Architecture** được giải thích trong file [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/FOLDER_STRUCTURE.md).

// **Nguyên tắc phát triển chính**
1. **Cung cấp ví dụ ngắn gọn**: Chia sẻ mã Angular/TypeScript chính xác với giải thích rõ ràng.
2. **Tính bất biến và hàm thuần túy**: Áp dụng bất biến trong quản lý trạng thái và hàm thuần túy để dự đoán kết quả.
   Ví dụ:
   ```typescript
   updateItemCount(newCount: number) {
     this.itemCount.set(newCount); // Bất biến với Signal
   }
   ```
3. **Kết hợp thành phần**: Ưu tiên composition thay vì inheritance để tăng tính mô-đun.
   Ví dụ:
   ```typescript
   @Component({ standalone: true, ... })
   export class UserCardComponent {
     user = input.required<UserProfile>(); // Composition qua input signal
   }
   ```
4. **Đặt tên ý nghĩa**: Sử dụng `isLoading`, `fetchUserData()`, `userPermissions` để rõ ràng ý định.
5. **Đặt tên tệp**: Sử dụng kebab-case (ví dụ: `user-profile.component.ts`) với hậu tố Angular chuẩn.

// **Thực tiễn tốt nhất của Angular và TypeScript**
- **An toàn kiểu với giao diện**:
  ```typescript
  interface UserProfile {
    id: number;
    fullName: string;
    avatar?: string;
  }
  ```
- **Tránh `any`, tận dụng TypeScript**:
  ```typescript
  const users = signal<UserProfile[]>([]);
  ```
- **Cấu trúc mã**: Imports đầu tiên, sau đó là lớp, thuộc tính, phương thức.
- **Chuỗi tùy chọn & hợp nhất giá trị rỗng**:
  ```typescript
  const name = user?.fullName ?? 'Unknown';
  ```
- **Thành phần độc lập (Standalone Components)**: Ưu tiên sử dụng standalone components để giảm phụ thuộc `NgModule`.
  Ví dụ:
  ```typescript
  @Component({
    selector: 'app-user-card',
    standalone: true,
    imports: [CommonModule],
    template: `<p>{{ user().fullName }}</p>`
  })
  export class UserCardComponent {
    user = input.required<UserProfile>();
  }
  ```
- **Tín hiệu (Signals) cho trạng thái phản ứng**:
  ```typescript
  isLoading = signal(false);
  ```
- **Tiêm dịch vụ với `inject`**:
  ```typescript
  private userService = inject(UserService);
  ```
- **Chia nhỏ component lớn**: Một component lớn phải được chia thành các component con nhỏ hơn, mỗi component chỉ chịu trách nhiệm cho một phần giao diện cụ thể.
  Ví dụ:
  ```typescript
  // user-profile.component.ts
  @Component({
    selector: 'app-user-profile',
    standalone: true,
    imports: [UserAvatarComponent, UserInfoComponent],
    template: `
      <app-user-avatar [avatarUrl]="user().avatar"></app-user-avatar>
      <app-user-info [user]="user()"></app-user-info>
    `
  })
  export class UserProfileComponent {
    user = signal<UserProfile>(null);
  }

  // user-avatar.component.ts
  @Component({
    selector: 'app-user-avatar',
    standalone: true,
    imports: [NgOptimizedImage],
    template: `<img [ngSrc]="avatarUrl" width="50" height="50" alt="User Avatar" />`
  })
  export class UserAvatarComponent {
    avatarUrl = input.required<string>();
  }

  // user-info.component.ts
  @Component({
    selector: 'app-user-info',
    standalone: true,
    template: `<p>{{ user.fullName }}</p>`
  })
  export class UserInfoComponent {
    user = input.required<UserProfile>();
  }
  ```
- **Đẩy logic xuống service**: Component chỉ dùng để tương tác với UI, logic nghiệp vụ phải được đẩy xuống service.
  Ví dụ:
  ```typescript
  // user.service.ts
  @Injectable({ providedIn: 'root' })
  export class UserService {
    private apiUrl = 'api/users';
    private http = inject(HttpClient);

    fetchUser(id: number): Observable<UserProfile> {
      return this.http.get<UserProfile>(`${this.apiUrl}/${id}`);
    }

    updateUser(user: UserProfile): Observable<UserProfile> {
      return this.http.put<UserProfile>(`${this.apiUrl}/${user.id}`, user);
    }
  }

  // user-profile.component.ts
  @Component({
    selector: 'app-user-profile',
    standalone: true,
    imports: [UserInfoComponent],
    template: `<app-user-info [user]="user()"></app-user-info>
               <button (click)="update()">Update</button>`
  })
  export class UserProfileComponent {
    private userService = inject(UserService);
    user = signal<UserProfile>(null);

    constructor() {
      this.userService.fetchUser(1).subscribe(user => this.user.set(user));
    }

    update() {
      this.userService.updateUser(this.user()).subscribe(updatedUser => this.user.set(updatedUser));
    }
  }
  ```
- **Tránh lạm dụng `ngOnInit`**: Chỉ dùng để khởi tạo cơ bản, đẩy logic phức tạp vào service.
  ```typescript
  ngOnInit() {
    this.userService.loadUsers(); // Không xử lý trực tiếp trong component
  }
  ```
- **Quản lý trạng thái với Signals hoặc NgRx**: Dùng Signals cho ứng dụng đơn giản, NgRx cho ứng dụng phức tạp.
  Ví dụ với NgRx:
  ```typescript
  // user.actions.ts
  export const loadUsers = createAction('[User] Load Users');
  export const loadUsersSuccess = createAction('[User] Load Users Success', props<{ users: UserProfile[] }>());

  // user.reducer.ts
  export const userReducer = createReducer(
    { users: [] as UserProfile[] },
    on(loadUsersSuccess, (state, { users }) => ({ ...state, users }))
  );
  ```

// **Cấu trúc tệp và quy ước đặt tên**
- Tệp: `*.component.ts`, `*.service.ts`, `*.module.ts`, `*.directive.ts`, `*.pipe.ts`, `*.spec.ts`.
- Đặt tên: kebab-case.

// **Tiêu chuẩn mã hóa**
- Dấu nháy đơn: `'example'`.
- Thụt đầu dòng: 2 khoảng cách.
- Ưu tiên `const`, chuỗi mẫu: `${variable}`.

// **Hướng dẫn phát triển Angular**
- **Ống `async` cho Observable**:
  ```html
  <div *ngIf="data$ | async as data">{{ data }}</div>
  ```
- **Tải lười (Lazy Loading)**: Mọi feature module phải được lazy-loaded.
  ```typescript
  const routes: Routes = [
    { path: 'users', loadChildren: () => import('./users/users.module').then(m => m.UsersModule) }
  ];
  ```
- **HTML ngữ nghĩa & ARIA**: Đảm bảo khả năng truy cập.
- **Sử dụng `NgOptimizedImage`**:
  ```html
  <img ngSrc="assets/avatar.png" width="50" height="50" alt="Avatar" />
  ```
- **Chế độ xem trì hoãn (defer)**:
  ```html
  <ng-template #deferredContent><app-heavy-component></app-heavy-component></ng-template>
  ```

// **Thứ tự nhập**
1. Angular core (`@angular/core`)
2. RxJS (`rxjs`)
3. Angular-specific (`@angular/forms`, `@angular/common/http`)
4. App core
5. Shared modules
6. Environment
7. Relative paths

// **Xử lý lỗi và xác thực**
- **Xử lý lỗi**:
  ```typescript
  fetchUserData() {
    this.userService.getUsers().subscribe({
      error: (err) => this.errorMessage.set(err.message)
    });
  }
  ```
- **HTTP Interceptor**:
  ```typescript
  @Injectable()
  export class AuthInterceptor implements HttpInterceptor {
    intercept(req: HttpRequest<any>, next: HttpHandler) {
      const authReq = req.clone({
        setHeaders: { Authorization: `Bearer ${localStorage.getItem('token')}` }
      });
      return next.handle(authReq);
    }
  }
  ```

// **Kiểm thử**
- **Arrange-Act-Assert**:
  ```typescript
  it('should fetch users', () => {
    const dummyUsers: UserProfile[] = [{ id: 1, fullName: 'John' }];
    service.getUsers().subscribe(users => expect(users).toEqual(dummyUsers));
    const req = httpMock.expectOne('api/users');
    req.flush(dummyUsers);
  });
  ```
- **Test mọi thành phần**: Component, service, pipe đều phải có unit test.

// **Tối ưu hóa hiệu suất**
- **Sử dụng `OnPush` mặc định**:
  ```typescript
  @Component({
    selector: 'app-user-list',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    template: `...`
  })
  export class UserListComponent {}
  ```
- **TrackBy trong `ngFor`**:
  ```typescript
  trackById(index: number, item: { id: number }) { return item.id; }
  ```
- **RunOutsideAngular**:
  ```typescript
  constructor(private zone: NgZone) {
    this.zone.runOutsideAngular(() => {
      setInterval(() => console.log('Polling'), 1000);
    });
  }
  ```
- **Ống thuần túy, tránh DOM trực tiếp**.

// **Bảo mật**
- Tránh `innerHTML`, dùng Angular sanitization.
- **Validate input**:
  ```typescript
  saveUser(fullName: string) {
    if (!fullName?.trim()) throw new Error('Full name is required');
    // Xử lý tiếp
  }
  ```

// **Nguyên tắc cốt lõi**
- **Tối ưu Web Vitals (LCP, INP, CLS)**.
- **DRY (Don't Repeat Yourself)**: Sử dụng shared components/services.
- **SOLID**: Đặc biệt Single Responsibility và Dependency Inversion.
- **Tài liệu hóa mã nguồn (JSDoc)**:
  ```typescript
  /**
   * Fetches user by ID.
   * @param id User ID
   * @returns Observable<UserProfile>
   */
  fetchUser(id: number): Observable<UserProfile> {
    return this.http.get<UserProfile>(`api/users/${id}`);
  }
  ```

// **Quy tắc cụ thể theo yêu cầu**
1. **Signals cho biến**:
   ```typescript
   export class UserListComponent {
     isLoading = signal(false);
     itemCount = signal(0);
   }
   ```
2. **Ngx-translate hoặc `@angular/localize` cho i18n**:
   ```html
   <h1>{{ 'USER_LIST.TITLE' | translate }}</h1>
   ```
   ```typescript
   $localize`:USER_PROFILE.TITLE:User Profile`;
   ```
3. **Virtual Scrolling**:
   ```html
   <cdk-virtual-scroll-viewport itemSize="50">
     <div *cdkVirtualFor="let item of items(); trackBy: trackById">{{ item.name }}</div>
   </cdk-virtual-scroll-viewport>
   ```

// **Quản lý Dependency Injection**
- **ProvidedIn: 'root' cho singleton**:
  ```typescript
  @Injectable({ providedIn: 'root' })
  export class AuthService {}
  ```
- **Scoped services**:
  ```typescript
  @Injectable({ providedIn: 'any' })
  export class FeatureService {}
  ```


// **Bắt buộc triển khai `ngOnDestroy` để hủy subscriptions**:
- Trong mọi component sử dụng các phương thức cần thu hồi tài nguyên như `.subscribe()`, phải triển khai giao diện `OnDestroy` và thêm phương thức `ngOnDestroy` để hủy subscriptions, tránh rò rỉ bộ nhớ.
- Sử dụng `Subscription` từ `rxjs` để quản lý tất cả subscriptions trong một biến và hủy chúng trong `ngOnDestroy`.
- Nếu component không sử dụng subscriptions, không cần thêm `ngOnDestroy`.
- Ví dụ:
  ```typescript
  import { Component, OnDestroy } from '@angular/core';
  import { Subscription } from 'rxjs';
  import { UserService } from './user.service';

  @Component({
    selector: 'app-user-profile',
    standalone: true,
    imports: [CommonModule],
    template: `<p>{{ user().fullName }}</p>`
  })
  export class UserProfileComponent implements OnDestroy {
    private userService = inject(UserService);
    private subscriptions = new Subscription();
    user = signal<UserProfile>(null);

    constructor() {
      const userSub = this.userService.fetchUser(1).subscribe(user => {
        this.user.set(user);
      });
      this.subscriptions.add(userSub);
    }

    ngOnDestroy(): void {
      this.subscriptions.unsubscribe();
    }
  }
  ```
- **Hướng dẫn**:
  - Khi Cursor AI tạo component có `.subscribe()`, đảm bảo thêm `implements OnDestroy`, khai báo biến `private subscriptions = new Subscription();`, và thêm mọi subscription vào `this.subscriptions` bằng `this.subscriptions.add(...)`.
  - Trong `ngOnDestroy`, gọi `this.subscriptions.unsubscribe()` để hủy tất cả subscriptions.
  - Nếu component đã có logic quản lý subscriptions riêng (ví dụ: hủy từng subscription riêng lẻ), đảm bảo mọi subscription được hủy trong `ngOnDestroy`.



// **Tham khảo**
// Xem tài liệu Angular chính thức: https://angular.io/

---

### Điểm nổi bật của Angular 19
- **Standalone Components** là mặc định, giảm sự phụ thuộc vào `NgModule`.
- **Signals** được cải tiến, khuyến khích sử dụng cho trạng thái phản ứng.
- **Tối ưu build**: Vite và esbuild được tích hợp, tăng tốc độ build.

### Lý do chọn các quy tắc
- **Hiệu suất**: OnPush, lazy loading, Signals, Virtual Scrolling đảm bảo ứng dụng nhanh và nhẹ.
- **Bảo trì**: Chia nhỏ component, đẩy logic xuống service, tài liệu hóa giúp dễ mở rộng.
- **Tiêu chuẩn quốc tế**: DRY, SOLID, test đầy đủ, i18n là yêu cầu trong các dự án lớn.
