---
description:
globs:
alwaysApply: true
---
## Tổng quan về dự án
- <PERSON><PERSON><PERSON> là dự án ERP sử dụng Angular 19 với các **standalone components** và **Clean Architecture**.
- Dự án bao gồm các module chính:
  - **<PERSON><PERSON> (cashier)**: <PERSON><PERSON><PERSON> đơn tạ<PERSON>, lịch sử đơn hàng.
  - **WMS (warehouse)**: <PERSON><PERSON><PERSON>, điều chỉnh tồn kho.
  - **ECOMMERCE**: Đồng bộ và quản lý đơn hàng từ Shopee, Lazada.
  - **Settings**: <PERSON><PERSON><PERSON> đặt hệ thống (ví dụ: bật/tắt chế độ tối).
- <PERSON><PERSON>c ngành hàng mục tiêu:
  - **Thời trang**: Quản lý kích cỡ (`Size`), màu sắc.
  - **Đ<PERSON> ăn**: Quản lý hạn sử dụng (`ExpirationDate`).
- T<PERSON><PERSON> hợp TMĐT: Đồng bộ đơn hàng từ Shopee, <PERSON><PERSON><PERSON>.
- Cấu trúc folder được định nghĩa chi tiết trong file [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/frontend/FOLDER_STRUCTURE.md)

## Quy tắc chung khi viết mã
- Sử dụng **TypeScript** cho tất cả các file.
- Tuân thủ **Clean Architecture** với 3 tầng:
  - **Domain**: Chứa entities (đối tượng kinh doanh) và business rules (quy tắc kinh doanh), độc lập với Angular và các framework.
  - **Application**: Chứa use cases, điều phối logic kinh doanh, kết nối Domain với Infrastructure.
  - **Infrastructure**: Chứa UI (components), services, adapters (gọi API), DTO, và View Models.
- Tất cả **components** phải là **standalone**, chỉ import các module cần thiết như `CommonModule`, `FormsModule`, `ReactiveFormsModule`.
- Sử dụng **RxJS Observable** cho các tác vụ bất đồng bộ, không sử dụng `Promise`.
- Mã phải tuân thủ **lazy loading** thông qua các file `*.routes.ts` sử dụng `loadComponent` hoặc `loadChildren`.
- Tất cả components sử dụng **OnPush Change Detection** để tối ưu hiệu suất.
- Sử dụng **ESLint** và **Prettier** để đảm bảo mã thống nhất và không có lỗi cú pháp.
- Không sử dụng các thư viện bên ngoài trừ khi được liệt kê rõ ràng (ví dụ: RxJS, Angular CDK, ngx-translate).

## Quy tắc về cấu trúc folder
Dựa trên [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/FOLDER_STRUCTURE.md), dưới đây là các quy tắc chi tiết cho từng thư mục:

### 1. `src/domain/` - Tầng Domain
- **Mục đích**: Chứa **entities** (đối tượng kinh doanh) và **business rules** (quy tắc kinh doanh), độc lập với Angular và tầng Infrastructure.
- **Quy tắc**:
  - **Không import** bất kỳ file nào từ `src/app/` (bao gồm `core/`, `shared/`, `features/`) hoặc `src/application/` để tuân thủ **Dependency Rule** của Clean Architecture.
  - Mỗi file trong `entities/` định nghĩa một **entity** hoặc **value object** bằng TypeScript class hoặc interface.
  - Mỗi file trong `rules/` chứa các hàm hoặc class xử lý quy tắc kinh doanh, không phụ thuộc vào hệ thống bên ngoài.
  - Các file trong `domain/` được dùng chung cho tất cả các module (`POS`, `WMS`, `ECOMMERCE`) và ngành hàng (Thời trang, Đồ ăn).
- **Thư mục con**:
  - **`entities/`**:
    - Chứa các file như:
      - `product.ts`: Định nghĩa entity `Product` (id, name, price, stock).
      - `order.ts`: Định nghĩa entity `Order` (id, items, totalPrice).
      - `money.ts`: Định nghĩa value object `Money` (amount, currency).
      - `size.ts`: Định nghĩa value object `Size` (value, ví dụ: 'S', 'M') cho ngành Thời trang.
      - `expiration-date.ts`: Định nghĩa value object `ExpirationDate` (date) cho ngành Đồ ăn.
    - Ví dụ nội dung `product.ts`:
      ```typescript
      export class Product {
        constructor(
          public id: string,
          public name: string,
          public price: number,
          public stock: number
        ) {}
      }
      ```
  - **`rules/`**:
    - Chứa các file như:
      - `pricing-rules.ts`: Hàm tính giá đơn hàng (chiết khấu, thuế).
      - `inventory-rules.ts`: Hàm kiểm tra tồn kho hoặc tính toán chênh lệch kiểm kho.
    - Ví dụ nội dung `inventory-rules.ts`:
      ```typescript
      export class InventoryRules {
        static calculateDifference(actual: number, expected: number): number {
          return actual - expected;
        }
      }
      ```
- **Lưu ý**:
  - Không đặt logic giao tiếp API (như gọi HTTP) hoặc logic UI (như hiển thị modal) trong `domain/`.
  - Đảm bảo các hàm trong `rules/` là **pure functions** (không có side effects).

### 2. `src/application/` - Tầng Application
- **Mục đích**: Chứa **use cases** điều phối logic kinh doanh, kết nối `domain/` với `core/services/` trong tầng Infrastructure.
- **Quy tắc**:
  - Chỉ import từ `src/domain/` (entities, rules) và `src/app/core/services/` (adapters như `http.service.ts`), không import từ `src/app/features/`.
  - Mỗi use case là một file trong `use-cases/<feature>/`, sử dụng TypeScript class hoặc function.
  - Business logic chính phải nằm trong `domain/` (rules) hoặc `application/` (use cases), không rò rỉ vào `features/`.
  - Use cases gọi các adapters trong `core/services/` để giao tiếp với hệ thống bên ngoài (API, database).
- **Thư mục con**:
  - **`use-cases/`**:
    - Chia theo feature:
      - `cashier/`:
        - `create-order.usecase.ts`: Tạo đơn hàng tại quầy, gọi `pricing-rules.ts` và `http.service.ts`.
        - `cancel-order.usecase.ts`: Hủy đơn hàng.
      - `warehouse/`:
        - `inventory-check.usecase.ts`: Thực hiện kiểm kho, gọi `inventory-rules.ts`.
        - `stock-adjustment.usecase.ts`: Điều chỉnh tồn kho.
      - `ecommerce/`:
        - `sync-order.usecase.ts`: Đồng bộ đơn hàng từ Shopee, Lazada.
        - `cancel-sync-order.usecase.ts`: Hủy đồng bộ đơn hàng.
    - Ví dụ nội dung `inventory-check.usecase.ts`:
      ```typescript
      import { InventoryCheck } from '../../../domain/entities/inventory-check';
      import { InventoryRules } from '../../../domain/rules/inventory-rules';
      import { InventoryCheckAdapter } from '../../../app/core/services/inventory-check-adapter';
      import { Observable } from 'rxjs';

      export class InventoryCheckUseCase {
        constructor(private adapter: InventoryCheckAdapter) {}

        execute(inventoryCheck: InventoryCheck): Observable<any> {
          const difference = InventoryRules.calculateDifference(
            inventoryCheck.actualQuantity,
            inventoryCheck.expectedQuantity
          );
          return this.adapter.saveInventoryCheck({ ...inventoryCheck, difference });
        }
      }
      ```
- **Lưu ý**:
  - Không chứa logic UI (như mở modal) hoặc state management (như BehaviorSubject).
  - Đảm bảo use cases trả về **Observable** cho các tác vụ bất đồng bộ.

### 3. `src/app/core/` - Thành phần toàn cục (Tầng Infrastructure)
- **Mục đích**: Chứa services và cấu hình toàn cục, giao tiếp với hệ thống bên ngoài (API, database).
- **Quy tắc**:
  - Không chứa pipes, directives, hoặc models (DTO, View Models).
  - Services trong `core/services/` chỉ xử lý giao tiếp với hệ thống bên ngoài, không chứa business logic.
  - Interceptors và guards phải được đăng ký trong `app.module.ts` hoặc `app.routes.ts`.
- **Thư mục con**:
  - **`services/`**:
    - Chứa các file như:
      - `http.service.ts`: Xử lý HTTP requests (GET, POST, PUT, DELETE).
      - `shopee-adapter.ts`: Gọi API Shopee để đồng bộ đơn hàng.
      - `lazada-adapter.ts`: Gọi API Lazada để đồng bộ đơn hàng.
    - Ví dụ nội dung `shopee-adapter.ts`:
      ```typescript
      import { Injectable } from '@angular/core';
      import { HttpService } from './http.service';
      import { Observable } from 'rxjs';
      import { ShopeeOrder } from '../../features/ecommerce/sync-orders/models/api/shopee-order.dto';

      @Injectable({ providedIn: 'root' })
      export class ShopeeAdapter {
        constructor(private http: HttpService) {}

        syncOrders(): Observable<ShopeeOrder[]> {
          return this.http.get<ShopeeOrder[]>('/shopee/orders');
        }
      }
      ```
  - **`interceptors/`**:
    - Chứa các file như:
      - `auth.interceptor.ts`: Thêm token vào HTTP requests.
  - **`guards/`**:
    - Chứa các file như:
      - `auth.guard.ts`: Kiểm tra quyền truy cập trước khi load route.
- **Lưu ý**:
  - Services phải được cung cấp ở phạm vi `root` (`providedIn: 'root'`).
  - Không đặt logic tính toán (như tính giá đơn hàng) trong `core/services/`.

### 4. `src/app/shared/` - Thành phần dùng chung (Tầng Infrastructure)
- **Mục đích**: Chứa DTO, View Models, components, pipes, và directives tái sử dụng giữa các features.
- **Quy tắc**:
  - Chỉ đặt DTO/View Models trong `shared/models/` nếu được dùng bởi nhiều features (như `product.dto.ts`).
  - Components, pipes, và directives phải là **standalone**, chỉ import modules cần thiết.
  - Mỗi component có thư mục riêng chứa `*.component.ts`, `*.component.html`, `*.component.scss`.
- **Thư mục con**:
  - **`models/api/`**:
    - Chứa DTO dùng chung:
      - `product.dto.ts`: Dữ liệu Product từ API.
      - `order.dto.ts`: Dữ liệu Order từ API.
    - Ví dụ nội dung `product.dto.ts`:
      ```typescript
      export interface ProductDto {
        id: string;
        name: string;
        price: number;
        stock: number;
      }
      ```
  - **`models/view/`**:
    - Chứa View Models dùng chung:
      - `cart-item-view.model.ts`: Hiển thị mục trong giỏ hàng.
      - `product-view.model.ts`: Hiển thị sản phẩm trên UI.
    - Ví dụ nội dung `product-view.model.ts`:
      ```typescript
      export interface ProductViewModel {
        id: string;
        name: string;
        displayPrice: string; // Định dạng giá cho UI
        inStock: boolean;
      }
      ```
  - **`components/`**:
    - Chứa standalone components tái sử dụng:
      - `button/`:
        - `button.component.ts`, `button.component.html`, `button.component.scss`.
      - `spinner/`:
        - `spinner.component.ts`, `spinner.component.html`, `spinner.component.scss`.
    - Ví dụ nội dung `button.component.ts`:
      ```typescript
      import { Component, Input } from '@angular/core';
      import { CommonModule } from '@angular/common';

      @Component({
        selector: 'app-button',
        standalone: true,
        imports: [CommonModule],
        templateUrl: './button.component.html',
        styleUrls: ['./button.component.scss'],
        changeDetection: ChangeDetectionStrategy.OnPush
      })
      export class ButtonComponent {
        @Input() label: string = '';
      }
      ```
  - **`pipes/`**:
    - Chứa standalone pipes:
      - `currency.pipe.ts`: Định dạng tiền tệ (VND).
      - `date-format.pipe.ts`: Định dạng ngày giờ.
  - **`directives/`**:
    - Chứa standalone directives:
      - `highlight.directive.ts`: Tô sáng element.
      - `tooltip.directive.ts`: Hiển thị tooltip.
- **Lưu ý**:
  - Không đặt DTO/View Models trùng lặp trong `shared/models/` và `features/<feature>/models/`.
  - Components phải sử dụng **scoped styles** (`*.component.scss`).

### 5. `src/app/features/` - Các feature của ứng dụng (Tầng Infrastructure)
- **Mục đích**: Chứa các feature cụ thể (`cashier`, `warehouse`, `ecommerce`, `settings`) với components, services, DTO, View Models.
- **Quy tắc**:
  - Mỗi feature lớn (`cashier`, `warehouse`, `ecommerce`) chứa:
    - **Sub-features** (như `create-order`, `inventory-check`, `sync-orders`).
    - Thư mục `services/` dùng chung trong feature.
    - File `*.routes.ts` để lazy-load sub-features.
  - Mỗi feature nhỏ (`settings`) chứa:
    - Một component chính, service (nếu cần), models (nếu cần).
    - File `*.routes.ts` để lazy-load.
  - Services trong `features/<feature>/<sub-feature>/services/` hoặc `features/<feature>/services/`:
    - Chỉ gọi use cases từ `application/use-cases/` hoặc adapters từ `core/services/`.
    - Quản lý state bằng `BehaviorSubject` hoặc `Signal` (nếu dùng Angular Signals).
    - Không chứa business logic (như tính toán giá, kiểm tra tồn kho).
  - Components phải là **standalone**, sử dụng **OnPush Change Detection**, và chỉ import modules cần thiết.
  - DTO trong `models/api/`, View Models trong `models/view/`, chỉ dùng trong sub-feature trừ khi tái sử dụng (chuyển sang `shared/models/`).
  - Mỗi sub-feature có file `index.ts` để export components, services, models.
- **Thư mục con**:
  - **Feature lớn**:
    - **`cashier/`**:
      - Sub-features:
        - `create-order/`:
          - `components/`:
            - `create-order.component.ts`: Page tạo đơn hàng.
            - `cart-item/`:
              - `cart-item.component.ts`, `cart-item.component.html`, `cart-item.component.scss`.
            - `payment-modal/`:
              - `payment-modal.component.ts`, `payment-modal.component.html`, `payment-modal.component.scss`.
          - `services/`:
            - `create-order.service.ts`: Gọi `create-order.usecase.ts`.
          - `models/`:
            - `api/create-order.dto.ts`.
            - `view/create-order-view.model.ts`.
          - `create-order.routes.ts`: Lazy-load sub-feature.
          - `index.ts`: Export components, services, models.
        - `order-history/`:
          - `components/`:
            - `order-history.component.ts`.
            - `order-list/`:
              - `order-list.component.ts`, `order-list.component.html`, `order-list.component.scss`.
          - `services/`:
            - `order-history.service.ts`.
          - `models/`:
            - `api/order-history.dto.ts`.
            - `view/order-history-view.model.ts`.
          - `order-history.routes.ts`.
          - `index.ts`.
      - `services/`:
        - `cashier.service.ts`: Logic chung cho `create-order` và `order-history`.
      - `cashier.routes.ts`: Lazy-load sub-features.
    - **`warehouse/`**:
      - Sub-features:
        - `inventory-check/`:
          - `components/`:
            - `inventory-check.component.ts`: Page kiểm kho.
            - `inventory-check-list/`:
              - `inventory-check-list.component.ts`, `inventory-check-list.component.html`, `inventory-check-list.component.scss`.
            - `confirm-modal/`:
              - `confirm-modal.component.ts`, `confirm-modal.component.html`, `confirm-modal.component.scss`.
          - `services/`:
            - `inventory-check.service.ts`: Gọi `inventory-check.usecase.ts`.
          - `models/`:
            - `api/inventory-check.dto.ts`.
            - `view/inventory-check-view.model.ts`.
          - `inventory-check.routes.ts`.
          - `index.ts`.
        - `stock-adjustment/`:
          - `components/`:
            - `stock-adjustment.component.ts`.
          - `services/`:
            - `stock-adjustment.service.ts`.
          - `models/`:
            - `api/stock-adjustment.dto.ts`.
            - `view/stock-adjustment-view.model.ts`.
          - `stock-adjustment.routes.ts`.
          - `index.ts`.
      - `services/`:
        - `warehouse.service.ts`: Logic chung.
      - `warehouse.routes.ts`.
    - **`ecommerce/`**:
      - Sub-features:
        - `sync-orders/`:
          - `components/`:
            - `sync-orders.component.ts`: Page đồng bộ đơn TMĐT.
            - `order-list/`:
              - `order-list.component.ts`, `order-list.component.html`, `order-list.component.scss`.
          - `services/`:
            - `sync-orders.service.ts`: Gọi `sync-order.usecase.ts` và `shopee-adapter.ts`.
          - `models/`:
            - `api/sync-orders.dto.ts`, `shopee-order.dto.ts`, `lazada-order.dto.ts`.
            - `view/sync-orders-view.model.ts`.
          - `sync-orders.routes.ts`.
          - `index.ts`.
        - `order-management/`:
          - `components/`:
            - `order-management.component.ts`.
          - `services/`:
            - `order-management.service.ts`.
          - `models/`:
            - `api/order-management.dto.ts`.
            - `view/order-management-view.model.ts`.
          - `order-management.routes.ts`.
          - `index.ts`.
      - `services/`:
        - `ecommerce.service.ts`: Logic chung.
      - `ecommerce.routes.ts`.
  - **Feature nhỏ**:
    - **`settings/`**:
      - `components/`:
        - `settings.component.ts`, `settings.component.html`, `settings.component.scss`.
      - `services/`:
        - `settings.service.ts` (nếu cần): Lưu cài đặt.
      - `models/`:
        - `api/settings.dto.ts` (nếu cần).
        - `view/settings-view.model.ts` (nếu cần).
      - `settings.routes.ts`.
      - `index.ts`.
- **Lưu ý**:
  - Services trong `features/<feature>/<sub-feature>/services/` không chứa business logic, chỉ gọi use cases hoặc adapters.
  - Components phụ (như `cart-item/`, `order-list/`) phải có thư mục riêng.
  - File `index.ts` export tất cả components, services, models của sub-feature:
    ```typescript
    export * from './components';
    export * from './services';
    export * from './models';
    ```

### 6. `src/app/` - Root component và routes
- **Mục đích**: Chứa root component và main routes của ứng dụng.
- **Quy tắc**:
  - `app.component.ts`: Root component, chứa layout chính (navbar, sidebar).
  - `app.component.html`: Template cho layout.
  - `app.component.scss`: Scoped styles cho layout.
  - `app.routes.ts`: Định nghĩa lazy-loaded routes cho các features (`cashier`, `warehouse`, `ecommerce`, `settings`).
  - Chuyển hướng mặc định đến `/cashier` trong `app.routes.ts`.
- **Ví dụ nội dung `app.routes.ts`**:
  ```typescript
  import { Routes } from '@angular/router';

  export const routes: Routes = [
    {
      path: '',
      redirectTo: 'cashier',
      pathMatch: 'full'
    },
    {
      path: 'cashier',
      loadChildren: () => import('./features/cashier/cashier.routes').then(m => m.routes)
    },
    {
      path: 'warehouse',
      loadChildren: () => import('./features/warehouse/warehouse.routes').then(m => m.routes)
    },
    {
      path: 'ecommerce',
      loadChildren: () => import('./features/ecommerce/ecommerce.routes').then(m => m.routes)
    },
    {
      path: 'settings',
      loadChildren: () => import('./features/settings/settings.routes').then(m => m.routes)
    }
  ];
  ```
- **Lưu ý**:
  - Không đặt logic kinh doanh trong `app.component.ts`.

### 7. `public/assets/` - Tài nguyên tĩnh
- **Mục đích**: Chứa hình ảnh, fonts dùng trong UI.
- **Quy tắc**:
  - `images/`: Chứa logo, hình sản phẩm.
  - `fonts/`: Chứa font tùy chỉnh.
  - Tham chiếu trong `styles.scss` hoặc `*.component.scss`.
- **Lưu ý**:
  - Đảm bảo đường dẫn tài nguyên đúng trong `angular.json`.

### 8. `src/styles/` - Styles toàn cục
- **Mục đích**: Chứa styles chung cho ứng dụng.
- **Quy tắc**:
  - `styles.scss`: Chứa reset CSS, biến SCSS, theme (như màu sắc, font).
  - Components sử dụng **scoped styles** trong `*.component.scss`, không phụ thuộc `styles.scss` trừ khi cần.
- **Lưu ý**:
  - Tránh CSS conflicts bằng cách sử dụng BEM hoặc CSS modules.

### 9. `src/main.ts` - Entry point
- **Mục đích**: Khởi động ứng dụng.
- **Quy tắc**:
  - Chỉ bootstrap `AppComponent`.
  - Không chứa logic kinh doanh.
- **Ví dụ nội dung `main.ts`**:
  ```typescript
  import { bootstrapApplication } from '@angular/platform-browser';
  import { AppComponent } from './app/app.component';
  import { provideRouter } from '@angular/router';
  import { routes } from './app/app.routes';

  bootstrapApplication(AppComponent, {
    providers: [provideRouter(routes)]
  }).catch(err => console.error(err));
  ```

- **Tích hợp TMĐT (Shopee, Lazada)**:
  - Sub-feature `ecommerce/sync-orders/`:
    - `services/sync-orders.service.ts`: Gọi `sync-order.usecase.ts` và adapters (`shopee-adapter.ts`, `lazada-adapter.ts`).
    - `models/api/shopee-order.dto.ts`, `lazada-order.dto.ts`: DTO cho đơn hàng TMĐT.
    - `models/view/sync-orders-view.model.ts`: View Model hiển thị danh sách đơn.
    - `components/order-list/`:
      - `order-list.component.ts`, `order-list.component.html`, `order-list.component.scss`.
  - Adapters trong `core/services/`:
    - `shopee-adapter.ts`: Gọi API Shopee.
    - `lazada-adapter.ts`: Gọi API Lazada.
  - Ví dụ nội dung `sync-orders.service.ts`:
    ```typescript
    import { Injectable } from '@angular/core';
    import { BehaviorSubject, Observable } from 'rxjs';
    import { SyncOrderUseCase } from '../../../../application/use-cases/ecommerce/sync-order.usecase';
    import { SyncOrdersViewModel } from '../models/view/sync-orders-view.model';

    @Injectable()
    export class SyncOrdersService {
      private ordersSubject = new BehaviorSubject<SyncOrdersViewModel[]>([]);
      orders$: Observable<SyncOrdersViewModel[]> = this.ordersSubject.asObservable();

      constructor(private syncOrderUseCase: SyncOrderUseCase) {}

      syncOrders(platform: 'shopee' | 'lazada'): Observable<SyncOrdersViewModel[]> {
        return this.syncOrderUseCase.execute(platform);
      }
    }
    ```

## Quy tắc tìm và đặt file
- **Entities**:
  - Tìm trong `src/domain/entities/` (như `product.ts`, `size.ts`).
  - Đặt mới trong `src/domain/entities/<entity>.ts`.
- **Business Rules**:
  - Tìm trong `src/domain/rules/` (như `inventory-rules.ts`).
  - Đặt mới trong `src/domain/rules/<rule>.ts`.
- **Use Cases**:
  - Tìm trong `src/application/use-cases/<feature>/` (như `inventory-check.usecase.ts`).
  - Đặt mới trong `src/application/use-cases/<feature>/<use-case>.usecase.ts`.
- **Adapters**:
  - Tìm trong `src/app/core/services/` (như `shopee-adapter.ts`).
  - Đặt mới trong `src/app/core/services/<adapter>.ts`.
- **DTO**:
  - Tìm trong:
    - `src/app/shared/models/api/` nếu dùng chung (như `product.dto.ts`).
    - `src/app/features/<feature>/<sub-feature>/models/api/` nếu cụ thể (như `inventory-check.dto.ts`).
  - Đặt mới tương ứng.
- **View Models**:
  - Tìm trong:
    - `src/app/shared/models/view/` nếu dùng chung (như `product-view.model.ts`).
    - `src/app/features/<feature>/<sub-feature>/models/view/` nếu cụ thể (như `inventory-check-view.model.ts`).
  - Đặt mới tương ứng.
- **Services**:
  - Tìm trong:
    - `src/app/core/services/` nếu toàn cục (như `http.service.ts`).
    - `src/app/features/<feature>/services/` nếu dùng chung trong feature (như `cashier.service.ts`).
    - `src/app/features/<feature>/<sub-feature>/services/` nếu cụ thể (như `inventory-check.service.ts`).
  - Đặt mới tương ứng.
- **Components**:
  - Tìm trong:
    - `src/app/shared/components/` nếu dùng chung (như `button/`).
    - `src/app/features/<feature>/<sub-feature>/components/` nếu cụ thể (như `inventory-check.component.ts`).
  - Đặt mới trong thư mục tương ứng, mỗi component có `*.component.ts`, `*.component.html`, `*.component.scss`.
- **Routes**:
  - Tìm trong:
    - `src/app/app.routes.ts` cho main routes.
    - `src/app/features/<feature>/<sub-feature>/*.routes.ts` cho sub-features.
    - `src/app/features/<feature>/*.routes.ts` cho feature lớn.
  - Đặt mới tương ứng, sử dụng `loadComponent` hoặc `loadChildren`.

## Quy tắc tối ưu hóa
- **Hiệu suất**:
  - Sử dụng **lazy loading** cho tất cả features và sub-features qua `*.routes.ts`.
  - Components sử dụng **ChangeDetectionStrategy.OnPush**.
  - Tránh import không cần thiết trong components (chỉ `CommonModule`, `FormsModule` nếu cần).
- **Bảo trì**:
  - Mỗi sub-feature có file `index.ts` để export components, services, models.
  - DTO/View Models không trùng lặp giữa `shared/` và `features/`.
  - Services không chứa business logic, chỉ gọi use cases hoặc adapters.
- **Ngành hàng**:
  - Tạo sub-features như `fashion-store/`, `food-store/` trong `features/<feature>/<sub-feature>/`.
  - Sử dụng `size.ts` (Thời trang) và `expiration-date.ts` (Đồ ăn) từ `domain/entities/`.
- **TMĐT**:
  - Sub-feature `sync-orders/` gọi `shopee-adapter.ts` và `lazada-adapter.ts` qua `sync-order.usecase.ts`.
  - DTO cụ thể cho Shopee (`shopee-order.dto.ts`) và Lazada (`lazada-order.dto.ts`).

## Ví dụ cách đặt file mới
- Nếu cần tạo service cho kiểm kho:
  - Đặt trong `src/app/features/warehouse/inventory-check/services/inventory-check.service.ts`.
  - Service chỉ gọi `inventory-check.usecase.ts` và `inventory-check-adapter.ts`.
  - Sử dụng `InventoryCheckViewModel` từ `src/app/features/warehouse/inventory-check/models/view/inventory-check-view.model.ts`.
  - Ví dụ:
    ```typescript
    import { Injectable } from '@angular/core';
    import { BehaviorSubject, Observable } from 'rxjs';
    import { InventoryCheckUseCase } from '../../../../../application/use-cases/warehouse/inventory-check.usecase';
    import { InventoryCheckViewModel } from '../models/view/inventory-check-view.model';
    import { InventoryCheckDto } from '../models/api/inventory-check.dto';

    @Injectable()
    export class InventoryCheckService {
      private inventoryCheckSubject = new BehaviorSubject<InventoryCheckViewModel | null>(null);
      inventoryCheck$: Observable<InventoryCheckViewModel | null> = this.inventoryCheckSubject.asObservable();

      constructor(private inventoryCheckUseCase: InventoryCheckUseCase) {}

      saveInventoryCheck(data: InventoryCheckViewModel): Observable<InventoryCheckDto> {
        return this.inventoryCheckUseCase.execute(data);
      }
    }
    ```

## Xử lý lỗi thường gặp
- **Service chứa business logic**:
  - Di chuyển logic tính toán sang `src/domain/rules/` (như `inventory-rules.ts`).
  - Di chuyển logic nghiệp vụ sang `src/application/use-cases/` (như `inventory-check.usecase.ts`).
- **DTO/View Model trùng lặp**:
  - Kiểm tra `src/app/shared/models/` trước khi tạo DTO/View Model mới trong `features/`.
- **Component không standalone**:
  - Thêm `standalone: true` và import modules cần thiết (`CommonModule`, `FormsModule`).
- **Sai vị trí file**:
  - Kiểm tra [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/FOLDER_STRUCTURE.md) và đặt file đúng thư mục (ví dụ: DTO trong `models/api/`, không phải `models/view/`).






