import { Injectable } from '@angular/core';
import { HttpContext } from '@angular/common/http';
import { PosInitUserData } from 'salehub_shared_contracts';
import { wait } from '@shared/utils';
import { AppLoaderService } from '../services/app_loader.service';
import { HttpService } from '../services/http.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { InitService } from '../services/init.service';
import { PageMessageService } from '../services/page_message.service';

@Injectable({
  providedIn: 'root'
})
export class AuthInitializer {
  constructor(
    private http: HttpService,
    private appLoader: AppLoaderService,
    private initService: InitService,
    private pageMsgService: PageMessageService
  ) {}

  async initialize(): Promise<void> {
    try {
      const user: PosInitUserData = await this.http.promisify(
        this.http.get('public', 'user_info', {
          isRelativeUrlEndpoint: true,
          context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
            hideError: true
          })
        })
      );

      this.initService.init(user);
    } catch(e: any) {
      console.log(e);
      if(e.status !== 403 && e.status !== 401) {
        this.pageMsgService.error(`Lỗi ${e.message} khi khởi tạo app. Tự động sửa trong 5s`);
        await wait(5000);
        return this.initialize();
      }
    }

    this.appLoader.loadingOff();
    this.appLoader.removeLoader();
  }
}

export function initializeAuth(authInitializer: AuthInitializer) {
  return authInitializer.initialize();
}

// export function initializeApp(authService: AuthService) {
//   return (): Promise<any> => authService.init();
// }
