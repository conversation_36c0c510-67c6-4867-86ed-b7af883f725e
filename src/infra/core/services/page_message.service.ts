import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class PageMessageService {
  constructor(private toast: ToastrService) {}

  msg(msg: string, type: 'error' | 'success' | 'info', opts?: Opts) {
    this.toast[type](msg, undefined, {
      toastClass: 'ngx-toastr toastr-alert',
      positionClass: 'toast-bottom-left',
      timeOut: opts?.timeout ?? 10000,
      progressBar: opts?.progressBar === undefined ? true : opts?.progressBar,
      progressAnimation: 'increasing',
      tapToDismiss: opts?.tapToDismiss === undefined ? true : opts.tapToDismiss,
      disableTimeOut: opts?.disableTimeOut,
      enableHtml: true
    });
  }

  error(msg: string, opts?: Opts) {
    this.msg(msg, 'error', opts);
  }

  success(msg: string, opts?: Opts) {
    this.msg(msg, 'success', opts);
  }
}

type Opts = {
  tapToDismiss?: boolean,
  progressBar?: boolean,
  timeout?: number,
  disableTimeOut?: boolean
};
