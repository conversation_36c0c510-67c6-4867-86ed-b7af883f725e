import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { NotificationBaseMessage } from 'salehub_shared_contracts';
import { AppCommonService } from './common.service';
/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class InAppNotificationService {
  constructor(
    private toast: ToastrService,
    private commonService: AppCommonService
  ) {}

  show(msg: NotificationBaseMessage, opts?: Opts) {
    const notification = this.toast.show(
      `<div class="content">
        <div class="title">
          ${msg.notification?.title}
        </div>
        <div class="body mt-1">${msg.notification?.body}</div>
      </div>`,
      undefined,
      {
        toastClass: 'ngx-toastr toastr-notification',
        positionClass: 'toast-top-center',
        // timeOut: opts?.timeout ?? 10000,
        timeOut: 1200000,
        closeButton: false,
        tapToDismiss: true,
        enableHtml: true,
        disableTimeOut: true
      }
    );

    // this.animate(notification.portal.location.nativeElement);

    // eslint-disable-next-line
    if(msg.data?.['inAppRouter']) {
      notification.onTap.subscribe(() => {
        console.log('Notification tapped');
        // eslint-disable-next-line
        this.commonService.redirectInAppRouter(msg.data?.['inAppRouter'] as any);
      });
    }

    this.commonService.playSound('notification');
  }

  animate(el: HTMLElement) {
    // console.log(el);
    // // eslint-disable-next-line
    // const reqAnimationFrame = (function () {
    //   // @ts-ignore
	  //   return window[Hammer.prefixed(window, 'requestAnimationFrame')] || function (callback) {
	  //       window.setTimeout(callback, 1000 / 60);
	  //   };
    // })();

    // let ticking = false;
    // let transform: any;

    // const mc = new Hammer.Manager(el);

    // const START_X = el.offsetLeft;
    // const START_Y = el.offsetTop;

    // mc.add(new Hammer.Pan({ threshold: 0, pointers: 0 }));

    // mc.add(new Hammer.Rotate({ threshold: 0 })).recognizeWith(mc.get('pan'));
    // mc.add(new Hammer.Pinch({ threshold: 0 })).recognizeWith([mc.get('pan'), mc.get('rotate')]);
    // mc.on('panstart panmove', onPan);

    // function resetElement() {
	  //   // el.className = 'animate';
	  //   transform = {
	  //       translate: { x: START_X, y: START_Y },
	  //       scale: 1,
	  //       angle: 0,
	  //       rx: 0,
	  //       ry: 0,
	  //       rz: 0
	  //   };
	  //   requestElementUpdate();
    // }

    // function updateElementTransform() {
	  //   let value = [
	  //       `translate3d(${transform.translate.x}px, ${transform.translate.y}px, 0)`,
	  //       `scale(${transform.scale}, ${transform.scale})`,
	  //       `rotate3d(${transform.rx},${transform.ry},${transform.rz},${transform.angle}deg)`
	  //   ];

    //   // @ts-ignore
	  //   value = value.join(' ');
    //   // @ts-ignore
	  //   el.style.webkitTransform = value;
    //   // @ts-ignore
	  //   el.style.mozTransform = value;
    //   // @ts-ignore
	  //   el.style.transform = value;
	  //   ticking = false;

    //   console.log('update', transform.translate.x, transform.translate.y);
    // }

    // function requestElementUpdate() {
	  //   if(!ticking) {
	  //       reqAnimationFrame(updateElementTransform);
	  //       ticking = true;
	  //   }
    // }

    // function onPan(ev: any) {
    //   let y = START_Y;
    //   if(ev.deltaY < 0) {
    //     y += ev.deltaY;
    //   }
	  //   transform.translate = {
	  //       x: START_X + ev.deltaX,
	  //       y
	  //   };

	  //   requestElementUpdate();
    // }

    // resetElement();
  }
}

type Opts = {
  tapToDismiss?: boolean,
  progressBar?: boolean,
  timeout?: number,
  disableTimeOut?: boolean
};
