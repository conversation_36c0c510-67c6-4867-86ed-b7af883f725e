import { Injectable, inject } from '@angular/core';
import { Observable, from } from 'rxjs';
import { ConfirmModalService } from '@shared/modals/common/confirm-modal';

/**
 * Interface cho dữ liệu xác nhận
 */
export interface ConfirmationData {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  confirmColor?: 'primary' | 'accent' | 'warn';
}

/**
 * Service để hiển thị hộp thoại xác nhận
 */
@Injectable({
  providedIn: 'root'
})
export class ConfirmationService {
  private confirmModalService = inject(ConfirmModalService);

  /**
   * Hiển thị hộp thoại xác nhận
   * @param data Dữ liệu xác nhận
   * @returns Observable<boolean> - true nếu người dùng xác nhận, false nếu hủy
   */
  confirm(data: ConfirmationData): Observable<boolean> {
    // Chuyển đổi Promise thành Observable để giữ nguyên API
    return from(this.confirmModalService.confirm({
      title: data.title,
      message: data.message,
      confirmText: data.confirmText || 'CONFIRM.YES',
      cancelText: data.cancelText || 'CONFIRM.NO',
      confirmColor: data.confirmColor || 'primary'
    }));
  }
}
