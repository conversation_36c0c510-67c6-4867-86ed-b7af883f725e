import { Injectable } from '@angular/core';
import { AppChannelMsg, FlutterCommunicateMsg, InAppRouter, APP_CONST } from 'salehub_shared_contracts';
import { HttpContext } from '@angular/common/http';
import { HttpService } from './http.service';
import { AppCommonService } from './common.service';
import { InAppNotificationService } from './in_app_notification.service';
import { PageMessageService } from './page_message.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { AppChannelService } from './app_channel.service';
import { WebServiceWorker } from './sw.service';

@Injectable({
  providedIn: 'root'
})
export class AppAutoUpdateService {
  constructor(
      private webServiceWorker: WebServiceWorker,
      private appChannel: AppChannelService
  ) {}

  checkIfAppUpdated() {
    this.webServiceWorker
      .$isAnyNewUpdateAvailable
      .subscribe((versionAvailableFlag: boolean) => {
        if(versionAvailableFlag) {
          this.appChannel.updateApp();
        }
      });
  }
}
