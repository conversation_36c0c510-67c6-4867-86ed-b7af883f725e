import { inject, Injectable } from '@angular/core';
import { ResolveFn, ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';
import { HttpContext } from '@angular/common/http';
import { HttpService, REQUEST_PERMISSION } from './http.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';

const resolver: ResolveFn<Object> = (route, state) => {
  const http = inject(HttpService);
  let url = '';
  // eslint-disable-next-line
  let queries: string[] = route.data['resolverRequestQuery'] ?? [];
  queries.forEach(q => {
    url += `&q[]=${q}`;
  });

  Object.keys(route.queryParams).forEach(k => {
    url += `&${k}=${route.queryParams[k]}`;
  });
  Object.keys(route.params).forEach(k => {
    url += `&${k}=${route.params[k]}`;
  });

  // eslint-disable-next-line dot-notation
  const permission: REQUEST_PERMISSION = route.data['resolverPermission'] || 'public';

  return http.get(permission, url, {
    context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
      useLoader: true
    })
  });
};

export const RouterResolverService = {
  resolverResponseData: resolver
};

@Injectable({
  providedIn: 'root'
})

export class RouterDataService {
  constructor(private activatedRoute: ActivatedRoute) { }

  getData() {
    return this.activatedRoute.data
      .pipe(
        map(res =>
          // @ts-ignore
          res.resolverResponseData
        )
      );
  }
}
