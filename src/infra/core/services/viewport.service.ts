import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Observable, takeUntil } from 'rxjs';
import { Platform } from '@angular/cdk/platform';

export interface ViewportInfo {
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  isDesktop: boolean
}

@Injectable({
  providedIn: 'root'
})
export class ViewportService implements OnDestroy {
  // BehaviorSubject để emit thông tin viewport
  private viewportSubject: BehaviorSubject<ViewportInfo> = new BehaviorSubject(null as any);

  // Observable để components có thể subscribe
  public viewport$!: Observable<ViewportInfo>;

  // Lưu trữ handler để cleanup
  private resizeHandler: () => void;

  constructor(
    private platform: Platform
  ) {
    // Khởi tạo handler
    this.resizeHandler = () => this.updateViewportInfo();

    // Đăng ký lắng nghe sự kiện resize
    window.addEventListener('resize', this.resizeHandler);

    // Đăng ký lắng nghe sự kiện orientation change cho mobile
    window.addEventListener('orientationchange', this.resizeHandler);

    // Cập nhật thông tin viewport ban đầu
    this.updateViewportInfo();
    this.viewport$ = this.viewportSubject.asObservable();
  }

  // Lấy orientation hiện tại
  private getOrientation(): 'portrait' | 'landscape' {
    return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
  }

  private getViewportInfo(): ViewportInfo {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      orientation: this.getOrientation(),
      isDesktop: window.innerWidth >= 1024 && this.getOrientation() !== 'portrait'
    }
  }

  // Cập nhật thông tin viewport
  private updateViewportInfo(): void {
    this.viewportSubject.next(this.getViewportInfo());
  }

  public observeViewport(destroy$: Observable<void>): Observable<ViewportInfo> {
    return this.viewportSubject.asObservable().pipe(
      takeUntil(destroy$)
    );
  }

  // Lấy thông tin viewport hiện tại
  public getCurrentViewport(): ViewportInfo {
    return this.viewportSubject.value;
  }

  public isMobile() {
    return !!this.platform.IOS || !!this.platform.ANDROID;
  }

  // Cleanup khi service bị destroy
  ngOnDestroy(): void {
    window.removeEventListener('resize', this.resizeHandler);
    window.removeEventListener('orientationchange', this.resizeHandler);
  }
}
