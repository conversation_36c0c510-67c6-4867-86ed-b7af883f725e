import { Injectable, NgZone } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { InAppRouter, NotificationBaseMessage } from 'salehub_shared_contracts';
import { scrollTop } from '@shared/utils';

/**
 * để tránh bị Circular dependency
 * các hàm ko inject service khác
 * thì cho vào đây
 */
@Injectable({
  providedIn: 'root'
})

export class AppCommonService {
  constructor(
    private router : Router,
    private zone: NgZone
  ) {}

  hasFlutterAppCommunicate() {
    // return (window as any)[this._channelName] && !!(window as any)[this._channelName].postMessage;
    return !!(window as any).flutter_inappwebview?.callHandler;
  }

  navigate(commands: any[], extras?: NavigationExtras) {
    return this.router.navigate(commands, extras)
      .then(() => scrollTop());
  }

  redirectInAppRouter(router: InAppRouter) {
    console.log('redirectInAppRouter');
    console.log(router);

    // fix loi
    // Navigation triggered outside Angular zone, did you forget to call 'ngZone.run()'?
    // khi goi từ app xuống
    this.zone.run(() => {
      this.navigate([router.path], {
        queryParams: (router as any).queryParams ? (router as any).queryParams : {}
      });
    });
  }

  playSound(type: 'notification') {
    let src: string;
    switch(type) {
    case 'notification':
      src = 'assets/sounds/notification.mp3';
      break;
    }
    const audio = new Audio();
    audio.src = src;
    audio.load();
    audio.play().catch(e => console.error(e));
  }

  decodeMsg(msg: string) {
    return JSON.parse(atob(msg));
  }

  decodeNotificationData(msg: NotificationBaseMessage) {
    if(msg.data) {
      [
        'inAppRouter'
      ].forEach(key => {
        if(msg.data?.[key]) {
          msg.data[key] = this.decodeMsg(msg.data[key]);
        }
      });
    }

    return msg;
  }
}
