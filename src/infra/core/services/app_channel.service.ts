import { Injectable } from '@angular/core';
import { AppChannelMsg, FlutterCommunicateMsg, InAppRouter, APP_CONST } from 'salehub_shared_contracts';
import { HttpContext } from '@angular/common/http';
import { HttpService } from './http.service';
import { AppCommonService } from './common.service';
import { InAppNotificationService } from './in_app_notification.service';
import { PageMessageService } from './page_message.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';

@Injectable({
  providedIn: 'root'
})
export class AppChannelService {
  private _channelName = 'XOIMOC_CHAN';

  constructor(
    private pageMsg: PageMessageService,
    private http: HttpService,
    private commonService: AppCommonService,
    private inAppNotificationService: InAppNotificationService
  ) {}

  send(data: AppChannelMsg) {
    if(this.commonService.hasFlutterAppCommunicate()) {
      // (window as any)[this._channelName].postMessage(
      //   JSON.stringify(data)
      // );

      // @ts-ignore
      return window.flutter_inappwebview.callHandler(this._channelName, JSON.stringify(data));
    }

    this.pageMsg.error('Máy của bạn không hỗ trợ.');
  }

  launch(url: string, event: Event) {
    if(this.commonService.hasFlutterAppCommunicate()) {
      event.stopPropagation();
      event.preventDefault();

      this.send({
        type: 'launcher',
        data: {
          url
        }
      });
    }
  }

  async share(data: { text: string, title?: string, url?: string, files?: File[], base64Image?: string }) {
    if(navigator.share) {
      try {
        await navigator.share(data);
        return true;
      } catch(e: any) {
        this.pageMsg.error(`Lỗi khi chia sẻ: ${e.message}`);
        return false;
      }
    }

    if(this.commonService.hasFlutterAppCommunicate()) {
      delete data.files;

      if(data.base64Image) {
        data.base64Image = data.base64Image.replace('data:image/png;base64,', '');
      }

      this.send({
        type: 'share',
        data
      });
      return true;
    }

    this.pageMsg.error('App không hỗ trợ chức năng share.');
    return false;
  }

  async updateApp(version?: string) {
    if(!version) {
      return;
    }
    try {
      const varNameLastUpdateVersion = 'LAST_UPDATED_VERSION';
      const varNameLastUpdate = 'LAST_TIME_UPDATE_VERSION';

      const _update = () => {
        this.pageMsg.msg(`Tự động cập nhật lên phiên bản ${version}...`, 'info');

        localStorage.setItem(varNameLastUpdateVersion, version as string);

        if(this.commonService.hasFlutterAppCommunicate()) {
          this.send({
            type: 'updateApp',
            data: {
              version: version as string
            }
          });

          setTimeout(() => {
          // @ts-ignore
            window.location.reload(true);
          }, 3000);
        } else {
        // @ts-ignore
          window.location.reload(true);
        }
      };

      if(!version) {
        const result = (await this.http.promisify(
          this.http.get(
            'cashier',
            'pos_updates',
            {
              context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
                hideError: true
              })
            }
          )
        ))?.pos_updates;

        version = result.appVersion as string;
      }

      if(!version) {
        return;
      }

      // console.log(APP_VERSION, version);

      if(APP_CONST.APP_VERSION !== version) {
        const _lastUpdate = localStorage.getItem(varNameLastUpdate);
        const lastUpdateTime = _lastUpdate ? Number(_lastUpdate) : 0;
        const lastUpdatedVersion = localStorage.getItem(varNameLastUpdateVersion);

        if(lastUpdatedVersion !== version) {
          _update();
        } else if(lastUpdateTime < Date.now() - 30 * 60000) {
          localStorage.setItem(varNameLastUpdate, String(Date.now()));
          _update();
        } else {
          this.pageMsg.error(`Tự động cập nhật lên phiên bản ${version} không thành công. Vui lòng liên hệ admin.`);
        }
      }
    } catch(e: any) {
      this.pageMsg.error(`Lỗi khi tự động cập nhật phiên bản: ${e.message}`);
    }
  }

  decodeMsg(msg: string) {
    return JSON.parse(atob(msg));
  }

  receive(msg: string) {
    try {
      const data: FlutterCommunicateMsg = JSON.parse(msg);
      switch(data.type) {
      case 'error':
        this.pageMsg.error(data.message);
        break;

      case 'openInAppRouter':
        this.commonService.redirectInAppRouter(this.decodeMsg(data.message) as InAppRouter);
        break;

      case 'inAppNotification':
        // console.log('received inAppNotification');
        // console.log(data.message);
        // this.inAppNotificationService.show(this.commonService.decodeNotificationData(data.message));
        break;
      }
    } catch(err: any) {
      this.pageMsg.error(`Lỗi khi lấy data từ app: ${err.message}`);
      this.pageMsg.error(msg);
    }
  }

  setCommunicateFunction() {
    // @ts-ignore
    if(window.setReceiveFlutterMsgHandler) {
      // @ts-ignore
      window.setReceiveFlutterMsgHandler(d => this.receive(d));
    }
  }
}
