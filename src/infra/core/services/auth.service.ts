import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { Observable, of, Subscription, switchMap } from 'rxjs';
import { HttpContext } from '@angular/common/http';
import { wait } from '@shared/utils';
import { PosInitUserData } from 'salehub_shared_contracts';
import { HttpService } from './http.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { InitDataStore } from '../store/init_data.store';
import { DeviceTokenService } from './device_token.service';
import { InitService } from './init.service';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  constructor(
    private router: Router,
    private http: HttpService,
    private initService: InitService,
    private initDataStore: InitDataStore,
    private deviceTokenService: DeviceTokenService
  ) {}

  isAuthenticated() {
    return !!this.initDataStore.getData()?.user?._id || !!this.initDataStore.getSelectedStore()?.token;
  }

  login(input: {
    email: string,
    password: string
  }) {
    return this.http.post<PosInitUserData>(
      'public',
      'login',
      input,
      {
        isRelativeUrlEndpoint: true,
        context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
          useLoader: true,
          hideError: true
        })
      }
    )
      .pipe(
        switchMap(async (user: PosInitUserData) => {
          await this.initService.init(user);
          return of(user);
        })
      );
  }

  async logout() {
    const savedToken = await this.deviceTokenService.getSavedToken();

    return this.http.post(
      'cashier',
      'logout',
      { device_token: savedToken?.token }
    )
      .pipe(switchMap(() => {
        this.initService.destroy();
        this.deviceTokenService.clearSavedToken();
        return of();
      }));
  }

  canActivateCashier(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    if(this.isAuthenticated()) {
      return true;
    }

    this.router.navigate(['login'], { queryParams: { redirect: state.url } });
    return false;
  }

  canActivateLogin(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    if(this.isAuthenticated()) {
    // eslint-disable-next-line dot-notation
      this.router.navigateByUrl(state.root.queryParams['redirect'] || '/');
      return false;
    }

    return true;
  }
}
