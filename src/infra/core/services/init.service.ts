import { Injectable } from '@angular/core';
import { PosInitData, PosInitUserData } from 'salehub_shared_contracts';
import { HttpContext } from '@angular/common/http';
import { HttpService } from './http.service';
import { InitDataStore } from '../store/init_data.store';
import { AppUpdateService } from './update.service';
import { SocketService } from './socket.service';
import { AppChannelService } from './app_channel.service';
import { AppCommonService } from './common.service';
import { DeviceTokenService } from './device_token.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';

@Injectable({
  providedIn: 'root'
})
export class InitService {
  constructor(
    private http: HttpService,
    private initStore: InitDataStore,
    private updateService: AppUpdateService,
    private socketService: SocketService,
    private appChannel: AppChannelService,
    private deviceTokenService: DeviceTokenService,
    private commonService: AppCommonService
  ) {}

  async init(user: PosInitUserData) {
    this.initStore.updateStore({
      storeId: user.stores?.[0]?.storeId,
      branchId: user.stores?.[0]?.branchIds?.[0],
      token: user.token
    });

    const data: PosInitData = (await this.http.promisify(
      this.http.get('cashier', 'init', {
        context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
          hideError: true
        })
      })
    ))?.init || {};

    this.initStore.updateInitData({
      user,
      ...data
    });

    this.updateService.updatePosData(data);

    // this.socketService.init('cashier', {
    //   storeId: user.stores?.[0]?.storeId,
    //   branchId: user.stores?.[0]?.branchIds?.[0],
    //   token: user.token
    // });
    this.updateService.init();
    this.deviceTokenService.getToken();

    if(
      data.store?.networkPrinter?.ip &&
      data.store?.networkPrinter?.port &&
      this.commonService.hasFlutterAppCommunicate()
    ) {
      // console.log('connect_printer', data.store.networkPrinter.ip, data.store.networkPrinter.port);
      this.appChannel.send({
        type: 'connect_printer',
        data: {
          ip: data.store.networkPrinter.ip,
          port: data.store.networkPrinter.port
        }
      });
    }
  }

  destroy() {
    this.initStore.updateInitData(null);
    this.updateService.destroy();
    this.socketService.destroy();
  }
}
