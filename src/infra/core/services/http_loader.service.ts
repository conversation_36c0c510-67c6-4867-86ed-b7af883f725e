import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

/**
 * https://www.angularjswiki.com/angular/progress-bar-in-angular-mat-progress-bar-examplematerial-design/
 * https://long2know.com/2017/01/angular2-http-interceptor-and-loading-indicator/
 */
@Injectable({
  providedIn: 'root'
})
export class HttpLoaderService {
  private _update = new BehaviorSubject<boolean>(false);

  public setLoader(value: boolean) {
    this._update.next(value);
  }

  getUpdates() {
    return this._update;
  }
}
