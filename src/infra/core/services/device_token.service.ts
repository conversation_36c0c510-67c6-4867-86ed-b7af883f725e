import { inject, Injectable, Injector } from '@angular/core';
import { getMessaging, getToken, isSupported } from 'firebase/messaging';
import { Subscription, EMPTY } from 'rxjs';
import { Messaging } from '@angular/fire/messaging';
import { decryptData, encryptData, wait } from '@shared/utils';
import { DeviceToken } from 'salehub_shared_contracts';
import { HttpContext } from '@angular/common/http';
import { VAPID_KEY } from '@config/firebase.config';
import { HttpService } from './http.service';
import { AppChannelService } from './app_channel.service';
import { AppCommonService } from './common.service';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';

@Injectable({
  providedIn: 'root'
})
export class DeviceTokenService {
  // private readonly messaging = inject(Messaging, { optional: true });
  protected readonly token$ = EMPTY;
  protected readonly message$ = EMPTY;
  readonly #localStorageSavedTokenName = 'b0h2aceqfo';
  readonly #encryptPassword = 'fANm7SyDpZ1FyD2x6fFO2WbQzrPK95G8';
  private timeoutSaveToken!: ReturnType<typeof setTimeout> | undefined;

  constructor(
    private http: HttpService,
    private commonService: AppCommonService,
    private appChannelService: AppChannelService,
    private injector: Injector
  ) {
    isSupported().then((r) => {
      if (r) {
        // dhs fai inject moi chay dc
        const _analytics = injector.get(Messaging, null, { optional: true });
      }
    });
  }

  async getSavedToken(): Promise<SavedToken | null> {
    const rawToken = localStorage.getItem(this.#localStorageSavedTokenName);
    if(rawToken) {
      try {
        const data = await decryptData(rawToken, this.#encryptPassword);
        return JSON.parse(data);
      } catch(e) {
        return null;
      }
    }

    return null;
  }

  clearSavedToken() {
    localStorage.removeItem(this.#localStorageSavedTokenName);
  }

  async #getBrowserToken(): Promise<DeviceToken> {
    return new Promise((resolve, reject) => {
      const func = async () => {
        const serviceWorkerRegistration = await navigator?.serviceWorker?.getRegistration?.();

        if(serviceWorkerRegistration) {
          const messaging = getMessaging();

          /**
         * hàm getToken nó tự show allow block trên uiui
         * nhung tren ios fai request permission truoc khi getToken
         *
         * neu ko se ra loi
         * Notification prompting can only be done from a user gesture.
         */
          await Notification.requestPermission();

          const token = await getToken(
            messaging,
            {
              /**
           * https://console.firebase.google.com/project/nodex-erp/settings/cloudmessaging?hl=vi
           * -> web push certificates
           */
              vapidKey: VAPID_KEY,
              /**
             * fix lỗi path của firebase-messaging-sw.js
             */
              serviceWorkerRegistration
            }
          );

          document.removeEventListener('click', func);
          document.removeEventListener('touchend', func);

          return resolve({
            token,
            type: 'browser',
            userAgent: navigator.userAgent
          });
        }

        return reject();
      };

      document.addEventListener('click', func);
      document.addEventListener('touchend', func);
    });
  }

  async getToken() {
    if(this.timeoutSaveToken) {
      clearTimeout(this.timeoutSaveToken);
    }

    try {
    // console.warn('get token');
      let token!: DeviceToken;
      if(this.commonService.hasFlutterAppCommunicate()) {
      // console.log('send flutter');

        const response = await this.appChannelService.send({ type: 'deviceToken' });
        if(response) {
          token = JSON.parse(response);
        }
      } else {
        token = await this.#getBrowserToken();
      }

      // console.warn('received toknes');
      // console.log(token?.token);

      if(token) {
        this.saveToken(token);
      }

      return token;
    } catch(e) {
      if(e) {
        console.error(e);
      }
    }

    return null;
  }

  async saveToken(token: DeviceToken): Promise<any> {
    if(!token?.token) {
      return;
    }

    const savedToken = await this.getSavedToken();

    if(savedToken?.token && savedToken.token !== token.token) {
      await this.removeToken(savedToken.token);
    }

    let lastActive = savedToken?.lastActive || 0;

    /**
     * 1 ngay send request 1 lan
     */
    const minTimeSendRequest = 24 * 60 * 60000;
    if(!lastActive || Date.now() - lastActive > minTimeSendRequest) {
      try {
        await this.http.promisify(
          this.http.post(
            'cashier',
            'pos_active_device_token',
            token,
            {
              context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
                useLoader: false,
                hideError: true
              })
            }
          )
        );

        const data = await encryptData(
          JSON.stringify({ token: token.token, lastActive: Date.now() }),
          this.#encryptPassword
        );
        localStorage.setItem(
          this.#localStorageSavedTokenName,
          data
        );

        lastActive = Date.now();
      } catch(e) {
        console.error('Error while save token:', e);
        setTimeout(() => this.saveToken(token), 30000);
        return;
      }
    }

    /**
     * phòng khi nhiều thằng nó bật app cả ngày ko tắt
     */
    const nextTimeSendRequest = lastActive + minTimeSendRequest;
    const remainTime = nextTimeSendRequest - Date.now();

    if(remainTime > 0) {
      this.timeoutSaveToken = setTimeout(() => this.saveToken(token), remainTime);
    }
  }

  async removeToken(token: string): Promise<any> {
    try {
      await this.http.promisify(
        this.http.post(
          'cashier',
          'pos_remove_device_token',
          {
            token
          },
          {
            context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
              useLoader: false,
              hideError: true
            })
          }
        )
      );
    } catch(e) {
      console.error('Error while remove token:', e);
      await wait(5000);
      return this.removeToken(token);
    }
  }
}

type SavedToken = {
  token: string;
  lastActive: number;
}
