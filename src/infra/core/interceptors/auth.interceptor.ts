import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { InitDataStore } from '../store/init_data.store';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const initDataStore = inject(InitDataStore); // Inject service trong function
  const data = initDataStore.getSelectedStore();

  // Thêm token vào headers nếu có
  const modifiedReq = data?.storeId
    ? req.clone({
      setHeaders: {
        'store-id': data.storeId,
        'branch-id': data.branchId,
        token: data.token
      }
    })
    : req;

  return next(modifiedReq);
};
