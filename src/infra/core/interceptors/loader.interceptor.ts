import { finalize } from 'rxjs';
import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { HTTP_REQUEST_OPTIONS } from '../tokens/http-context-token';
import { HttpLoaderService } from '../services/http_loader.service';

export const loaderInterceptor: HttpInterceptorFn = (req, next) => {
  const loaderService = inject(HttpLoaderService); // Inject service trong function
  const { useLoader } = req.context.get(HTTP_REQUEST_OPTIONS);

  if (useLoader) {
    loaderService.setLoader(true);
  }

  return next(req).pipe(
    finalize(() => {
      if (useLoader) {
        loaderService.setLoader(false);
      }
    })
  );
};
