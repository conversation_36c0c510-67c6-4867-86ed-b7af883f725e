import { Injectable } from '@angular/core';
import { mergeDeep } from '@shared/utils';
import { createNotifiableSignal } from '@shared/utils/signal.util';
import { PosUpdateData } from 'salehub_shared_contracts';

@Injectable({
  providedIn: 'root'
})
export class UpdateDataStore {
  #_data = createNotifiableSignal<PosUpdateData>({} as any);

  public updatePosData(newData: PosUpdateData) {
    this.#_data.mutate(data => mergeDeep(data, newData));
    return this.getData();
  }

  getData() {
    return this.#_data();
  }

  getDataSignal() {
    return this.#_data;
  }
}
