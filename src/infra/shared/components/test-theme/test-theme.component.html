<div class="container mt-5">
  <div class="row">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h4>Test Product Modifiers Bottom Sheet</h4>
        </div>
        <div class="card-body">
          <button class="btn btn-primary" (click)="openProductModifiersSheet()">
            Mở bottom sheet chọn thêm sản phẩm
          </button>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h4>Test Batch Dialog</h4>
        </div>
        <div class="card-body">
          <button class="btn btn-success" (click)="openBatchDialog()">
            Mở dialog lô hàng
          </button>
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-3">
    <div class="col-md-6">
      <input-place></input-place>
    </div>
  </div>

  <!-- <app-product-selection
    [list]="productSelectorConfig.list"
    [data]="productSelectorConfig.data"
    [warehouseList]="productSelectorConfig.warehouseList"
    [categoryList]="productSelectorConfig.categoryList"
    [brandList]="productSelectorConfig.brandList"
    (openProductForm)="openProductForm()"
  ></app-product-selection> -->

  <!-- Thêm button để test dialog vào vị trí thích hợp -->
  <div class="content-section">
    <h3>Test Dialogs</h3>
    <div class="button-row">
      <button mat-raised-button color="primary" (click)="openCategoryProductDialog()">
        Test Category Product Dialog
      </button>

      <!-- Các button test khác đã có sẵn -->
    </div>
  </div>

  <h2>Test AdditionalCostDialog</h2>
  <div class="button-row">
    <button mat-raised-button color="primary" (click)="openAdditionalCostDialog(false)">
      Thêm chi phí mới
    </button>
    <button mat-raised-button color="accent" (click)="openAdditionalCostDialog(true)">
      Sửa chi phí
    </button>
  </div>

  <h2>Test TaxDialog</h2>
  <div class="button-row">
    <button mat-raised-button color="primary" (click)="openTaxDialog(false)">
      Thêm thuế mới
    </button>
    <button mat-raised-button color="accent" (click)="openTaxDialog(true)">
      Sửa thuế
    </button>
  </div>

  <!-- Test SelectAdditionalCostsDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Select Additional Costs Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog chọn và tùy chỉnh chi phí phát sinh khi nhập kho</p>
        <button class="btn btn-primary" (click)="openSelectAdditionalCostsDialog()">
          Mở Dialog Chọn Chi Phí
        </button>
      </div>
    </div>
  </div>

  <!-- Test QualityCheckRejectDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Quality Check Reject Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog tạo hoặc chỉnh sửa sản phẩm bị từ chối trong kiểm tra chất lượng</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openQualityCheckRejectDialog(false)">
            Thêm sản phẩm bị từ chối
          </button>
          <button class="btn btn-warning" (click)="openQualityCheckRejectDialog(true)">
            Sửa sản phẩm bị từ chối
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test SimpleNoteDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Simple Note Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog thêm hoặc chỉnh sửa ghi chú đơn giản</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openSimpleNoteDialog()">
            Thêm ghi chú mới
          </button>
          <button class="btn btn-warning" (click)="openSimpleNoteDialog('Ghi chú hiện tại cần chỉnh sửa')">
            Sửa ghi chú
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test NoteModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Note Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thêm hoặc chỉnh sửa ghi chú với loại ghi chú</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openNoteModal()">
            Mở modal ghi chú
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test PromotionModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Promotion Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thêm hoặc chỉnh sửa khuyến mãi</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openPromotionModal()">
            Mở modal khuyến mãi
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test VariantFormModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Variant Form Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal tạo hoặc chỉnh sửa thuộc tính sản phẩm</p>
        <div class="d-flex gap-2">
          <button class="btn btn-primary" (click)="openVariantFormModal()">
            Mở modal thuộc tính sản phẩm
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Test ProductFilterDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Product Filter Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog lọc sản phẩm theo nhóm hàng và vị trí kho</p>
        <button class="btn btn-primary" (click)="openProductFilterDialog()">
          Mở dialog lọc sản phẩm
        </button>
      </div>
    </div>
  </div>

  <!-- Test SerialNumberDialog -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Serial Number Dialog</h3>
      </div>
      <div class="card-body">
        <p>Test dialog quản lý số serial của sản phẩm</p>
        <button class="btn btn-primary" (click)="openSerialNumberDialog()">
          Mở dialog quản lý serial
        </button>
      </div>
    </div>
  </div>

  <!-- Test ConfirmModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Confirm Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal xác nhận hành động</p>
        <button class="btn btn-primary" (click)="openConfirmDialog()">
          Mở modal xác nhận
        </button>
      </div>
    </div>
  </div>

  <!-- Test FieldPermissionModal -->
  <div class="col-md-6 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Field Permission Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal thiết lập quyền truy cập field với table responsive và radio buttons</p>
        <button class="btn btn-success" (click)="openFieldPermissionModal()">
          Mở modal thiết lập quyền field
        </button>
      </div>
    </div>
  </div>

  <!-- Test FieldPropertiesModal -->
  <div class="col-md-12 mb-10">
    <div class="card shadow-sm">
      <div class="card-header">
        <h3 class="card-title">Field Properties Modal</h3>
      </div>
      <div class="card-body">
        <p>Test modal chỉnh sửa thuộc tính field với các loại field khác nhau</p>
        <div class="row">
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('text')">
              Text Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('picklist')">
              Picklist Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('multi-picklist')">
              Multi-Picklist Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-primary w-100" (click)="openFieldPropertiesModal('textarea')">
              Textarea Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('number')">
              Number Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('currency')">
              Currency Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('decimal')">
              Decimal Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-secondary w-100" (click)="openFieldPropertiesModal('search')">
              Search Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('user')">
              User Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('upload-file')">
              Upload File Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('upload-image')">
              Upload Image Field
            </button>
          </div>
          <div class="col-md-3 mb-2">
            <button class="btn btn-info w-100" (click)="openFieldPropertiesModal('checkbox')">
              Checkbox Field
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Dynamic Layout Builder Test -->
  <div class="test-section">
    <div class="card">
      <div class="card-header">
        <h3>Dynamic Layout Builder</h3>
      </div>
      <div class="card-body">
        <p>Test Dynamic Layout Builder component</p>
        <app-dynamic-layout-builder></app-dynamic-layout-builder>
      </div>
    </div>
  </div>

  <!-- Field Filters Test -->
  <div class="test-section mt-4">
    <div class="card">
      <div class="card-header">
        <h3>Field Filters</h3>
      </div>
      <div class="card-body">
        <p>Test Field Filters component với các loại field khác nhau</p>
        <div class="row">
          <div class="col-md-6">
            <app-field-filters
              [fields]="mockFields"
              [showTitle]="true"
              [showClearAll]="true"
              (filterChange)="onFieldFilterChange($event)"
              (filtersApplied)="onFiltersApplied($event)">
            </app-field-filters>
          </div>
          <div class="col-md-6">
            <div class="alert alert-info">
              <h5>Hướng dẫn test:</h5>
              <ul class="mb-0">
                <li>Chọn checkbox để kích hoạt filter cho field</li>
                <li>Chọn operator phù hợp với loại field</li>
                <li>Nhập giá trị filter (nếu cần)</li>
                <li>Xem console để theo dõi events</li>
                <li>Sử dụng "Clear All" để xóa tất cả filters</li>
              </ul>
            </div>
            <div class="alert alert-warning">
              <h6>Các loại field được test:</h6>
              <small>
                Text, Email, Phone, Number, Currency, Date, Picklist, Multi-picklist, Checkbox, Textarea
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
