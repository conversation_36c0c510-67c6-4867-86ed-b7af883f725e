import { OperatorConfig } from './field-filter-view.model';

// Operators cho Date và DateTime fields
export const DATE_OPERATORS: OperatorConfig[] = [
  { value: 'age_in', labelKey: 'FIELD_FILTERS.OPERATORS.AGE_IN', requiresInput: true, inputType: 'time-unit' },
  { value: 'due_in', labelKey: 'FIELD_FILTERS.OPERATORS.DUE_IN', requiresInput: true, inputType: 'time-unit' },
  { value: 'previous', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS', requiresInput: true, inputType: 'time-unit' },
  { value: 'next', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT', requiresInput: true, inputType: 'time-unit' },
  { value: 'on', labelKey: 'FIELD_FILTERS.OPERATORS.ON', requiresInput: true, inputType: 'date' },
  { value: 'before', labelKey: 'FIELD_FILTERS.OPERATORS.BEFORE', requiresInput: true, inputType: 'date' },
  { value: 'after', labelKey: 'FIELD_FILTERS.OPERATORS.AFTER', requiresInput: true, inputType: 'date' },
  { value: 'between', labelKey: 'FIELD_FILTERS.OPERATORS.BETWEEN', requiresInput: true, inputType: 'range' },
  { value: 'not_between', labelKey: 'FIELD_FILTERS.OPERATORS.NOT_BETWEEN', requiresInput: true, inputType: 'range' },
  { value: 'today', labelKey: 'FIELD_FILTERS.OPERATORS.TODAY', requiresInput: false },
  { value: 'tomorrow', labelKey: 'FIELD_FILTERS.OPERATORS.TOMORROW', requiresInput: false },
  { value: 'till_yesterday', labelKey: 'FIELD_FILTERS.OPERATORS.TILL_YESTERDAY', requiresInput: false },
  { value: 'starting_tomorrow', labelKey: 'FIELD_FILTERS.OPERATORS.STARTING_TOMORROW', requiresInput: false },
  { value: 'yesterday', labelKey: 'FIELD_FILTERS.OPERATORS.YESTERDAY', requiresInput: false },
  { value: 'this_week', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_WEEK', requiresInput: false },
  { value: 'this_month', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_MONTH', requiresInput: false },
  { value: 'previous_week', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_WEEK', requiresInput: false },
  { value: 'previous_month', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_MONTH', requiresInput: false },
  { value: 'this_year', labelKey: 'FIELD_FILTERS.OPERATORS.THIS_YEAR', requiresInput: false },
  { value: 'current_fy', labelKey: 'FIELD_FILTERS.OPERATORS.CURRENT_FY', requiresInput: false },
  { value: 'current_fq', labelKey: 'FIELD_FILTERS.OPERATORS.CURRENT_FQ', requiresInput: false },
  { value: 'previous_year', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_YEAR', requiresInput: false },
  { value: 'previous_fy', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_FY', requiresInput: false },
  { value: 'previous_fq', labelKey: 'FIELD_FILTERS.OPERATORS.PREVIOUS_FQ', requiresInput: false },
  { value: 'next_year', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT_YEAR', requiresInput: false },
  { value: 'next_fq', labelKey: 'FIELD_FILTERS.OPERATORS.NEXT_FQ', requiresInput: false },
  { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
  { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
];

// Operators cho Picklist và MultiPicklist fields
export const PICKLIST_OPERATORS: OperatorConfig[] = [
  { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'picklist' },
  { value: 'is_not', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT', requiresInput: true, inputType: 'picklist' },
  { value: 'is_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_EMPTY', requiresInput: false },
  { value: 'is_not_empty', labelKey: 'FIELD_FILTERS.OPERATORS.IS_NOT_EMPTY', requiresInput: false }
];

// Operators cho Checkbox fields
export const CHECKBOX_OPERATORS: OperatorConfig[] = [
  { value: 'is', labelKey: 'FIELD_FILTERS.OPERATORS.IS', requiresInput: true, inputType: 'checkbox' }
];

// Time units cho date operators
export interface TimeUnitOption {
  value: 'days' | 'weeks' | 'months' | 'years';
  labelKey: string;
}

export const TIME_UNITS: TimeUnitOption[] = [
  { value: 'days', labelKey: 'FIELD_FILTERS.TIME_UNITS.DAYS' },
  { value: 'weeks', labelKey: 'FIELD_FILTERS.TIME_UNITS.WEEKS' },
  { value: 'months', labelKey: 'FIELD_FILTERS.TIME_UNITS.MONTHS' },
  { value: 'years', labelKey: 'FIELD_FILTERS.TIME_UNITS.YEARS' }
];

// Checkbox values
export interface CheckboxValueOption {
  value: 'selected' | 'not_selected';
  labelKey: string;
}

export const CHECKBOX_VALUES: CheckboxValueOption[] = [
  { value: 'selected', labelKey: 'FIELD_FILTERS.CHECKBOX_VALUES.SELECTED' },
  { value: 'not_selected', labelKey: 'FIELD_FILTERS.CHECKBOX_VALUES.NOT_SELECTED' }
];
