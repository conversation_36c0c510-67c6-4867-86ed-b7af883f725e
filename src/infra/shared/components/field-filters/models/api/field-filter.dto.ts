// DTO cho API request/response của field filters

export interface FieldFilterDto {
  fieldId: number;
  operator: string;
  value?: string | number | boolean;
  minValue?: string | number;
  maxValue?: string | number;
  values?: string[]; // cho picklist
  timeValue?: number; // cho date operators
  timeUnit?: 'days' | 'weeks' | 'months' | 'years';
}

export interface FieldFiltersRequestDto {
  filters: FieldFilterDto[];
}

export interface FieldFiltersResponseDto {
  success: boolean;
  data?: unknown;
  message?: string;
}
