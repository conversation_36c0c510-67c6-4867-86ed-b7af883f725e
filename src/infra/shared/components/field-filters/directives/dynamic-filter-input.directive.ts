import {
  Directive,
  Input,
  Output,
  EventEmitter,
  ViewContainerRef,
  ComponentRef,
  OnInit,
  OnDestroy,
  OnChanges,
  SimpleChanges,
  Type
} from '@angular/core';

import { Field } from '@domain/entities/field.entity';
import { 
  BaseFilterInput,
  FilterInputConfig,
  FilterInputChangeEvent,
  FilterInputComponentMapping 
} from '../input-components/base-filter-input.interface';

// Import specialized input components
import { TextFilterInputComponent } from '../input-components/text-filter-input/text-filter-input.component';
import { NumberFilterInputComponent } from '../input-components/number-filter-input/number-filter-input.component';
import { DateFilterInputComponent } from '../input-components/date-filter-input/date-filter-input.component';
import { PicklistFilterInputComponent } from '../input-components/picklist-filter-input/picklist-filter-input.component';
import { CheckboxFilterInputComponent } from '../input-components/checkbox-filter-input/checkbox-filter-input.component';

/**
 * Dynamic directive để render appropriate input component dựa trên field type và operator
 * Sử dụng <PERSON>'s dynamic component loading với ViewContainerRef
 */
@Directive({
  selector: '[appDynamicFilterInput]',
  standalone: true
})
export class DynamicFilterInputDirective implements OnInit, OnDestroy, OnChanges {
  @Input() field!: Field;
  @Input() operator!: string;
  @Input() value?: any;
  @Input() disabled = false;

  @Output() valueChange = new EventEmitter<any>();
  @Output() validationChange = new EventEmitter<boolean>();
  @Output() filterChange = new EventEmitter<FilterInputChangeEvent>();

  private componentRef: ComponentRef<any> | null = null;

  // Component mapping dựa trên field type
  private readonly componentMapping: FilterInputComponentMapping = {
    // Text-based fields
    'text': TextFilterInputComponent,
    'email': TextFilterInputComponent,
    'phone': TextFilterInputComponent,
    'url': TextFilterInputComponent,
    'textarea': TextFilterInputComponent,
    
    // Number-based fields
    'number': NumberFilterInputComponent,
    'decimal': NumberFilterInputComponent,
    'currency': NumberFilterInputComponent,
    'percent': NumberFilterInputComponent,
    
    // Date-based fields
    'date': DateFilterInputComponent,
    'datetime': DateFilterInputComponent,
    
    // Picklist fields
    'picklist': PicklistFilterInputComponent,
    'multi-picklist': PicklistFilterInputComponent,
    
    // Checkbox fields
    'checkbox': CheckboxFilterInputComponent
  };

  constructor(private viewContainerRef: ViewContainerRef) {}

  ngOnInit(): void {
    this.createComponent();
  }

  ngOnDestroy(): void {
    this.destroyComponent();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['field'] || changes['operator']) {
      // Recreate component nếu field type hoặc operator thay đổi
      this.destroyComponent();
      this.createComponent();
    } else if (changes['value'] || changes['disabled']) {
      // Update existing component properties
      this.updateComponentInputs();
    }
  }

  /**
   * Tạo appropriate component dựa trên field type
   */
  private createComponent(): void {
    if (!this.field) {
      console.warn('DynamicFilterInputDirective: Field is required');
      return;
    }

    const componentClass = this.getComponentClass();
    if (!componentClass) {
      console.warn(`DynamicFilterInputDirective: No component found for field type: ${this.field.type}`);
      return;
    }

    try {
      // Clear existing component
      this.viewContainerRef.clear();

      // Create new component
      this.componentRef = this.viewContainerRef.createComponent(componentClass);

      // Set input properties
      this.updateComponentInputs();

      // Subscribe to output events
      this.subscribeToComponentEvents();

    } catch (error) {
      console.error('DynamicFilterInputDirective: Error creating component', error);
    }
  }

  /**
   * Destroy current component
   */
  private destroyComponent(): void {
    if (this.componentRef) {
      this.componentRef.destroy();
      this.componentRef = null;
    }
    this.viewContainerRef.clear();
  }

  /**
   * Update component input properties
   */
  private updateComponentInputs(): void {
    if (!this.componentRef) return;

    const instance = this.componentRef.instance as BaseFilterInput;
    
    // Set input properties
    instance.field = this.field;
    instance.operator = this.operator;
    instance.value = this.value;
    instance.disabled = this.disabled || false;

    // Trigger change detection
    this.componentRef.changeDetectorRef.detectChanges();
  }

  /**
   * Subscribe to component output events
   */
  private subscribeToComponentEvents(): void {
    if (!this.componentRef) return;

    const instance = this.componentRef.instance as BaseFilterInput;

    // Subscribe to valueChange
    if (instance.valueChange) {
      instance.valueChange.subscribe((value: any) => {
        this.valueChange.emit(value);
        this.emitFilterChange(value, instance.validate());
      });
    }

    // Subscribe to validationChange
    if (instance.validationChange) {
      instance.validationChange.subscribe((isValid: boolean) => {
        this.validationChange.emit(isValid);
        this.emitFilterChange(instance.value, isValid);
      });
    }
  }

  /**
   * Emit comprehensive filter change event
   */
  private emitFilterChange(value: any, isValid: boolean): void {
    const filterChangeEvent: FilterInputChangeEvent = {
      field: this.field,
      operator: this.operator,
      value: value,
      isValid: isValid
    };

    this.filterChange.emit(filterChangeEvent);
  }

  /**
   * Get appropriate component class dựa trên field type
   */
  private getComponentClass(): Type<any> | null {
    if (!this.field?.type) return null;

    const componentClass = this.componentMapping[this.field.type];
    
    if (!componentClass) {
      // Fallback to text component cho unknown types
      console.warn(`DynamicFilterInputDirective: Unknown field type '${this.field.type}', falling back to TextFilterInputComponent`);
      return TextFilterInputComponent;
    }

    return componentClass;
  }

  /**
   * Get current component instance
   */
  getCurrentComponent(): BaseFilterInput | null {
    return this.componentRef ? this.componentRef.instance as BaseFilterInput : null;
  }

  /**
   * Validate current component
   */
  validate(): boolean {
    const component = this.getCurrentComponent();
    return component ? component.validate() : true;
  }

  /**
   * Reset current component
   */
  reset(): void {
    const component = this.getCurrentComponent();
    if (component) {
      component.reset();
    }
  }

  /**
   * Get current filter value từ component
   */
  getCurrentValue(): any {
    const component = this.getCurrentComponent();
    return component ? component.value : undefined;
  }
}
