import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  OnInit, 
  OnDestroy,
  ChangeDetectionStrategy,
  signal,
  computed,
  effect
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { Field } from '@domain/entities/field.entity';
import { FieldFiltersService } from './services/field-filters.service';
import { FilterChangeEvent, FieldFilter } from './models/view/field-filter-view.model';
import { FilterFieldComponent } from './filter-field/filter-field.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-field-filters',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FilterFieldComponent
  ],
  templateUrl: './field-filters.component.html',
  styleUrls: ['./field-filters.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FieldFiltersComponent implements OnInit, OnDestroy {
  private subscriptions = new Subscription();

  // Input properties
  @Input() fields: Field[] = [];
  @Input() showTitle = true;
  @Input() showClearAll = true;

  // Output events
  @Output() filterChange = new EventEmitter<FilterChangeEvent>();
  @Output() filtersApplied = new EventEmitter<FieldFilter[]>();

  // Signals để quản lý state
  readonly isInitialized = signal(false);
  readonly filters = computed(() => this.fieldFiltersService.filtersState().filters);
  readonly activeFilters = computed(() => this.fieldFiltersService.activeFilters());
  readonly hasActiveFilters = computed(() => this.fieldFiltersService.hasActiveFilters());

  constructor(private fieldFiltersService: FieldFiltersService) {
    // Effect để emit events khi có thay đổi
    effect(() => {
      const activeFilters = this.activeFilters();
      if (this.isInitialized()) {
        this.filtersApplied.emit(activeFilters);
      }
    });
  }

  ngOnInit(): void {
    this.initializeFilters();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Khởi tạo filters từ danh sách fields
   */
  private initializeFilters(): void {
    if (this.fields && this.fields.length > 0) {
      this.fieldFiltersService.initializeFilters(this.fields);
      this.isInitialized.set(true);
    }
  }

  /**
   * Xử lý khi filter của một field thay đổi
   * @param event - Filter change event
   */
  onFilterChange(event: FilterChangeEvent): void {
    // Cập nhật service state
    this.fieldFiltersService.updateFilter(
      event.fieldId, 
      event.isActive, 
      event.filterValue
    );

    // Emit event ra ngoài
    this.filterChange.emit(event);
  }

  /**
   * Xóa tất cả filters
   */
  onClearAllFilters(): void {
    this.fieldFiltersService.clearAllFilters();
  }

  /**
   * Track function cho ngFor để tối ưu performance
   * @param index - Index của item
   * @param filter - FieldFilter object
   * @returns Unique identifier
   */
  trackByFieldId(index: number, filter: FieldFilter): number {
    return filter.field._id;
  }
}
