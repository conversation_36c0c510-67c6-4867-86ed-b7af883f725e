// Filter Field Component Styles

.filter-field-container {
  .field-header {
    .form-check {
      .form-check-input {
        &:checked {
          background-color: #007bff;
          border-color: #007bff;
        }
        
        &:focus {
          border-color: #80bdff;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
      }
      
      .form-check-label {
        color: #495057;
        cursor: pointer;
        
        &:hover {
          color: #007bff;
        }
      }
    }
  }
}

.filter-options-panel {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  
  &.show {
    opacity: 1;
    max-height: 500px;
  }
  
  .card {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    
    .card-body {
      background-color: #f8f9fa;
    }
  }
}

.input-container {
  .form-label {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
  }
  
  .form-control,
  .form-select {
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    
    &:focus {
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    &.form-control-sm,
    &.form-select-sm {
      padding: 0.375rem 0.75rem;
      font-size: 0.8125rem;
    }
  }
  
  .form-select[multiple] {
    min-height: 80px;
    
    option {
      padding: 0.25rem 0.5rem;
    }
  }
  
  .form-text {
    font-size: 0.7rem;
    margin-top: 0.25rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .filter-options-panel {
    .row {
      .col-md-4,
      .col-md-8 {
        margin-bottom: 1rem;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    
    .input-container {
      .row {
        .col-6 {
          margin-bottom: 0.75rem;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

@media (max-width: 576px) {
  .filter-options-panel {
    .card-body {
      padding: 1rem;
    }
    
    .input-container {
      .row {
        .col-6 {
          flex: 0 0 100%;
          max-width: 100%;
        }
      }
    }
  }
}

// Animation cho smooth transitions
.filter-field-container {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    .field-header .form-check-label {
      color: #007bff;
    }
  }
}

// Custom styles cho validation states
.input-container {
  .is-invalid {
    border-color: #dc3545;
    
    &:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }
  }
  
  .is-valid {
    border-color: #28a745;
    
    &:focus {
      border-color: #28a745;
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
  }
}
