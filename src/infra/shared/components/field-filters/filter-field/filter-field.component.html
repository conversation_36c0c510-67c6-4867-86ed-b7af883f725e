<!-- Filter Field Component -->
<div class="filter-field-container">
  <!-- Checkbox và Label -->
  <div class="field-header d-flex align-items-center">
    <mat-checkbox
      [id]="'filter-' + filter.field._id"
      [checked]="isActive()"
      (change)="handleMatCheckboxChange($event)"
      class="fw-medium">
      {{ filter.field.label }}
    </mat-checkbox>
  </div>

  <!-- Filter Options Panel (Collapse) -->
  <div
    class="filter-options-panel mt-2"
    [class.show]="!isCollapsed()"
    *ngIf="isActive()">

    <div class="card border-0 bg-light">
      <div class="card-body p-3">

        <!-- Operator Selection -->
        <div class="row align-items-center mb-3">
          <div class="col-12 col-md-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>{{ 'FIELD_FILTERS.LABELS.OPERATOR' | translate }}</mat-label>
              <mat-select
                [value]="selectedOperator()"
                (selectionChange)="handleMatOperatorChange($event)"
                [placeholder]="getOperatorPlaceholder() | translate">
                <mat-option value="">{{ getOperatorPlaceholder() | translate }}</mat-option>
                <mat-option
                  *ngFor="let operator of availableOperators()"
                  [value]="operator.value">
                  {{ operator.labelKey | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Input Area - Dynamic based on field type -->
          <div class="col-12 col-md-8" *ngIf="shouldShowInput()">

            <!-- Picklist Fields: Chips with Autocomplete -->
            <div *ngIf="isPicklistField() && inputType() === 'picklist'; else regularInput">
              <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{ 'FIELD_FILTERS.PLACEHOLDERS.SELECT_VALUES' | translate }}</mat-label>
                <mat-chip-grid #chipGrid>
                  <mat-chip-row
                    *ngFor="let chip of selectedChips()"
                    (removed)="onChipRemoved(chip)"
                    [removable]="true">
                    {{ chip }}
                    <button matChipRemove>
                      <mat-icon>cancel</mat-icon>
                    </button>
                  </mat-chip-row>
                </mat-chip-grid>
                <input
                  matInput
                  [value]="chipInputValue()"
                  (input)="onChipInputChange($event)"
                  [matAutocomplete]="auto"
                  [matChipInputFor]="chipGrid"
                  [placeholder]="selectedChips().length === 0 ? ('FIELD_FILTERS.PLACEHOLDERS.SELECT_VALUES' | translate) : ''">
                <mat-autocomplete #auto="matAutocomplete" (optionSelected)="onChipSelected($event)">
                  <mat-option
                    *ngFor="let option of filteredPicklistOptions()"
                    [value]="option">
                    {{ option }}
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
            </div>

            <!-- Regular Input Template -->
            <ng-template #regularInput>
              <mat-form-field appearance="outline" class="w-100">
                <mat-label>{{ 'FIELD_FILTERS.LABELS.VALUE' | translate }}</mat-label>

                <!-- Date Fields: Datepicker -->
                <div *ngIf="isDateField() && inputType() === 'date'; else textInput">
                  <input
                    matInput
                    [matDatepicker]="datePicker"
                    [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.SELECT_DATE' | translate"
                    [value]="getCurrentValue()"
                    (dateInput)="handleMatDateChange($event)">
                  <mat-datepicker-toggle matIconSuffix [for]="datePicker"></mat-datepicker-toggle>
                  <mat-datepicker #datePicker></mat-datepicker>
                </div>

                <!-- Text/Number/Other Fields -->
                <ng-template #textInput>
                  <input
                    matInput
                    [type]="getInputType()"
                    [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.ENTER_VALUE' | translate"
                    [value]="getCurrentValue()"
                    (input)="handleMatValueChange($event)">
                </ng-template>
              </mat-form-field>
            </ng-template>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>


