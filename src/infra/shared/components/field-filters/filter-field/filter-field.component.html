<!-- Filter Field Component -->
<div class="filter-field-container">
  <!-- Checkbox và Label -->
  <div class="field-header d-flex align-items-center">
    <mat-checkbox
      [id]="'filter-' + filter.field._id"
      [checked]="isActive()"
      (change)="handleMatCheckboxChange($event)"
      class="fw-medium">
      {{ filter.field.label }}
    </mat-checkbox>
  </div>

  <!-- Filter Options Panel (Collapse) -->
  <div
    class="filter-options-panel mt-2"
    [class.show]="!isCollapsed()"
    *ngIf="isActive()">

    <div class="card border-0 bg-light">
      <div class="card-body p-3">

        <!-- Operator Selection -->
        <div class="row align-items-center mb-3">
          <div class="col-12 col-md-4">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>{{ 'FIELD_FILTERS.LABELS.OPERATOR' | translate }}</mat-label>
              <mat-select
                [value]="selectedOperator()"
                (selectionChange)="handleMatOperatorChange($event)"
                [placeholder]="getOperatorPlaceholder() | translate">
                <mat-option value="">{{ getOperatorPlaceholder() | translate }}</mat-option>
                <mat-option
                  *ngFor="let operator of availableOperators()"
                  [value]="operator.value">
                  {{ operator.labelKey | translate }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Input Area - Dynamic Specialized Components -->
          <div class="col-12 col-md-8" *ngIf="shouldShowInput()">
            <div
              appDynamicFilterInput
              [field]="filter.field"
              [operator]="selectedOperator()"
              [value]="getCurrentFilterValue()"
              [disabled]="false"
              (filterChange)="onDynamicFilterChange($event)">
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>


