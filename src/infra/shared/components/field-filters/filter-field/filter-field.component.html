<!-- Filter Field Component -->
<div class="filter-field-container">
  <!-- Checkbox và Label -->
  <div class="field-header d-flex align-items-center">
    <div class="form-check">
      <input
        class="form-check-input"
        type="checkbox"
        [id]="'filter-' + filter.field._id"
        [checked]="isActive()"
        (change)="handleCheckboxChange($event)">
      <label
        class="form-check-label fw-medium"
        [for]="'filter-' + filter.field._id">
        {{ filter.field.label }}
      </label>
    </div>
  </div>

  <!-- Filter Options Panel (Collapse) -->
  <div
    class="filter-options-panel mt-2"
    [class.show]="!isCollapsed()"
    *ngIf="isActive()">

    <div class="card border-0 bg-light">
      <div class="card-body p-3">

        <!-- Operator Selection -->
        <div class="row align-items-center mb-3">
          <div class="col-12 col-md-4">
            <label class="form-label small text-muted mb-1">
              {{ 'FIELD_FILTERS.LABELS.OPERATOR' | translate }}
            </label>
            <select
              class="form-select form-select-sm"
              [value]="selectedOperator()"
              (change)="handleOperatorChange($event)">
              <option value="">{{ getOperatorPlaceholder() | translate }}</option>
              <option
                *ngFor="let operator of availableOperators()"
                [value]="operator.value">
                {{ operator.labelKey | translate }}
              </option>
            </select>
          </div>

          <!-- Input Area - Simplified -->
          <div class="col-12 col-md-8" *ngIf="shouldShowInput()">
            <div class="input-container">
              <label class="form-label small text-muted mb-1">
                {{ 'FIELD_FILTERS.LABELS.VALUE' | translate }}
              </label>
              <input
                type="text"
                class="form-control form-control-sm"
                [placeholder]="'FIELD_FILTERS.PLACEHOLDERS.ENTER_VALUE' | translate"
                [value]="getCurrentValue()"
                (input)="handleValueChange($event)">
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>


