import { Injectable, signal, computed } from '@angular/core';
import { Field } from '@domain/entities/field.entity';
import {
  FieldFilter,
  FieldFiltersState,
  FilterChangeEvent,
  FilterValue,
  FIELD_OPERATORS,
  OperatorConfig
} from '../models/view/field-filter-view.model';

@Injectable({
  providedIn: 'root'
})
export class FieldFiltersService {
  // Signal để quản lý state của filters
  private readonly _filtersState = signal<FieldFiltersState>({ filters: [] });

  // Computed signals để expose data
  readonly filtersState = this._filtersState.asReadonly();
  readonly activeFilters = computed(() =>
    this._filtersState().filters.filter(filter => filter.isActive)
  );
  readonly hasActiveFilters = computed(() => this.activeFilters().length > 0);

  /**
   * Khởi tạo filters từ danh sách fields
   * @param fields - Danh sách các field để tạo filter
   */
  initializeFilters(fields: Field[]): void {
    const filters: FieldFilter[] = fields.map(field => ({
      field,
      isActive: false,
      filterValue: undefined
    }));

    this._filtersState.set({ filters });
  }

  /**
   * Cập nhật trạng thái của một filter
   * @param fieldId - ID của field
   * @param isActive - Trạng thái active
   * @param filterValue - Giá trị filter (optional)
   */
  updateFilter(fieldId: number, isActive: boolean, filterValue?: FilterValue): void {
    const currentState = this._filtersState();
    const updatedFilters = currentState.filters.map(filter => {
      if (filter.field._id === fieldId) {
        return {
          ...filter,
          isActive,
          filterValue: isActive ? filterValue : undefined
        };
      }
      return filter;
    });

    this._filtersState.set({ filters: updatedFilters });
  }

  /**
   * Lấy filter theo field ID
   * @param fieldId - ID của field
   * @returns FieldFilter hoặc undefined
   */
  getFilterByFieldId(fieldId: number): FieldFilter | undefined {
    return this._filtersState().filters.find(filter => filter.field._id === fieldId);
  }

  /**
   * Lấy danh sách operators cho một field type
   * @param fieldType - Loại field
   * @returns Danh sách operators
   */
  getOperatorsForFieldType(fieldType: string): OperatorConfig[] {
    return FIELD_OPERATORS[fieldType] || [];
  }

  /**
   * Kiểm tra xem operator có cần input không
   * @param fieldType - Loại field
   * @param operator - Operator
   * @returns true nếu cần input
   */
  operatorRequiresInput(fieldType: string, operator: string): boolean {
    const operators = this.getOperatorsForFieldType(fieldType);
    const operatorConfig = operators.find(op => op.value === operator);
    return operatorConfig?.requiresInput || false;
  }

  /**
   * Lấy input type cho operator
   * @param fieldType - Loại field
   * @param operator - Operator
   * @returns Input type
   */
  getInputTypeForOperator(fieldType: string, operator: string): string | undefined {
    const operators = this.getOperatorsForFieldType(fieldType);
    const operatorConfig = operators.find(op => op.value === operator);
    return operatorConfig?.inputType;
  }

  /**
   * Xóa tất cả filters
   */
  clearAllFilters(): void {
    const currentState = this._filtersState();
    const clearedFilters = currentState.filters.map(filter => ({
      ...filter,
      isActive: false,
      filterValue: undefined
    }));

    this._filtersState.set({ filters: clearedFilters });
  }

  /**
   * Lấy filter value mặc định cho field type và operator
   * @param fieldType - Loại field
   * @param operator - Operator
   * @returns Default filter value
   */
  getDefaultFilterValue(fieldType: string, operator: string): FilterValue | undefined {
    // Tạo default value dựa trên field type và operator
    switch (fieldType) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
      case 'textarea':
        return { operator: operator as any, value: '' };

      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        if (operator === 'between' || operator === 'not_between') {
          return { operator: operator as any, minValue: 0, maxValue: 0 };
        }
        return { operator: operator as any, value: 0 };

      case 'date':
      case 'datetime':
        if (operator === 'age_in' || operator === 'due_in' || operator === 'previous' || operator === 'next') {
          return { operator: operator as any, timeValue: 1, timeUnit: 'days' };
        }
        if (operator === 'between' || operator === 'not_between') {
          return { operator: operator as any, minValue: '', maxValue: '' };
        }
        if (operator === 'on' || operator === 'before' || operator === 'after') {
          return { operator: operator as any, value: '' };
        }
        return { operator: operator as any };

      case 'picklist':
      case 'multi-picklist':
        return { operator: operator as any, values: [] };

      case 'checkbox':
        return { operator: operator as any, value: 'selected' };

      default:
        return undefined;
    }
  }

  /**
   * Validate filter value
   * @param fieldType - Loại field
   * @param filterValue - Giá trị filter
   * @returns true nếu valid
   */
  validateFilterValue(fieldType: string, filterValue: FilterValue): boolean {
    if (!filterValue) return false;

    const operator = filterValue.operator;
    const requiresInput = this.operatorRequiresInput(fieldType, operator);

    if (!requiresInput) return true;

    // Validate dựa trên field type và operator
    switch (fieldType) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
      case 'textarea':
        const textValue = (filterValue as any).value;
        return typeof textValue === 'string' && textValue.trim().length > 0;

      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        if (operator === 'between' || operator === 'not_between') {
          const numFilter = filterValue as any;
          return typeof numFilter.minValue === 'number' &&
                 typeof numFilter.maxValue === 'number' &&
                 numFilter.minValue <= numFilter.maxValue;
        }
        const numValue = (filterValue as any).value;
        return typeof numValue === 'number';

      case 'date':
      case 'datetime':
        if (operator === 'age_in' || operator === 'due_in' || operator === 'previous' || operator === 'next') {
          const dateFilter = filterValue as any;
          return typeof dateFilter.timeValue === 'number' &&
                 dateFilter.timeValue > 0 &&
                 ['days', 'weeks', 'months', 'years'].includes(dateFilter.timeUnit);
        }
        if (operator === 'between' || operator === 'not_between') {
          const dateRangeFilter = filterValue as any;
          return typeof dateRangeFilter.minValue === 'string' &&
                 typeof dateRangeFilter.maxValue === 'string' &&
                 dateRangeFilter.minValue.length > 0 &&
                 dateRangeFilter.maxValue.length > 0;
        }
        if (operator === 'on' || operator === 'before' || operator === 'after') {
          const dateValueFilter = filterValue as any;
          return typeof dateValueFilter.value === 'string' && dateValueFilter.value.length > 0;
        }
        return true;

      case 'picklist':
      case 'multi-picklist':
        const picklistFilter = filterValue as any;
        return Array.isArray(picklistFilter.values) && picklistFilter.values.length > 0;

      case 'checkbox':
        const checkboxFilter = filterValue as any;
        return ['selected', 'not_selected'].includes(checkboxFilter.value);

      default:
        return false;
    }
  }

  /**
   * Tạo event khi filter thay đổi
   * @param fieldId - ID của field
   * @param isActive - Trạng thái active
   * @param filterValue - Giá trị filter
   * @returns FilterChangeEvent
   */
  createFilterChangeEvent(fieldId: number, isActive: boolean, filterValue?: FilterValue): FilterChangeEvent {
    return {
      fieldId,
      isActive,
      filterValue
    };
  }
}
