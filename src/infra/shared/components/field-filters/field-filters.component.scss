// Field Filters Component Styles

.field-filters-container {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 0.375rem;
  padding: 1rem;
  
  // Responsive design
  @media (max-width: 768px) {
    padding: 0.75rem;
  }

  h5 {
    color: #495057;
    font-weight: 600;
  }

  .btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
    
    &:hover {
      background-color: #6c757d;
      border-color: #6c757d;
      color: #fff;
    }
  }
}

.filters-list {
  max-height: 400px;
  overflow-y: auto;
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.filter-item {
  border-bottom: 1px solid #f8f9fa;
  padding-bottom: 0.5rem;
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.active-filters-summary {
  border-top: 1px solid #e9ecef;
  padding-top: 0.75rem;
  
  small {
    display: flex;
    align-items: center;
    
    i {
      color: #007bff;
    }
  }
}

// Alert styles
.alert {
  border: none;
  border-radius: 0.375rem;
  
  &.alert-info {
    background-color: #e7f3ff;
    color: #0c5460;
    
    i {
      color: #0dcaf0;
    }
  }
}

// Responsive adjustments
@media (max-width: 576px) {
  .field-filters-container {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 0.5rem;
      
      .btn {
        align-self: flex-end;
      }
    }
  }
  
  .filters-list {
    max-height: 300px;
  }
}
