import { EventEmitter } from '@angular/core';
import { Field } from '@domain/entities/field.entity';
import { FilterValue } from '../models/view/field-filter-view.model';

/**
 * Base interface cho tất cả filter input components
 * Đảm bảo consistency và type safety across specialized components
 */
export interface BaseFilterInput {
  // Input properties
  field: Field;
  operator: string;
  value?: any;
  disabled?: boolean;

  // Output events
  valueChange: EventEmitter<any>;
  validationChange: EventEmitter<boolean>;

  // Methods
  validate(): boolean;
  reset(): void;
}

/**
 * Configuration cho dynamic component loading
 */
export interface FilterInputConfig {
  field: Field;
  operator: string;
  value?: any;
  disabled?: boolean;
}

/**
 * Event emitted khi filter value thay đổi
 */
export interface FilterInputChangeEvent {
  field: Field;
  operator: string;
  value: any;
  isValid: boolean;
}

/**
 * Mapping field types to component classes
 */
export interface FilterInputComponentMapping {
  [fieldType: string]: any; // Component class
}

/**
 * Time unit options cho date operators
 */
export interface TimeUnitOption {
  value: 'days' | 'weeks' | 'months' | 'years';
  labelKey: string;
}

/**
 * Range value cho between operators
 */
export interface RangeValue {
  minValue: any;
  maxValue: any;
}

/**
 * Date filter specific value types
 */
export interface DateFilterValue {
  operator: string;
  value?: string; // ISO date string
  timeValue?: number; // For age_in, due_in operators
  timeUnit?: 'days' | 'weeks' | 'months' | 'years';
  minValue?: string; // For between operators
  maxValue?: string; // For between operators
}

/**
 * Number filter specific value types
 */
export interface NumberFilterValue {
  operator: string;
  value?: number;
  minValue?: number; // For between operators
  maxValue?: number; // For between operators
}

/**
 * Picklist filter specific value types
 */
export interface PicklistFilterValue {
  operator: string;
  values: string[]; // Selected options
}

/**
 * Text filter specific value types
 */
export interface TextFilterValue {
  operator: string;
  value: string;
}

/**
 * Checkbox filter specific value types
 */
export interface CheckboxFilterValue {
  operator: string;
  value: 'selected' | 'not_selected';
}
