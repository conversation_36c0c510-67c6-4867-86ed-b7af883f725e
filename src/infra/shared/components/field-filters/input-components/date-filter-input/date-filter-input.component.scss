.date-filter-input {
  width: 100%;

  // Time unit input styles
  .time-unit-input {
    width: 100%;
    
    .time-value-input {
      text-align: right;
    }
  }

  // Single date input styles
  .single-date-input {
    width: 100%;
  }

  // Range date input styles
  .range-date-input {
    width: 100%;
    
    .range-error {
      margin-top: 4px;
      
      small {
        font-size: 0.75rem;
        color: #dc3545;
      }
    }
  }

  // Material form field customization
  ::ng-deep .mat-mdc-form-field {
    width: 100%;
    
    .mat-mdc-form-field-flex {
      align-items: center;
    }

    .mat-mdc-form-field-infix {
      min-height: 40px;
      padding: 8px 0;
    }

    // Input field styles
    input[matInput] {
      font-size: 0.875rem;
      line-height: 1.4;
      
      &:focus {
        outline: none;
      }
      
      &[readonly] {
        cursor: pointer;
      }
    }

    // Time value input specific styles
    .time-value-input {
      text-align: right;
      
      // Remove spinner arrows
      &::-webkit-outer-spin-button,
      &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
      }
      
      &[type=number] {
        -moz-appearance: textfield;
      }
    }

    // Label styles
    .mat-mdc-form-field-label {
      font-size: 0.875rem;
      color: #6c757d;
    }

    // Error styles
    .mat-mdc-form-field-error {
      font-size: 0.75rem;
      color: #dc3545;
      margin-top: 4px;
    }

    // Disabled state
    &.mat-form-field-disabled {
      .mat-mdc-form-field-flex {
        background-color: #f8f9fa;
      }
      
      input[matInput] {
        color: #6c757d;
        cursor: not-allowed;
      }
    }

    // Focus state
    &.mat-focused {
      .mat-mdc-form-field-outline-thick {
        border-color: #007bff;
      }
    }

    // Invalid state
    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline-thick {
        border-color: #dc3545;
      }
    }
  }

  // Material datepicker styles
  ::ng-deep mat-datepicker-toggle {
    .mat-mdc-icon-button {
      width: 40px;
      height: 40px;
      
      .mat-mdc-button-touch-target {
        width: 40px;
        height: 40px;
      }
      
      .mat-icon {
        color: #6c757d;
        
        &:hover {
          color: #007bff;
        }
      }
    }
  }

  // Material select styles
  ::ng-deep .mat-mdc-select {
    .mat-mdc-select-trigger {
      font-size: 0.875rem;
    }
    
    .mat-mdc-select-value {
      color: #495057;
    }
    
    .mat-mdc-select-arrow {
      color: #6c757d;
    }
  }
}

.no-input-message {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  text-align: center;
  
  small {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
  }
}

// Operator specific styling
.date-filter-input {
  &.age-in-operator,
  &.due-in-operator {
    .time-unit-input {
      .time-value-input {
        color: #28a745;
        font-weight: 500;
      }
    }
  }
  
  &.between-operator,
  &.not-between-operator {
    .range-date-input {
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 8px;
      background-color: #f8f9fa;
    }
  }
}

// Responsive design
@media (max-width: 576px) {
  .date-filter-input {
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-form-field-infix {
        min-height: 36px;
        padding: 6px 0;
      }
      
      input[matInput] {
        font-size: 0.8rem;
      }
      
      .mat-mdc-form-field-label {
        font-size: 0.8rem;
      }
    }
    
    ::ng-deep mat-datepicker-toggle {
      .mat-mdc-icon-button {
        width: 36px;
        height: 36px;
        
        .mat-mdc-button-touch-target {
          width: 36px;
          height: 36px;
        }
      }
    }
    
    .range-date-input {
      .range-error {
        margin-top: 2px;
        
        small {
          font-size: 0.7rem;
        }
      }
    }
  }
  
  .no-input-message {
    padding: 8px 12px;
    
    small {
      font-size: 0.7rem;
    }
  }
}
