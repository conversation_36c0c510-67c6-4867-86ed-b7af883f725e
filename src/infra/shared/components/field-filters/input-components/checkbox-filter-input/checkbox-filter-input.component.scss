.checkbox-filter-input {
  width: 100%;

  // Value explanation styles
  .value-explanation {
    margin-top: 4px;
    
    small {
      font-size: 0.75rem;
      color: #6c757d;
      font-style: italic;
    }
  }

  // Material form field customization
  ::ng-deep .mat-mdc-form-field {
    width: 100%;
    
    .mat-mdc-form-field-flex {
      align-items: center;
    }

    .mat-mdc-form-field-infix {
      min-height: 40px;
      padding: 8px 0;
    }

    // Label styles
    .mat-mdc-form-field-label {
      font-size: 0.875rem;
      color: #6c757d;
    }

    // Hint styles (selected preview)
    .mat-mdc-form-field-hint {
      font-size: 0.75rem;
      color: #6c757d;
      margin-top: 4px;
      
      .small-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }

    // Disabled state
    &.mat-form-field-disabled {
      .mat-mdc-form-field-flex {
        background-color: #f8f9fa;
      }
      
      .checkbox-select {
        color: #6c757d;
        cursor: not-allowed;
      }
    }

    // Focus state
    &.mat-focused {
      .mat-mdc-form-field-outline-thick {
        border-color: #007bff;
      }
    }
  }

  // Material select styles
  ::ng-deep .checkbox-select {
    .mat-mdc-select-trigger {
      font-size: 0.875rem;
    }
    
    .mat-mdc-select-value {
      color: #495057;
    }
    
    .mat-mdc-select-arrow {
      color: #6c757d;
    }
  }

  // Material option styles
  ::ng-deep .checkbox-option {
    .option-content {
      width: 100%;
      
      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        
        &.text-success {
          color: #28a745;
        }
        
        &.text-muted {
          color: #6c757d;
        }
      }
      
      span {
        font-size: 0.875rem;
        color: #495057;
      }
    }
    
    &:hover {
      background-color: rgba(0, 123, 255, 0.1);
      
      .option-content {
        mat-icon {
          &.text-success {
            color: #1e7e34;
          }
          
          &.text-muted {
            color: #495057;
          }
        }
        
        span {
          color: #007bff;
        }
      }
    }
    
    &.mat-selected {
      background-color: #e3f2fd;
      
      .option-content {
        mat-icon {
          &.text-success {
            color: #1e7e34;
          }
          
          &.text-muted {
            color: #495057;
          }
        }
        
        span {
          color: #1976d2;
          font-weight: 500;
        }
      }
    }
  }
}

.no-input-message {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  text-align: center;
  
  small {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
  }
}

// Selected preview styles
.selected-preview {
  .text-success {
    color: #28a745 !important;
  }
  
  .text-muted {
    color: #6c757d !important;
  }
}

// State specific styling
.checkbox-filter-input {
  &.selected-state {
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-form-field-outline-thick {
        border-color: #28a745;
      }
    }
    
    .value-explanation {
      small {
        color: #28a745;
      }
    }
  }
  
  &.not-selected-state {
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-form-field-outline-thick {
        border-color: #6c757d;
      }
    }
    
    .value-explanation {
      small {
        color: #6c757d;
      }
    }
  }
}

// Responsive design
@media (max-width: 576px) {
  .checkbox-filter-input {
    ::ng-deep .mat-mdc-form-field {
      .mat-mdc-form-field-infix {
        min-height: 36px;
        padding: 6px 0;
      }
      
      .mat-mdc-form-field-label {
        font-size: 0.8rem;
      }
      
      .mat-mdc-form-field-hint {
        font-size: 0.7rem;
        
        .small-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }
    }
    
    ::ng-deep .checkbox-select {
      .mat-mdc-select-trigger {
        font-size: 0.8rem;
      }
    }
    
    ::ng-deep .checkbox-option {
      .option-content {
        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
        
        span {
          font-size: 0.8rem;
        }
      }
    }
    
    .value-explanation {
      margin-top: 2px;
      
      small {
        font-size: 0.7rem;
      }
    }
  }
  
  .no-input-message {
    padding: 8px 12px;
    
    small {
      font-size: 0.7rem;
    }
  }
}
