<!-- Field Filters Component -->
<div class="field-filters-container">
  <!-- Header với title và clear all button -->
  <div class="d-flex justify-content-between align-items-center mb-3" *ngIf="showTitle || (showClearAll && hasActiveFilters())">
    <h5 class="mb-0" *ngIf="showTitle">
      {{ 'FIELD_FILTERS.TITLE' | translate }}
    </h5>
    
    <button 
      type="button" 
      class="btn btn-outline-secondary btn-sm"
      *ngIf="showClearAll && hasActiveFilters()"
      (click)="onClearAllFilters()">
      <i class="fas fa-times me-1"></i>
      {{ 'FIELD_FILTERS.CLEAR_ALL' | translate }}
    </button>
  </div>

  <!-- Danh sách filters -->
  <div class="filters-list" *ngIf="filters().length > 0; else noFields">
    <div 
      class="filter-item mb-2"
      *ngFor="let filter of filters(); trackBy: trackByFieldId">
      <app-filter-field
        [filter]="filter"
        (filterChange)="onFilterChange($event)">
      </app-filter-field>
    </div>
  </div>

  <!-- Thông báo khi không có fields -->
  <ng-template #noFields>
    <div class="alert alert-info text-center">
      <i class="fas fa-info-circle me-2"></i>
      {{ 'FIELD_FILTERS.NO_FIELDS' | translate }}
    </div>
  </ng-template>

  <!-- Summary của active filters -->
  <div class="active-filters-summary mt-3" *ngIf="hasActiveFilters()">
    <small class="text-muted">
      <i class="fas fa-filter me-1"></i>
      {{ activeFilters().length }} filter(s) active
    </small>
  </div>
</div>
