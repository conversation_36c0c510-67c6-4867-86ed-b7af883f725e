<div class="template-selector-container">

  <!-- Header -->
  <div class="selector-header">
    <h4>{{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.TITLE' | translate }}</h4>
    <p class="selector-description">
      {{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.DESCRIPTION' | translate }}
    </p>
  </div>

  <!-- Template Selection -->
  <div class="template-selection">
    <mat-form-field appearance="outline" class="template-dropdown">
      <mat-label>{{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.SELECT_TEMPLATE' | translate }}</mat-label>
      <mat-select [(value)]="selectedTemplateId" (selectionChange)="onTemplateSelected($event.value)">
        <mat-option value="">
          {{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.NO_TEMPLATE' | translate }}
        </mat-option>
        <mat-option *ngFor="let template of availableTemplates(); trackBy: trackByTemplate"
                    [value]="template.id">
          <div class="template-option">
            <mat-icon class="template-icon">{{ template.icon }}</mat-icon>
            <span class="template-name">{{ template.name }}</span>
          </div>
        </mat-option>
      </mat-select>
    </mat-form-field>

    <!-- Apply Template Button -->
    <button mat-raised-button
            color="primary"
            class="apply-btn"
            [disabled]="!selectedTemplateId || isApplying()"
            (click)="onApplyTemplate()">
      <mat-icon>check</mat-icon>
      {{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.APPLY_TEMPLATE' | translate }}
    </button>
  </div>

  <!-- Template Preview -->
  <div *ngIf="selectedTemplate()" class="template-preview">
    <mat-card class="preview-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon class="preview-icon">{{ selectedTemplate()?.icon }}</mat-icon>
          {{ selectedTemplate()?.name }}
        </mat-card-title>
        <mat-card-subtitle>
          {{ selectedTemplate()?.description }}
        </mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <!-- Template Tags -->
        <div class="template-tags">
          <mat-chip-listbox>
            <mat-chip *ngFor="let tag of selectedTemplate()?.tags">
              {{ tag }}
            </mat-chip>
          </mat-chip-listbox>
        </div>

        <!-- Template Sections Preview -->
        <div class="sections-preview">
          <h5>{{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.SECTIONS_INCLUDED' | translate }}</h5>
          <div class="sections-list">
            <div *ngFor="let section of selectedTemplate()?.sections; trackBy: trackBySection"
                 class="section-preview-item">
              <mat-icon class="section-icon">view_module</mat-icon>
              <div class="section-info">
                <span class="section-title">{{ section.title }}</span>
                <span class="section-fields-count">
                  {{ section.fields.length }}
                  {{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.FIELDS' | translate }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>

      <mat-card-actions>
        <button mat-button (click)="onPreviewTemplate()">
          <mat-icon>visibility</mat-icon>
          {{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.PREVIEW' | translate }}
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Templates Gallery -->
  <div class="templates-gallery">
    <h5>{{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.AVAILABLE_TEMPLATES' | translate }}</h5>
    <div class="templates-grid">
      <mat-card *ngFor="let template of availableTemplates(); trackBy: trackByTemplate"
                class="template-card"
                [class.selected]="selectedTemplateId === template.id"
                (click)="onTemplateCardClick(template.id)">

        <mat-card-header>
          <mat-icon mat-card-avatar class="template-avatar">{{ template.icon }}</mat-icon>
          <mat-card-title class="template-card-title">{{ template.name }}</mat-card-title>
          <mat-card-subtitle class="template-card-subtitle">
            {{ template.sections.length }} {{ 'DYNAMIC_LAYOUT_BUILDER.ARIA_LABELS.SECTIONS_COUNT' | translate }}
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <p class="template-card-description">{{ template.description }}</p>

          <!-- Category badge -->
          <div class="category-badge" [class]="'category-' + template.category">
            {{ getCategoryLabel(template.category) }}
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-button
                  color="primary"
                  (click)="onSelectAndApplyTemplate(template.id); $event.stopPropagation()">
            {{ 'DYNAMIC_LAYOUT_BUILDER.TEMPLATE_SELECTOR.USE_TEMPLATE' | translate }}
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>
</div>
