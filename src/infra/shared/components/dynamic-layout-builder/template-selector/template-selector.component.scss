.template-selector-container {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  .selector-header {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    .selector-description {
      margin: 0;
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }
  }

  .template-selection {
    display: flex;
    gap: 16px;
    align-items: flex-end;
    margin-bottom: 24px;

    .template-dropdown {
      flex: 1;
      min-width: 250px;

      .template-option {
        display: flex;
        align-items: center;
        gap: 8px;

        .template-icon {
          font-size: 18px;
          color: #2196f3;
        }

        .template-name {
          font-size: 14px;
        }
      }
    }

    .apply-btn {
      height: 56px;
      padding: 0 24px;
      
      mat-icon {
        margin-right: 8px;
      }
    }
  }

  .template-preview {
    margin-bottom: 32px;

    .preview-card {
      .preview-icon {
        margin-right: 8px;
        color: #2196f3;
      }

      .template-tags {
        margin-bottom: 16px;

        mat-chip-listbox {
          mat-chip {
            background-color: #e3f2fd;
            color: #1976d2;
            font-size: 12px;
          }
        }
      }

      .sections-preview {
        h5 {
          margin: 0 0 12px 0;
          font-size: 14px;
          font-weight: 500;
          color: #555;
        }

        .sections-list {
          .section-preview-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .section-icon {
              margin-right: 12px;
              color: #666;
              font-size: 18px;
            }

            .section-info {
              display: flex;
              flex-direction: column;

              .section-title {
                font-size: 14px;
                font-weight: 500;
                color: #333;
              }

              .section-fields-count {
                font-size: 12px;
                color: #666;
              }
            }
          }
        }
      }
    }
  }

  .templates-gallery {
    h5 {
      margin: 0 0 16px 0;
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }

    .templates-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 16px;

      .template-card {
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        &.selected {
          border-color: #2196f3;
          box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3);
        }

        .template-avatar {
          background-color: #2196f3;
          color: white;
          font-size: 20px;
        }

        .template-card-title {
          font-size: 16px;
          font-weight: 500;
        }

        .template-card-subtitle {
          font-size: 12px;
          color: #666;
        }

        .template-card-description {
          font-size: 14px;
          color: #555;
          line-height: 1.4;
          margin-bottom: 12px;
        }

        .category-badge {
          display: inline-block;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;

          &.category-fashion {
            background-color: #e8f5e8;
            color: #2e7d32;
          }

          &.category-beauty {
            background-color: #fce4ec;
            color: #c2185b;
          }

          &.category-food {
            background-color: #fff3e0;
            color: #f57c00;
          }

          &.category-electronics {
            background-color: #e3f2fd;
            color: #1976d2;
          }

          &.category-general {
            background-color: #f3e5f5;
            color: #7b1fa2;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .template-selector-container {
    padding: 12px;

    .template-selection {
      flex-direction: column;
      align-items: stretch;

      .template-dropdown {
        min-width: auto;
      }

      .apply-btn {
        height: 48px;
      }
    }

    .templates-gallery {
      .templates-grid {
        grid-template-columns: 1fr;
        gap: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .template-selector-container {
    .selector-header {
      h4 {
        font-size: 16px;
      }
    }

    .template-preview {
      .preview-card {
        mat-card-header {
          mat-card-title {
            font-size: 16px;
          }
        }
      }
    }
  }
}

// Animation for template cards
@keyframes templateCardAppear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-card {
  animation: templateCardAppear 0.3s ease-out;
}

// Stagger animation for multiple cards
.template-card:nth-child(1) { animation-delay: 0.1s; }
.template-card:nth-child(2) { animation-delay: 0.2s; }
.template-card:nth-child(3) { animation-delay: 0.3s; }
.template-card:nth-child(4) { animation-delay: 0.4s; }
.template-card:nth-child(5) { animation-delay: 0.5s; }
.template-card:nth-child(6) { animation-delay: 0.6s; }

// Loading state for apply button
.apply-btn[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

// Preview card animation
.template-preview {
  animation: templateCardAppear 0.4s ease-out;
}
