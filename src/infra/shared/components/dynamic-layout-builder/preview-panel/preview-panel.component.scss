.preview-panel-container {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;

  .preview-header {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
      color: #333;
    }

    .preview-description {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }

    .preview-controls {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      button {
        mat-icon {
          margin-right: 8px;
          font-size: 18px;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #666;

    .empty-icon {
      font-size: 64px;
      width: 64px;
      height: 64px;
      color: #ccc;
      margin-bottom: 16px;
    }

    h5 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 500;
    }

    p {
      margin: 0;
      font-size: 14px;
      line-height: 1.4;
    }
  }

  .preview-form {
    .preview-tabs {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .form-view-content {
        padding: 16px;

        .section-card {
          margin-bottom: 24px;
          border: 1px solid #e0e0e0;

          &:last-child {
            margin-bottom: 0;
          }

          .section-title {
            display: flex;
            align-items: center;
            gap: 8px;

            mat-icon {
              color: #2196f3;
            }
          }

          .fields-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 16px;
            margin-top: 16px;

            .field-container {
              &.field-type-textarea {
                grid-column: 1 / -1;
              }

              .field-input {
                width: 100%;

                &.field-textarea {
                  textarea {
                    min-height: 80px;
                  }
                }

                .required-asterisk {
                  color: #f44336;
                  margin-left: 4px;
                }
              }

              .checkbox-field {
                padding: 16px 0;

                mat-checkbox {
                  .required-asterisk {
                    color: #f44336;
                    margin-left: 4px;
                  }
                }
              }

              .radio-field {
                padding: 16px 0;

                .field-label {
                  display: block;
                  font-size: 14px;
                  font-weight: 500;
                  color: #333;
                  margin-bottom: 12px;

                  .required-asterisk {
                    color: #f44336;
                    margin-left: 4px;
                  }
                }

                mat-radio-group {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;

                  .radio-option {
                    margin-bottom: 8px;
                  }
                }
              }
            }
          }
        }
      }

      .data-view-content {
        padding: 16px;

        .data-card {
          border: 1px solid #e0e0e0;

          mat-card-title {
            display: flex;
            align-items: center;
            gap: 8px;

            mat-icon {
              color: #2196f3;
            }
          }

          .data-json {
            background: #f5f5f5;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            padding: 16px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
          }
        }
      }
    }
  }

  .validation-results {
    margin-top: 24px;

    .validation-card {
      border: 1px solid #e0e0e0;

      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;

        .error-icon {
          color: #f44336;
        }

        .success-icon {
          color: #4caf50;
        }
      }

      .validation-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &.error {
          .validation-icon {
            color: #f44336;
          }

          .validation-message {
            color: #d32f2f;
          }
        }

        &.warning {
          .validation-icon {
            color: #ff9800;
          }

          .validation-message {
            color: #f57c00;
          }
        }

        .validation-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }

        .validation-message {
          font-size: 14px;
          line-height: 1.4;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .preview-panel-container {
    padding: 12px;

    .preview-header {
      .preview-controls {
        flex-direction: column;

        button {
          width: 100%;
          justify-content: center;
        }
      }
    }

    .preview-form {
      .preview-tabs {
        .form-view-content {
          padding: 12px;

          .section-card {
            .fields-grid {
              grid-template-columns: 1fr;
              gap: 12px;
            }
          }
        }

        .data-view-content {
          padding: 12px;

          .data-card {
            .data-json {
              font-size: 11px;
              padding: 12px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .preview-panel-container {
    .preview-header {
      h4 {
        font-size: 16px;
      }

      .preview-description {
        font-size: 13px;
      }
    }

    .empty-state {
      padding: 32px 16px;

      .empty-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
      }

      h5 {
        font-size: 16px;
      }
    }
  }
}

// Animation for form fields
@keyframes fieldAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.field-container {
  animation: fieldAppear 0.3s ease-out;
}

// Stagger animation for multiple fields
.field-container:nth-child(1) { animation-delay: 0.1s; }
.field-container:nth-child(2) { animation-delay: 0.2s; }
.field-container:nth-child(3) { animation-delay: 0.3s; }
.field-container:nth-child(4) { animation-delay: 0.4s; }
.field-container:nth-child(5) { animation-delay: 0.5s; }
.field-container:nth-child(6) { animation-delay: 0.6s; }

// Tab content animation
.form-view-content,
.data-view-content {
  animation: fieldAppear 0.4s ease-out;
}

// Validation results animation
.validation-results {
  animation: fieldAppear 0.5s ease-out;
}

// Loading states
button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}
