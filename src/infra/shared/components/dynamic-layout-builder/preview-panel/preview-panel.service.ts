import { Injectable } from '@angular/core';
import { Section, LayoutField, FieldValue, PreviewData } from '@shared/models/view/dynamic-layout-builder.model';

/**
 * Interface cho validation result
 */
export interface ValidationResult {
  type: 'error' | 'warning';
  message: string;
}

/**
 * PreviewPanelService - Service xử lý business logic cho Preview Panel
 *
 * Chức năng chính:
 * - Validation form data
 * - Tạo sample data
 * - Xử lý field options
 * - Format validation
 */
@Injectable({
  providedIn: 'root'
})
export class PreviewPanelService {

  /**
   * Validate form data
   */
  validateFormData(sections: Section[], previewData: PreviewData): ValidationResult[] {
    const results: ValidationResult[] = [];

    sections.forEach(section => {
      section.fields.forEach(field => {
        const fieldKey = this.getFieldKey(field);
        const value = previewData[fieldKey];

        // Check required fields
        const isRequired = field.required || field.isRequired;
        if (isRequired && (!value || value === '')) {
          results.push({
            type: 'error',
            message: `Trường "${field.label}" là bắt buộc`
          });
        }

        // Check email format
        if (field.type === 'email' && value && !this.isValidEmail(String(value))) {
          results.push({
            type: 'error',
            message: `Trường "${field.label}" không đúng định dạng email`
          });
        }

        // Check phone format
        if (field.type === 'phone' && value && !this.isValidPhone(String(value))) {
          results.push({
            type: 'warning',
            message: `Trường "${field.label}" có thể không đúng định dạng số điện thoại`
          });
        }
      });
    });

    if (results.length === 0) {
      results.push({
        type: 'warning',
        message: 'Tất cả dữ liệu hợp lệ!'
      });
    }

    return results;
  }

  /**
   * Tạo sample data cho tất cả fields
   */
  generateSampleData(sections: Section[]): PreviewData {
    const sampleData: PreviewData = {};

    sections.forEach(section => {
      section.fields.forEach(field => {
        const fieldKey = this.getFieldKey(field);
        sampleData[fieldKey] = this.getSampleValue(field);
      });
    });

    return sampleData;
  }

  /**
   * Lấy key cho field
   */
  getFieldKey(field: LayoutField): string {
    return `field_${field.id}`;
  }

  /**
   * Lấy giá trị mẫu cho field
   */
  getSampleValue(field: LayoutField): FieldValue {
    switch (field.type) {
      case 'text':
        return field.label.includes('Tên') ? 'Nguyễn Văn A' : 'Giá trị mẫu';
      case 'number':
        return 123;
      case 'email':
        return '<EMAIL>';
      case 'phone':
        return '0123456789';
      case 'textarea':
        return 'Đây là nội dung mẫu cho trường textarea';
      case 'date':
        return new Date();
      case 'select':
        const options = this.getSelectOptions(field);
        return options.length > 0 ? options[0].value : '';
      case 'checkbox':
        return true;
      case 'radio':
        const radioOptions = this.getRadioOptions(field);
        return radioOptions.length > 0 ? radioOptions[0].value : '';
      default:
        return 'Giá trị mẫu';
    }
  }

  /**
   * Lấy options cho select field
   */
  getSelectOptions(field: LayoutField): Array<{ value: string; label: string }> {
    // Kiểm tra constraints trước
    if (field.constraints?.options) {
      if (Array.isArray(field.constraints.options)) {
        return field.constraints.options.map(option => {
          if (typeof option === 'string') {
            return { value: option, label: option };
          }
          return { value: String(option.value), label: option.label };
        });
      }
    }

    // Mock options mặc định
    return [
      { value: 'option1', label: 'Tùy chọn 1' },
      { value: 'option2', label: 'Tùy chọn 2' },
      { value: 'option3', label: 'Tùy chọn 3' }
    ];
  }

  /**
   * Lấy options cho radio field
   */
  getRadioOptions(field: LayoutField): Array<{ value: string; label: string }> {
    // Kiểm tra constraints trước
    if (field.constraints?.options) {
      if (Array.isArray(field.constraints.options)) {
        return field.constraints.options.map(option => {
          if (typeof option === 'string') {
            return { value: option, label: option };
          }
          return { value: String(option.value), label: option.label };
        });
      }
    }

    // Mock options mặc định
    return [
      { value: 'radio1', label: 'Lựa chọn 1' },
      { value: 'radio2', label: 'Lựa chọn 2' },
      { value: 'radio3', label: 'Lựa chọn 3' }
    ];
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  private isValidPhone(phone: string): boolean {
    const phoneRegex = /^[0-9+\-\s()]+$/;
    return phoneRegex.test(phone) && phone.length >= 10;
  }

  /**
   * Format form data as JSON
   */
  formatFormDataAsJson(previewData: PreviewData): string {
    return JSON.stringify(previewData, null, 2);
  }
}
