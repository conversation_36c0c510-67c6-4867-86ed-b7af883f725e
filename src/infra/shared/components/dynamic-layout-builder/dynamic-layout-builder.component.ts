import { Component, Input, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, signal, computed, inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';

import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { DragDropModule, CdkDragDrop, moveItemInArray, transferArrayItem } from '@angular/cdk/drag-drop';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

import { DynamicLayoutBuilderInput, Section, Template, LayoutField, GoldenLayoutConfig } from '@shared/models/view/dynamic-layout-builder.model';
import { DynamicLayoutBuilderService } from './dynamic-layout-builder.service';
import { NewSectionComponent } from './new-section/new-section.component';
import { SectionComponent } from './section/section.component';
import { FieldTypeSelectorComponent } from './field-type-selector/field-type-selector.component';
import { TemplateSelectorComponent, IndustryTemplate } from './template-selector/template-selector.component';
import { PreviewPanelComponent } from './preview-panel/preview-panel.component';
import { FieldListComponent } from './field-list/field-list.component';
import { FieldItemComponent } from './field-item/field-item.component';
import { ResizePanelDirective } from '@/shared/directives/resize-panel/resize-panel.directive';
import { getElementMaxHeightToFit100vh } from '@shared/utils';


// import { TemplateSelectorComponent } from './template-selector/template-selector.component';
// import { PreviewPanelComponent } from './preview-panel/preview-panel.component';
// import { GoldenLayoutAdapterComponent } from './golden-layout/golden-layout-adapter.component';

/**
 * DynamicLayoutBuilderComponent - Component chính để tạo và quản lý layout khách hàng
 *
 * Tính năng chính:
 * - Kéo-thả để tạo sections và fields
 * - Chỉnh sửa inline tên section và field labels
 * - Quản lý thuộc tính và quyền của fields
 * - Template ngành hàng có sẵn
 * - Lưu/khôi phục layout
 * - Preview dữ liệu khách hàng
 */
@Component({
  selector: 'app-dynamic-layout-builder',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,

    MatProgressSpinnerModule,
    DragDropModule,
    TranslateModule,
    NewSectionComponent,
    SectionComponent,
    FieldTypeSelectorComponent,
    TemplateSelectorComponent,
    PreviewPanelComponent,
    ResizePanelDirective
  ],
  templateUrl: './dynamic-layout-builder.component.html',
  styleUrls: ['./dynamic-layout-builder.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DynamicLayoutBuilderComponent implements OnInit, OnDestroy, AfterViewInit {
  private dynamicLayoutBuilderService = inject(DynamicLayoutBuilderService);
  private subscriptions = new Subscription();

  @ViewChild('leftPanel', { static: true }) leftPanel!: ElementRef<HTMLElement>;

  // Input properties từ parent component
  @Input() layoutConfig?: GoldenLayoutConfig; // Golden Layout config
  @Input() sections: Section[] = [];
  @Input() templates: Template[] = [];
  @Input() availableFieldTypes: LayoutField[] = [];

  // Signals để quản lý trạng thái
  currentSections = signal<Section[]>([]);
  selectedTemplate = signal<Template | null>(null);
  isPreviewMode = signal<boolean>(false);
  isLoading = signal<boolean>(false);

  // Computed signals
  hasAnySections = computed(() => this.currentSections().length > 0);
  totalFields = computed(() =>
    this.currentSections().reduce((total, section) => total + section.fields.length, 0)
  );

  ngOnInit(): void {
    // Khởi tạo dữ liệu mặc định nếu không có input
    if (this.availableFieldTypes.length === 0) {
      this.availableFieldTypes = this.dynamicLayoutBuilderService.getDefaultFieldTypes();
    }

    if (this.templates.length === 0) {
      this.templates = this.dynamicLayoutBuilderService.getDefaultTemplates();
    }

    // Khởi tạo dữ liệu từ input hoặc service
    this.initializeData();

    // Subscribe to service events
    this.subscribeToServiceEvents();

  }

  ngAfterViewInit(): void {
    if(this.leftPanel.nativeElement) {
      const maxHeight = getElementMaxHeightToFit100vh(this.leftPanel.nativeElement);
      this.leftPanel.nativeElement.style.setProperty('max-height', `${maxHeight}px`);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Khởi tạo dữ liệu ban đầu
   */
  private initializeData(): void {
    if (this.sections.length > 0) {
      this.currentSections.set([...this.sections]);
    } else {
      // Tạo section mặc định nếu không có dữ liệu
      this.createDefaultSection();
    }
  }

  /**
   * Subscribe to service events để cập nhật UI
   */
  private subscribeToServiceEvents(): void {
    // Subscribe to layout changes
    const layoutSub = this.dynamicLayoutBuilderService.layoutChanged$.subscribe(
      (sections: Section[]) => {
        this.currentSections.set(sections);
      }
    );
    this.subscriptions.add(layoutSub);

    // Subscribe to loading state
    const loadingSub = this.dynamicLayoutBuilderService.isLoading$.subscribe(
      (loading: boolean) => {
        this.isLoading.set(loading);
      }
    );
    this.subscriptions.add(loadingSub);
  }

  /**
   * Tạo section mặc định
   */
  private createDefaultSection(): void {
    const defaultSection = this.dynamicLayoutBuilderService.createDefaultSection();
    this.currentSections.set([defaultSection]);
  }

  /**
   * Xử lý khi chọn template
   */
  onTemplateSelected(template: Template): void {
    this.selectedTemplate.set(template);
    this.dynamicLayoutBuilderService.applyTemplate(template);
  }

  /**
   * Xử lý khi thêm section mới
   */
  onAddSection(): void {
    const defaultSection = this.dynamicLayoutBuilderService.createDefaultSection();
    const newSection: Section = {
      ...defaultSection,
      title: `Section ${this.currentSections().length + 1}`
    };

    const updatedSections = [...this.currentSections(), newSection];
    this.currentSections.set(updatedSections);
    this.dynamicLayoutBuilderService.updateSections(updatedSections);
  }

  /**
   * Xử lý khi xóa section
   */
  onDeleteSection(sectionId: string): void {
    const updatedSections = this.currentSections().filter(s => s.id !== sectionId);
    this.currentSections.set(updatedSections);
    this.dynamicLayoutBuilderService.updateSections(updatedSections);
  }

  /**
   * Xử lý khi thêm field vào section
   */
  onAddField(sectionId: string, fieldType: LayoutField): void {
    this.dynamicLayoutBuilderService.addFieldToSection(sectionId, fieldType);
  }

  /**
   * Xử lý khi xóa field khỏi section
   */
  onDeleteField(sectionId: string, fieldId: number): void {
    this.dynamicLayoutBuilderService.removeFieldFromSection(sectionId, fieldId);
  }

  /**
   * Lưu layout hiện tại
   */
  onSaveLayout(): void {
    this.dynamicLayoutBuilderService.saveLayout(this.currentSections());
  }

  /**
   * Khôi phục layout đã lưu
   */
  onRestoreLayout(): void {
    this.dynamicLayoutBuilderService.restoreLayout();
  }

  /**
   * Toggle preview mode
   */
  onTogglePreview(): void {
    this.isPreviewMode.set(!this.isPreviewMode());
  }

  /**
   * Xử lý khi người dùng chọn field type từ FieldTypeSelectorComponent
   */
  onFieldTypeSelected(fieldType: LayoutField): void {
    console.log('onFieldTypeSelected called with:', fieldType);

    // Tạo field mới với ID unique
    const newField: LayoutField = {
      ...fieldType,
      id: Date.now() + Math.floor(Math.random() * 1000),
      order: 1 // Sẽ được cập nhật khi thêm vào section
    };

    console.log('Created new field:', newField);
    console.log('Current sections:', this.currentSections());

    // Nếu có section, thêm vào section đầu tiên
    if (this.currentSections().length > 0) {
      const firstSection = this.currentSections()[0];
      console.log('Adding field to first section:', firstSection.id);
      this.onAddField(firstSection.id, newField);
    } else {
      console.log('No sections found, creating new section');
      // Tạo section mới và thêm field vào
      this.onAddSection();
      setTimeout(() => {
        if (this.currentSections().length > 0) {
          const newSection = this.currentSections()[this.currentSections().length - 1];
          console.log('Adding field to new section:', newSection.id);
          this.onAddField(newSection.id, newField);
        }
      }, 100);
    }
  }

  /**
   * Xử lý khi người dùng áp dụng template
   */
  onTemplateApplied(template: IndustryTemplate): void {
    // Sử dụng service để tạo sections từ template
    const templateSections = this.dynamicLayoutBuilderService.applyTemplateAndGetSections(template);

    this.currentSections.set(templateSections);
    this.dynamicLayoutBuilderService.updateSections(templateSections);

    // Hiển thị thông báo thành công
    console.log(`Template "${template.name}" đã được áp dụng thành công`);
  }

  /**
   * Xử lý khi người dùng preview template
   */
  onTemplatePreviewed(template: IndustryTemplate): void {
    // Tạm thời hiển thị preview trong console
    console.log('Preview template:', template);

    // Có thể mở modal hoặc panel để hiển thị preview
    // Hoặc tạm thời set preview mode
    this.isPreviewMode.set(true);
  }

  /**
   * TrackBy function cho sections
   */
  trackBySection(_index: number, section: Section): string {
    return section.id;
  }

  /**
   * TrackBy function cho field types
   */
  trackByFieldType(_index: number, field: LayoutField): number {
    return field.id;
  }

  /**
   * TrackBy function cho fields
   */
  trackByField(_index: number, field: LayoutField): number {
    return field.id;
  }

  /**
   * Xử lý khi drop section
   */
  onSectionDropped(event: CdkDragDrop<Section[]>): void {
    if (event.previousContainer === event.container) {
      const sections = [...this.currentSections()];
      moveItemInArray(sections, event.previousIndex, event.currentIndex);
      this.currentSections.set(sections);
      this.dynamicLayoutBuilderService.updateSections(sections);
    }
  }

  /**
   * Xử lý khi drop field
   */
  onFieldDropped(event: CdkDragDrop<LayoutField[]>, sectionId: string): void {
    if (event.previousContainer === event.container) {
      // Sắp xếp lại fields trong cùng section
      const sections = [...this.currentSections()];
      const sectionIndex = sections.findIndex(s => s.id === sectionId);
      if (sectionIndex !== -1) {
        const fields = [...sections[sectionIndex].fields];
        moveItemInArray(fields, event.previousIndex, event.currentIndex);
        sections[sectionIndex] = { ...sections[sectionIndex], fields };
        this.currentSections.set(sections);
        this.dynamicLayoutBuilderService.updateSections(sections);
      }
    } else {
      // Chuyển field từ sidebar vào section
      const dragData = event.previousContainer.data;
      if (dragData && dragData[0]?.type) {
        this.onAddField(sectionId, dragData[0]);
      }
    }
  }

  /**
   * Xử lý khi cập nhật section
   */
  onSectionUpdated(updatedSection: Section): void {
    const sections = [...this.currentSections()];
    const index = sections.findIndex(s => s.id === updatedSection.id);
    if (index !== -1) {
      sections[index] = updatedSection;
      this.currentSections.set(sections);
      this.dynamicLayoutBuilderService.updateSections(sections);
    }
  }

  /**
   * Xử lý khi cập nhật field
   */
  onFieldUpdated(data: { sectionId: string; fieldId: number; field: Partial<LayoutField> }): void {
    this.dynamicLayoutBuilderService.updateFieldInSection(data.sectionId, data.fieldId, data.field);
  }



  /**
   * Xử lý khi section bị xóa
   */
  onSectionDeleted(section: Section): void {
    this.onDeleteSection(section.id);
  }

  /**
   * Xử lý khi tên section thay đổi
   */
  onSectionTitleChanged(data: { section: Section; newTitle: string }): void {
    const updatedSections = this.dynamicLayoutBuilderService.handleSectionTitleChange(
      data.section.id,
      data.newTitle,
      this.currentSections()
    );
    this.currentSections.set(updatedSections);
    this.dynamicLayoutBuilderService.updateSections(updatedSections);
  }



  /**
   * Xử lý khi thứ tự fields thay đổi
   */
  onSectionFieldsReordered(data: { section: Section; fields: LayoutField[] }): void {
    const updatedSections = this.dynamicLayoutBuilderService.handleFieldReorder(
      data.section.id,
      data.fields,
      this.currentSections()
    );
    this.currentSections.set(updatedSections);
    this.dynamicLayoutBuilderService.updateSections(updatedSections);
  }

  /**
   * Xử lý khi toggle required status của field
   */
  onSectionFieldRequiredToggled(data: { section: Section; field: LayoutField; isRequired: boolean }): void {
    this.dynamicLayoutBuilderService.updateFieldInSection(data.section.id, data.field.id, { isRequired: data.isRequired });
  }

  /**
   * Xử lý khi edit properties của field
   */
  onSectionFieldPropertiesEdit(data: { section: Section; field: LayoutField }): void {
    const updatedSections = this.dynamicLayoutBuilderService.handleFieldPropertiesUpdate(
      data.section.id,
      data.field.id,
      data.field,
      this.currentSections()
    );
    this.currentSections.set(updatedSections);
    this.dynamicLayoutBuilderService.updateSections(updatedSections);

    console.log('✅ Field properties updated in dynamic-layout-builder:', data.field.label);
  }

  /**
   * Xử lý khi set permission của field
   */
  onSectionFieldPermissionSet(data: { section: Section; field: LayoutField }): void {
    const updatedSections = this.dynamicLayoutBuilderService.handleFieldPropertiesUpdate(
      data.section.id,
      data.field.id,
      data.field,
      this.currentSections()
    );
    this.currentSections.set(updatedSections);
    this.dynamicLayoutBuilderService.updateSections(updatedSections);

    console.log('✅ Field permissions updated in dynamic-layout-builder:', data.field.id);
  }

  /**
   * Xử lý khi field bị xóa
   */
  onSectionFieldDeleted(data: { section: Section; field: LayoutField }): void {
    this.onDeleteField(data.section.id, data.field.id);
  }

  /**
   * Xử lý khi quick add field từ section
   */
  onSectionQuickAddField(data: { section: Section; fieldType: string }): void {
    console.log('Quick add field to section:', data.section.id, 'field type:', data.fieldType);

    const updatedSections = this.dynamicLayoutBuilderService.handleQuickAddField(
      data.section.id,
      data.fieldType,
      this.currentSections()
    );
    this.currentSections.set(updatedSections);
    this.dynamicLayoutBuilderService.updateSections(updatedSections);
  }

}
