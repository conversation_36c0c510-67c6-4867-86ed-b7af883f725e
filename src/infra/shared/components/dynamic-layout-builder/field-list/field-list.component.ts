import { Component, Input, Output, EventEmitter, signal, Signal, computed, ElementRef, ViewChild, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import Sortable from 'sortablejs';
import { LayoutField, DragData } from '@shared/models/view/dynamic-layout-builder.model';
import { FieldItemComponent } from '../field-item/field-item.component';

/**
 * Component hiển thị danh sách fields trong một section
 * Hỗ trợ drag & drop để sắp xếp lại thứ tự fields
 */
@Component({
  selector: 'app-field-list',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatButtonModule,
    MatMenuModule,
    MatDividerModule,
    TranslateModule,
    FieldItemComponent
  ],
  templateUrl: './field-list.component.html',
  styleUrls: ['./field-list.component.scss']
})
export class FieldListComponent implements AfterViewInit, OnDestroy {

  /**
   * Reference to field grid container for SortableJS
   */
  @ViewChild('fieldGridContainer', { static: false }) fieldGridContainer!: ElementRef<HTMLElement>;

  /**
   * SortableJS instance
   */
  private sortableInstance: Sortable | null = null;

  /**
   * Danh sách fields để hiển thị
   */
  @Input() fields!: Signal<LayoutField[]>;

  /**
   * ID của section chứa field list này
   */
  @Input() sectionId!: Signal<string>;

  /**
   * Event khi thứ tự fields thay đổi
   */
  @Output() fieldsReordered = new EventEmitter<LayoutField[]>();

  /**
   * Event khi toggle required status
   */
  @Output() fieldRequiredToggled = new EventEmitter<{ field: LayoutField; isRequired: boolean }>();

  /**
   * Event khi edit properties
   */
  @Output() fieldPropertiesEdit = new EventEmitter<LayoutField>();

  /**
   * Event khi set permission
   */
  @Output() fieldPermissionSet = new EventEmitter<LayoutField>();

  /**
   * Event khi delete field
   */
  @Output() fieldDeleted = new EventEmitter<LayoutField>();

  /**
   * Event khi quick add field được click
   */
  @Output() quickAddField = new EventEmitter<string>();

  /**
   * Event khi field label thay đổi
   */
  @Output() fieldLabelChanged = new EventEmitter<{ field: LayoutField; newLabel: string }>();

  // Signals cho drag & drop state management
  showInsertionIndicator = signal<boolean>(false);
  insertionIndex = signal<number>(-1);
  insertionPosition = signal<'before' | 'after'>('before');
  isDragOver = signal<boolean>(false);
  draggedField = signal<LayoutField | null>(null);

  // Track external drag operations (from sidebar)
  isExternalDrag = false;
  isFieldDragging = false;
  private timeoutUnsetExternalDrag!: ReturnType<typeof setTimeout>;

  /**
   * Initialize SortableJS after view init
   */
  ngAfterViewInit(): void {
    this.initializeSortable();
  }

  /**
   * Cleanup SortableJS on destroy
   */
  ngOnDestroy(): void {
    if (this.sortableInstance) {
      this.sortableInstance.destroy();
      this.sortableInstance = null;
    }
  }

  /**
   * Initialize SortableJS for drag & drop functionality
   */
  private initializeSortable(): void {
    if (!this.fieldGridContainer?.nativeElement) {
      console.warn('Field grid container not found, skipping SortableJS initialization');
      return;
    }

    const options: Sortable.Options = {
      group: {
        name: 'field-lists',
        pull: true,
        put: false // Don't accept external drops via SortableJS - use HTML5 events instead
      },
      animation: 150,
      ghostClass: 'sortable-ghost',
      chosenClass: 'sortable-chosen',
      dragClass: 'sortable-drag',
      // Cho phép drag toàn bộ field-item area, không chỉ drag handle
      handle: '.field-item-container',
      onStart: (evt) => {
        console.log('🚀 SortableJS drag started:', evt);
        document.body.classList.add('field-dragging');
        this.isExternalDrag = false; // This is internal reordering
        this.isFieldDragging = true;

        // Thêm class dragging cho field item
        const fieldItem = evt.item.querySelector('.field-item-container');
        if (fieldItem) {
          fieldItem.classList.add('dragging');
        }
      },
      onEnd: (evt) => {
        console.log('🏁 SortableJS drag ended:', evt);
        document.body.classList.remove('field-dragging');
        this.isExternalDrag = false;
        this.isFieldDragging = false;


        // Xóa class dragging từ tất cả field items
        const allFieldItems = document.querySelectorAll('.field-item-container.dragging');
        allFieldItems.forEach(item => item.classList.remove('dragging'));
      },
      onUpdate: (evt) => {
        console.log('🔄 SortableJS item moved:', evt);
        this.handleSortableUpdate(evt);
      }
    };

    this.sortableInstance = new Sortable(this.fieldGridContainer.nativeElement, options);
    console.log('✅ SortableJS initialized for field grid:', this.sectionId());
  }



  /**
   * Handle when existing item is reordered within the same list
   */
  private handleSortableUpdate(evt: Sortable.SortableEvent): void {
    if (evt.oldIndex !== undefined && evt.newIndex !== undefined) {
      const fields = [...this.fields()];
      const movedField = fields.splice(evt.oldIndex, 1)[0];
      fields.splice(evt.newIndex, 0, movedField);

      // Update order for all fields
      fields.forEach((field, index) => {
        field.order = index + 1;
      });

      this.fieldsReordered.emit(fields);
      console.log('✅ Reordered field via SortableJS:', movedField.label);
    }
  }

  /**
   * Tạo drag data cho field với thông tin section
   */
  createFieldDragData(field: LayoutField): DragData {
    return {
      type: 'field',
      field: field,
      sourceSection: this.sectionId()
    };
  }











  /**
   * Xử lý khi field label thay đổi
   */
  onFieldLabelChanged(event: { field: LayoutField; newLabel: string }): void {
    // Cập nhật field trong danh sách
    const fields = [...this.fields()];
    const fieldIndex = fields.findIndex(f => f.id === event.field.id);
    if (fieldIndex !== -1) {
      fields[fieldIndex] = { ...fields[fieldIndex], label: event.newLabel };
      this.fieldsReordered.emit(fields); // Sử dụng event có sẵn để cập nhật
    }

    // Emit event cho parent component
    this.fieldLabelChanged.emit(event);
  }

  /**
   * Xử lý khi toggle required status của field
   */
  onFieldRequiredToggled(event: { field: LayoutField; isRequired: boolean }): void {
    // Cập nhật field trong danh sách
    const fields = [...this.fields()];
    const fieldIndex = fields.findIndex(f => f.id === event.field.id);
    if (fieldIndex !== -1) {
      fields[fieldIndex] = { ...fields[fieldIndex], isRequired: event.isRequired };
      this.fieldsReordered.emit(fields); // Sử dụng event có sẵn để cập nhật
    }

    // Emit event cho parent component
    this.fieldRequiredToggled.emit(event);
  }

  /**
   * Toggle required status của field (legacy method)
   */
  onToggleRequired(field: LayoutField): void {
    this.fieldRequiredToggled.emit({
      field,
      isRequired: !field.isRequired
    });
  }

  /**
   * Xử lý khi edit properties của field
   */
  onEditProperties(updatedField: LayoutField): void {
    // Cập nhật field trong danh sách local
    const fields = [...this.fields()];
    const fieldIndex = fields.findIndex(f => f.id === updatedField.id);
    if (fieldIndex !== -1) {
      fields[fieldIndex] = { ...updatedField };
      this.fieldsReordered.emit(fields); // Sử dụng event có sẵn để cập nhật
      console.log('✅ Field properties updated in field-list:', updatedField.label);
    }

    // Emit event cho parent component
    this.fieldPropertiesEdit.emit(updatedField);
  }

  /**
   * Xử lý khi set permission của field
   */
  onSetPermission(updatedField: LayoutField): void {
    // Cập nhật field trong danh sách local
    const fields = [...this.fields()];
    const fieldIndex = fields.findIndex(f => f.id === updatedField.id);
    if (fieldIndex !== -1) {
      fields[fieldIndex] = { ...updatedField };
      this.fieldsReordered.emit(fields); // Sử dụng event có sẵn để cập nhật
      console.log('✅ Field permissions updated in field-list:', updatedField.id);
    }

    // Emit event cho parent component
    this.fieldPermissionSet.emit(updatedField);
  }

  /**
   * Xóa field
   */
  onDeleteField(field: LayoutField): void {
    this.fieldDeleted.emit(field);
  }

  /**
   * Xử lý khi bắt đầu drag field từ FieldItemComponent
   */
  onFieldDragStarted(field: LayoutField): void {
    console.log('onFieldDragStarted', field);
    this.isFieldDragging = true;
    this.draggedField.set(field);
    document.body.classList.add('field-dragging');
    console.log('🚀 Field drag started from FieldItem:', field.label);
  }

  /**
   * Xử lý khi kết thúc drag field từ FieldItemComponent
   */
  onFieldDragEnded(field: LayoutField): void {
    console.log('onFieldDragEnded', field);

    this.isFieldDragging = false;
    this.draggedField.set(null);
    this.showInsertionIndicator.set(false);
    document.body.classList.remove('field-dragging');
    console.log('🏁 Field drag ended from FieldItem:', field.label);
  }

  /**
   * Xử lý khi quick add field được click
   */
  onQuickAddField(fieldType: string): void {
    this.quickAddField.emit(fieldType);
  }

  /**
   * External Drag Over Event Handler (from sidebar)
   */
  onExternalDragOver(event: DragEvent): void {
    event.preventDefault(); // Allow drop
    event.stopPropagation();

    if(this.isFieldDragging) {
      return;
    }

    if (event.dataTransfer) {
      event.dataTransfer.dropEffect = 'copy';
    }

    if(this.timeoutUnsetExternalDrag) {
      clearTimeout(this.timeoutUnsetExternalDrag);
    }

    console.log('🔄 External drag over field list', this.draggedField());

    this.isExternalDrag = true;
    // if(!this.isDragOver()) {
    //   this.isDragOver.set(true);
    // }
  }

  /**
   * External Drag Enter Event Handler (from sidebar)
   */
  onExternalDragEnter(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if(this.isFieldDragging) {
      return;
    }

    if(this.timeoutUnsetExternalDrag) {
      clearTimeout(this.timeoutUnsetExternalDrag);
    }

    console.log('🎯 External drag entered field list:', this.sectionId(), this.isExternalDrag, this.draggedField());

    this.isExternalDrag = true;
    if(!this.isDragOver()) {
      this.isDragOver.set(true);
    }
  }

  /**
   * External Drag Leave Event Handler (from sidebar)
   */
  onExternalDragLeave(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    console.log('🚪 External drag left field list', event);

    // Reset visual feedback with delay to prevent flicker
    this.timeoutUnsetExternalDrag = setTimeout(() => {
      this.isDragOver.set(false);
      this.isExternalDrag = false;
    }, 50);
  }



  /**
   * External Drop Event Handler (from sidebar)
   */
  onExternalDrop(event: DragEvent): void {
    event.preventDefault();
    event.stopPropagation();

    console.log('🎯 External drop event in field list:', this.sectionId());

    // Reset visual feedback
    this.isDragOver.set(false);
    this.isExternalDrag = false;

    // Get drag data
    if (event.dataTransfer) {
      try {
        console.log(event);
        const dragDataStr = event.dataTransfer.getData('application/json');
        if(!dragDataStr) {
          return;
        }
        const dragData = JSON.parse(dragDataStr);

        console.log('📦 External drop data:', dragData);

        if (dragData.type === 'field-type-clone' && dragData.fieldType) {
          // Create new field from field type
          const newField: LayoutField = {
            id: Date.now(), // Temporary ID
            label: dragData.fieldType.label,
            type: dragData.fieldType.type,
            isRequired: false,
            isPublic: true,
            order: this.fields().length + 1,
            constraints: dragData.fieldType.constraints || {}
          };

          // Add field to list
          const fields = [...this.fields(), newField];

          // Update order for all fields
          fields.forEach((field, index) => {
            field.order = index + 1;
          });

          // Emit event
          this.fieldsReordered.emit(fields);

          console.log('✅ Added new field from external drop:', newField.label);
        }
      } catch (error) {
        console.error('❌ Error parsing external drop data:', error);
      }
    }
  }





  /**
   * TrackBy function cho ngFor
   */
  trackByField(_index: number, field: LayoutField): number {
    return field.id;
  }

  /**
   * Lấy label hiển thị cho field type
   */
  getFieldTypeLabel(type: string): string {
    const typeMap: { [key: string]: string } = {
      'text': 'Text',
      'integer': 'Integer',
      'decimal': 'Decimal',
      'percent': 'Percent',
      'currency': 'Currency',
      'date': 'Date',
      'datetime': 'DateTime',
      'email': 'Email',
      'phone': 'Phone',
      'picklist': 'Picklist',
      'multi-picklist': 'Multi Picklist',
      'url': 'URL',
      'textarea': 'Text Area',
      'checkbox': 'Checkbox'
    };

    return typeMap[type] || type;
  }
}
