<div class="field-type-selector-container">

  <!-- Header -->
  <div class="selector-header">
    <h4>{{ 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPE_SELECTOR.TITLE' | translate }}</h4>
    <p class="selector-description">
      {{ 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPE_SELECTOR.DESCRIPTION' | translate }}
    </p>
  </div>

  <!-- Field Types List (drag source only) -->
  <div class="field-types-list"
       #fieldTypesList
       id="field-type-source">

    <!-- Basic Fields Group -->
    <div class="field-group">
      <div class="field-type-item"
           *ngFor="let fieldType of fieldTypes(); trackBy: trackByFieldType"
           draggable="true"
           [attr.data-field-type]="fieldType.type"
           (dragstart)="onDragStart($event, fieldType)"
           (dragend)="onDragEnd($event)">

        <!-- Drag handle -->
        <mat-icon class="drag-handle">drag_indicator</mat-icon>

        <!-- Field icon -->
        <mat-icon class="field-icon" [class]="'field-icon-' + fieldType.type">
          {{ getFieldIcon(fieldType.type) }}
        </mat-icon>

        <!-- Field info -->
        <div class="field-info">
          <span class="field-label">{{ fieldType.label }}</span>
          <span class="field-description">{{ fieldType.description || ('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPE_DESCRIPTIONS.DEFAULT' | translate) }}</span>
        </div>
      </div>
    </div>
  </div>
</div>
