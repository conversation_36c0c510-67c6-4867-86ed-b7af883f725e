<!-- Dynamic Layout Builder Main Container -->
<div class="dynamic-layout-builder-container">
  <!-- Header Toolbar -->
  <mat-toolbar class="layout-toolbar">
    <span class="toolbar-title">
      {{ 'DYNAMIC_LAYOUT_BUILDER.TITLE' | translate }}
    </span>
    <span class="toolbar-subtitle">
      {{ 'DYNAMIC_LAYOUT_BUILDER.SUBTITLE' | translate }}
    </span>

    <span class="spacer"></span>

    <!-- Action Buttons -->
    <div class="toolbar-actions">
      <!-- Template Selector -->
      <!-- <app-template-selector
        [templates]="templates"
        [selectedTemplate]="selectedTemplate()"
        (templateSelected)="onTemplateSelected($event)">
      </app-template-selector> -->

      <button mat-button (click)="onTogglePreview()"
              [class.active]="isPreviewMode()">
        <mat-icon>preview</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.PREVIEW.TITLE' | translate }}
      </button>

      <button mat-button (click)="onSaveLayout()"
              [disabled]="isLoading()">
        <mat-icon>save</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.ACTIONS.SAVE' | translate }}
      </button>

      <button mat-button (click)="onRestoreLayout()"
              [disabled]="isLoading()">
        <mat-icon>restore</mat-icon>
        {{ 'DYNAMIC_LAYOUT_BUILDER.ACTIONS.RESTORE' | translate }}
      </button>
    </div>
  </mat-toolbar>

  <!-- Main Content Area -->
  <div class="layout-content" [class.preview-mode]="isPreviewMode()">

    <!-- Flexbox Layout Container -->
    <div class="layout-flex-container">

      <!-- Sidebar - New Fields Panel -->
      <div class="fields-sidebar" #leftPanel>
        <!-- New Section Button -->
        <app-new-section (createSection)="onAddSection()"></app-new-section>

        <!-- Field Type Selector -->
        <app-field-type-selector
          [availableFieldTypes]="availableFieldTypes"
          (fieldTypeSelected)="onFieldTypeSelected($event)">
        </app-field-type-selector>

        <!-- Template Selector -->
        <app-template-selector
          (templateApplied)="onTemplateApplied($event)"
          (templatePreviewed)="onTemplatePreviewed($event)">
        </app-template-selector>
      </div>

      <div
        class="panel-resize-handle"
        appResizePanel
        [leftPanel]="leftPanel"
        [minWidth]="200"
        [maxWidth]="600"
        >
      </div>

      <!-- Main Layout Area -->
      <div class="layout-main-content">

        <!-- Loading Indicator -->
        <div *ngIf="isLoading()" class="loading-container">
          <mat-spinner></mat-spinner>
          <p>{{ 'DYNAMIC_LAYOUT_BUILDER.LOADING' | translate }}</p>
        </div>

        <!-- Empty State -->
        <div *ngIf="!hasAnySections() && !isLoading()" class="empty-state">
          <mat-icon class="empty-icon">dashboard</mat-icon>
          <h3>{{ 'DYNAMIC_LAYOUT_BUILDER.EMPTY_STATE.NO_SECTIONS' | translate }}</h3>
          <p>{{ 'DYNAMIC_LAYOUT_BUILDER.EMPTY_STATE.NO_SECTIONS_DESCRIPTION' | translate }}</p>
          <app-new-section (createSection)="onAddSection()"></app-new-section>
        </div>

        <!-- Sections Container -->
        <div *ngIf="hasAnySections() && !isLoading()"
             class="sections-container"
             cdkDropList
             [cdkDropListData]="currentSections()"
             (cdkDropListDropped)="onSectionDropped($event)">

          <!-- Section Components -->
          <app-section
            *ngFor="let section of currentSections(); trackBy: trackBySection"
            [section]="section"
            (sectionDeleted)="onSectionDeleted($event)"
            (sectionTitleChanged)="onSectionTitleChanged($event)"
            (fieldsReordered)="onSectionFieldsReordered($event)"
            (fieldRequiredToggled)="onSectionFieldRequiredToggled($event)"
            (fieldPropertiesEdit)="onSectionFieldPropertiesEdit($event)"
            (fieldPermissionSet)="onSectionFieldPermissionSet($event)"
            (fieldDeleted)="onSectionFieldDeleted($event)"
            (quickAddField)="onSectionQuickAddField($event)">
          </app-section>
        </div>

        <!-- Preview Panel -->
        <div *ngIf="isPreviewMode()" class="preview-panel">
          <app-preview-panel
            [sections]="currentSections">
          </app-preview-panel>
        </div>

      </div>
    </div>
  </div>
</div>
