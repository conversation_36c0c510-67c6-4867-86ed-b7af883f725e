import { Component, Input, Output, EventEmitter, signal, computed, OnChanges, SimpleChanges, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { TranslateModule } from '@ngx-translate/core';
import { Section, LayoutField } from '@shared/models/view/dynamic-layout-builder.model';
import { FieldListComponent } from '../field-list/field-list.component';
import { ConfirmModalService } from '@shared/modals/common/confirm-modal/confirm-modal.service';
import { ConfirmModalData } from '@shared/modals/common/confirm-modal/confirm-modal.component';
import { MatTooltipModule } from '@angular/material/tooltip';

/**
 * Component hiển thị một section trong Dynamic Layout Builder
 * Hỗ trợ inline editing cho tên section và quản lý fields
 */
@Component({
  selector: 'app-section',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    DragDropModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatMenuModule,
    MatDividerModule,
    TranslateModule,
    FieldListComponent,
    MatTooltipModule
  ],
  templateUrl: './section.component.html',
  styleUrls: ['./section.component.scss']
})
export class SectionComponent implements OnChanges {

  /**
   * Section data
   */
  @Input() section: Section = {
    id: '',
    title: '',
    fields: []
  };

  /**
   * Signal cho section data để sử dụng trong template
   */
  sectionSignal = signal<Section>({
    id: '',
    title: '',
    fields: []
  });

  /**
   * Event khi section bị xóa
   */
  @Output() sectionDeleted = new EventEmitter<Section>();

  /**
   * Event khi tên section thay đổi
   */
  @Output() sectionTitleChanged = new EventEmitter<{ section: Section; newTitle: string }>();



  /**
   * Event khi thứ tự fields thay đổi
   */
  @Output() fieldsReordered = new EventEmitter<{ section: Section; fields: LayoutField[] }>();

  /**
   * Event khi toggle required status của field
   */
  @Output() fieldRequiredToggled = new EventEmitter<{ section: Section; field: LayoutField; isRequired: boolean }>();

  /**
   * Event khi edit properties của field
   */
  @Output() fieldPropertiesEdit = new EventEmitter<{ section: Section; field: LayoutField }>();

  /**
   * Event khi set permission của field
   */
  @Output() fieldPermissionSet = new EventEmitter<{ section: Section; field: LayoutField }>();

  /**
   * Event khi field bị xóa
   */
  @Output() fieldDeleted = new EventEmitter<{ section: Section; field: LayoutField }>();

  /**
   * Event khi quick add field được click
   */
  @Output() quickAddField = new EventEmitter<{ section: Section; fieldType: string }>();

  /**
   * Signal để quản lý trạng thái đang edit title
   */
  isEditingTitle = signal(false);

  /**
   * Tên đang được edit
   */
  editingTitle = '';

  /**
   * Signal cho fields để truyền vào FieldListComponent
   */
  fieldsSignal = computed(() => this.sectionSignal().fields);

  /**
   * Signal cho section ID để truyền vào FieldListComponent
   */
  sectionIdSignal = computed(() => this.sectionSignal().id);

  /**
   * Computed để check có fields hay không
   */
  hasFields = computed(() => this.sectionSignal().fields.length > 0);

  /**
   * Signal để quản lý trạng thái collapse/expand của section
   */
  isCollapsed = signal(false);

  /**
   * Inject ConfirmModalService để hiển thị modal xác nhận
   */
  private confirmModalService = inject(ConfirmModalService);

  /**
   * Lifecycle hook - cập nhật sectionSignal khi input thay đổi
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['section'] && changes['section'].currentValue) {
      this.sectionSignal.set(changes['section'].currentValue);
    }
  }

  /**
   * Bắt đầu edit title
   */
  startEditTitle(): void {
    this.editingTitle = this.sectionSignal().title;
    this.isEditingTitle.set(true);

    // Focus vào input sau khi render
    setTimeout(() => {
      const input = document.querySelector('.title-input input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    });
  }

  /**
   * Lưu title mới
   */
  saveTitle(): void {
    if (this.editingTitle.trim() && this.editingTitle.trim() !== this.sectionSignal().title) {
      this.sectionTitleChanged.emit({
        section: this.sectionSignal(),
        newTitle: this.editingTitle.trim()
      });
    }
    this.isEditingTitle.set(false);
  }

  /**
   * Hủy edit title
   */
  cancelEditTitle(): void {
    this.editingTitle = '';
    this.isEditingTitle.set(false);
  }

  /**
   * Toggle collapse/expand section
   */
  toggleCollapse(): void {
    this.isCollapsed.set(!this.isCollapsed());
  }

  /**
   * Xóa section với xác nhận
   */
  async onDeleteSection(): Promise<void> {
    const confirmData: ConfirmModalData = {
      title: 'DYNAMIC_LAYOUT_BUILDER.SECTION.DELETE_CONFIRM_TITLE',
      message: 'DYNAMIC_LAYOUT_BUILDER.SECTION.DELETE_CONFIRM_MESSAGE',
      confirmText: 'DYNAMIC_LAYOUT_BUILDER.SECTION.DELETE',
      cancelText: 'COMMON.CANCEL',
      confirmColor: 'warn'
    };

    try {
      const confirmed = await this.confirmModalService.confirm(confirmData);
      if (confirmed) {
        this.sectionDeleted.emit(this.sectionSignal());
      }
    } catch (error) {
      console.error('Lỗi khi xác nhận xóa section:', error);
    }
  }



  /**
   * Xử lý khi thứ tự fields thay đổi
   */
  onFieldsReordered(fields: LayoutField[]): void {
    this.fieldsReordered.emit({
      section: this.sectionSignal(),
      fields
    });
  }

  /**
   * Xử lý khi toggle required status
   */
  onFieldRequiredToggled(data: { field: LayoutField; isRequired: boolean }): void {
    this.fieldRequiredToggled.emit({
      section: this.sectionSignal(),
      field: data.field,
      isRequired: data.isRequired
    });
  }

  /**
   * Xử lý khi edit properties
   */
  onFieldPropertiesEdit(field: LayoutField): void {
    this.fieldPropertiesEdit.emit({
      section: this.sectionSignal(),
      field
    });
  }

  /**
   * Xử lý khi set permission
   */
  onFieldPermissionSet(field: LayoutField): void {
    this.fieldPermissionSet.emit({
      section: this.sectionSignal(),
      field
    });
  }

  /**
   * Xử lý khi field bị xóa
   */
  onFieldDeleted(field: LayoutField): void {
    this.fieldDeleted.emit({
      section: this.sectionSignal(),
      field
    });
  }

  /**
   * Xử lý khi quick add field được click
   */
  onQuickAddField(fieldType: string): void {
    this.quickAddField.emit({
      section: this.sectionSignal(),
      fieldType
    });
  }
}
