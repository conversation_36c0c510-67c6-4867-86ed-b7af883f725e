import { Directive, ElementRef, Input, HostListener, OnInit, OnDestroy, inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

// Dịch vụ lưu trữ localStorage
@Injectable({ providedIn: 'root' })
export class StorageService {
  setItem(key: string, value: string): void {
    localStorage.setItem(key, value);
  }

  getItem(key: string): string | null {
    return localStorage.getItem(key);
  }
}

@Directive({
  selector: '[appResizePanel]',
  standalone: true,
})
export class ResizePanelDirective implements OnInit, OnDestroy {
  // Inject các dịch vụ cần thiết
  private el = inject(ElementRef);
  private translateService = inject(TranslateService);
  private storageService = inject(StorageService);

  // Input để nhận tham chiếu đến panel trái
  @Input() leftPanel!: ElementRef<HTMLElement> | HTMLElement;

  // Input để giới hạn chiều rộng tối thiểu và tối đa
  @Input() minWidth: number = 200;
  @Input() maxWidth: number = 600;

  // BehaviorSubject để quản lý chiều rộng panel trái
  private panelWidthSubject = new BehaviorSubject<number>(this.minWidth);
  panelWidth$: Observable<number> = this.panelWidthSubject.asObservable();

  // Key lưu trữ trong localStorage
  private storageKey = 'panel_width';

  // Biến theo dõi trạng thái kéo
  private isDragging = false;

  ngOnInit(): void {
    // Khởi tạo chiều rộng từ localStorage hoặc minWidth
    const savedWidth = this.storageService.getItem(this.storageKey);
    const initialWidth = savedWidth ? parseInt(savedWidth, 10) : this.minWidth;
    this.setPanelWidth(initialWidth);

    // Thêm tooltip dịch từ i18n
    this.translateService.get('RESIZE_PANEL.TOOLTIP').subscribe(tooltip => {
      this.el.nativeElement.setAttribute('title', tooltip);
    });
  }

  // Bắt sự kiện mousedown để bắt đầu kéo
  @HostListener('mousedown', ['$event'])
  onMouseDown(event: MouseEvent): void {
    this.isDragging = true;
    event.preventDefault();
  }

  // Bắt sự kiện mousemove để cập nhật chiều rộng
  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    if (!this.isDragging) return;

    // Lấy chiều rộng mới từ vị trí chuột
    const newWidth = event.clientX;

    // Giới hạn chiều rộng trong khoảng minWidth và maxWidth
    if (newWidth >= this.minWidth && newWidth <= this.maxWidth) {
      this.setPanelWidth(newWidth);
    }
  }

  // Bắt sự kiện mouseup để kết thúc kéo
  @HostListener('document:mouseup')
  onMouseUp(): void {
    this.isDragging = false;
  }

  // Cập nhật chiều rộng panel trái và lưu vào localStorage
  private setPanelWidth(width: number): void {
    const leftPanelEl = this.leftPanel instanceof HTMLElement ? this.leftPanel : this.leftPanel.nativeElement;
    leftPanelEl.style.width = `${width}px`;
    this.panelWidthSubject.next(width);
    this.storageService.setItem(this.storageKey, width.toString());
  }

  ngOnDestroy(): void {
    // Hủy subscription nếu có
    this.panelWidthSubject.complete();
  }
}