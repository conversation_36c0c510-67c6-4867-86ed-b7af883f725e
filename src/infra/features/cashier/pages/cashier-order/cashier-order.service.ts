import { Injectable, signal } from '@angular/core';
import { InitDataStore } from '@core/store/init_data.store';
import { UpdateDataStore } from '@core/store/update_data.store';
import { PosBackendCashierData, PosCategorizedProducts, PosInvoice, PosInvoiceItem, PosProduct } from 'salehub_shared_contracts';
import { createNotifiableSignal } from '@shared/utils/signal.util';
import { buildFullPosOrder, placeholderOrder, testCashierOrder } from '@features/cashier/utils/order.util';
import { DeliveryService } from '../../services/delivery.service';
import { CashierOrderCustomerService } from './cashier-order.customer.service';
import { CashierOrderDeliveryService } from './cashier-order.delivery.service';

@Injectable()
export class CashierOrderService {
  products = signal<PosCategorizedProducts>([]);
  toppingProducts = signal<PosCategorizedProducts>([]);
  deliveryCompanies = signal<PosBackendCashierData['deliveryCompanies']>([]);
  ingredients = signal<PosBackendCashierData['ingredients']>([]);
  order = createNotifiableSignal<PosInvoice>(null as any);
  deliveryFee = signal<{[companyId: string]: number}>({});

  customerService!: CashierOrderCustomerService;
  deliveryService!: CashierOrderDeliveryService;

  constructor(
    private updateDataStore: UpdateDataStore,
    private initStore: InitDataStore,
    private _deliveryService: DeliveryService
  ) {
    this.customerService = new CashierOrderCustomerService(this, this.initStore);
    this.deliveryService = new CashierOrderDeliveryService(this, this._deliveryService);
  }

  rebuildBackendData(data: PosBackendCashierData) {
    // const allStr = 'Tất cả';
    const { productCategorySorted, toppingCategorySorted } = data;
    const build: {
      [category: string]: Array<PosProduct>
    } = {};

    productCategorySorted.forEach(cat => (build[cat] = []));
    toppingCategorySorted.forEach(cat => (build[cat] ||= []));

    data.products.forEach(product => {
      if(build[product.category]) {
        build[product.category].push(product);
      }
    });

    const products: PosCategorizedProducts = [];
    const toppingProducts: PosCategorizedProducts = [];

    productCategorySorted.forEach(cat => {
      products.push({
        category: cat,
        items: JSON.parse(JSON.stringify(build[cat]))
      });
    });

    toppingCategorySorted.forEach(cat => {
      toppingProducts.push({
        category: cat,
        items: JSON.parse(JSON.stringify(build[cat]))
      });
    });

    const { ingredients } = data;
    const { deliveryCompanies } = data;
    let order: PosInvoice;

    if(data.invoice) {
      order = data.invoice;
    } else {
      order = placeholderOrder(this.updateDataStore.getData()?.currentShift?.shiftId);
      // order = testCashierOrder();
    }

    this.products.set(products);
    this.toppingProducts.set(toppingProducts);
    this.ingredients.set(ingredients);
    this.deliveryCompanies.set(deliveryCompanies);
    this.order.set(order);
  }

  updateOrderItems(result: {items: PosInvoiceItem[], products: PosCategorizedProducts}) {
    this.order.mutate(order => {
      order.items = result.items;
      buildFullPosOrder(order);
      return order;
    });
    this.products.set(result.products);
  }

  updateDiningOption(option: PosInvoice['diningOption']) {
    this.order.mutate(order => {
      order.diningOption = option;
      return order;
    });
  }

  updateDeliveryOption(option: PosInvoice['delivery']['selfArrive']) {
    this.order.mutate(order => {
      order.delivery ||= {} as any;
      order.delivery.selfArrive = option;
      return order;
    });
  }

  reset() {
    const order = placeholderOrder(this.updateDataStore.getData()?.currentShift?.shiftId);
    buildFullPosOrder(order);

    this.order.set(order);
  }
}
