import { DeliveryService } from '@features/cashier/services/delivery.service';
import { CashierOrderService } from './cashier-order.service';

export class CashierOrderDeliveryService {
  service!: CashierOrderService;
  deliveryService!: DeliveryService;

  constructor(service: CashierOrderService, deliveryService: DeliveryService) {
    this.service = service;
    this.deliveryService = deliveryService;
  }

  updateInvoiceDeliveryFee() {
    const order = this.service.order();

    if(
      order.delivery.distance &&
      order.delivery.company?.id &&
      this.service.deliveryFee()[order.delivery.company.id]
    ) {
      order.delivery.fee = this.service.deliveryFee()[order.delivery.company.id];
      this.service.order.update(prevInvoice => order);
    }
  }

  updateDeliveryFee(distance: number | null | undefined) {
    const order = this.service.order();

    if(distance) {
      order.delivery.distance = distance;
      this.service.order.update(prevInvoice => order);
    }

    if(order.delivery.distance) {
      this.service.deliveryFee.set(this.deliveryService.getAllDeliveryFees(
        this.service.deliveryCompanies(),
        order.delivery.distance
      ));
    } else {
      this.service.deliveryFee.set({});
    }

    const deliveryFee = this.service.deliveryFee();
    const deliveryCompanies = this.service.deliveryCompanies().sort(
      (a, b) => (deliveryFee[a.id] ?? 0) - (deliveryFee[b.id] ?? 0)
    );

    this.service.deliveryCompanies.set(deliveryCompanies);

    this.updateInvoiceDeliveryFee();
  }

  clearDeliveryFee() {
    this.service.order.update(order => {
      order.delivery.fee = undefined;
      order.delivery.distance = undefined;
      return order;
    });
  }

  setDeliveryCompany(companyId: string) {
    if(!companyId) {
      return;
    }

    const company = this.service.deliveryCompanies().find(c => c.id === companyId);
    if(company) {
      this.service.order.update(order => {
        order.delivery.company = company;
        return order;
      });
    }
  }
}
