@use '../../../../shared/styles/_variable' as *;

/** fix de sticky work*/
.mat-drawer-container,
.mat-drawer-content {
  overflow: visible;
}

.select-dining {
  padding: 20px 0;

  mat-radio-button {
    position: relative;
    border: 1px solid #dadada;
    width: calc(50% - 10px);
    min-height: 40px;
    border-radius: 3px;
    margin-right: 10px;

    &:nth-child(2) {
      margin-right: 0;
      margin-left: 10px;
    }

    &.mat-mdc-radio-checked {
      background-color: #03a9f4;
      border-color: #03a9f4;

      .mdc-label {
        color: #fff;
      }
    }

    .mdc-form-field {
      display: flex !important;
    }
    .mdc-label {
      flex-grow: 1;
    }

    img {
      position: absolute;
      top: 50%;
      right: 1px;
      display: block;
      height: 30px;
      margin-top: -15px;
    }
  }
}

.invoice {
  .mat-horizontal-stepper-header-container,
  .select-dining,
  .invoice-header {
    background-color: #fff;
  }


  /** fix de sticky work*/
  .mat-horizontal-content-container {
    overflow: visible;
  }



  .mat-stepper-vertical, .mat-stepper-horizontal {
    background: none;
  }

  .mat-stepper-horizontal {
    padding-bottom: 50px;
  }

  .mat-horizontal-stepper-header-container {
    height: 40px;
  }
  .mat-step-header {
    height: 100%;
  }
  .mat-horizontal-content-container {
    padding: 0 !important;
  }
  .mat-horizontal-stepper-header .mat-step-icon {
    display: none;
  }

  .mat-step-label {
    opacity: .5;

    &.mat-step-label-selected {
      color: #000;
      opacity: 1;
    }
  }
}


app-cashier-order-edit-item {
  .calc-ing-container {
    opacity: .3;
    pointer-events: none;

    &.active {
      opacity: 1;
      pointer-events: all;
    }
  }
  .ing-container {
    margin-bottom: 80px;
  }
  .ing-item {
    &.active {
      background: #00b0ff;
      color: #fff;
      border: 1px solid #2dbeff !important;

      .text-muted {
        color: #fff !important
      }
    }
  }
  .ing-item-form {
    .mat-mdc-form-field-infix {
      padding: 8px 0 !important;
      line-height: 1;
      min-height: auto;
    }
    .mdc-text-field__input {
      font-size: 15px;
      text-align: right;
    }
    .mat-mdc-form-field-subscript-wrapper {
      height: 0;
    }
  }

  .save {
    position: fixed;
    bottom: 20px;
    width: inherit;

    .btn {
      height: 60px;
    }
  }
}




@media screen and (min-width: #{$breakpointMinHorizontalWidth}px) {
  .mat-drawer-container,
  .mat-drawer-content {
    overflow: auto;
  }
}
