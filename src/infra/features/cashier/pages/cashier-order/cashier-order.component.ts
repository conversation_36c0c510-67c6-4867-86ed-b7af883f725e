import { Component, ViewEncapsulation, ViewChild, ChangeDetectionStrategy, effect, signal } from '@angular/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatStepper, MatStepperModule } from '@angular/material/stepper';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSelectModule } from '@angular/material/select';
import { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { Router, ActivatedRoute } from '@angular/router';
import { RouterDataService } from '@core/services/router.resolver.service';
import { scrollTop } from '@shared/utils';
import { PosBackendCashierData, PosCategorizedProducts, PosInvoiceItem } from 'salehub_shared_contracts';
import { openOrderSummary } from '@features/cashier/utils/order.util';
import { UpdateDataStore } from '@core/store/update_data.store';
import { ShiftCheckComponent } from '@features/cashier/components/shift-check/shift-check.component';
import { CashierOrderService } from '@features/cashier/pages/cashier-order/cashier-order.service';
import { CashierOrderCustomerComponent } from '@features/cashier/components/cashier-order/order-customer/order-customer.component';
import { CashierOrderDiningComponent } from '@features/cashier/components/cashier-order/order-dining/order-dining.component';
import { CashierOrderProductListComponent } from '@features/cashier/components/cashier-order/order-product-list/order-product-list.component';

@Component({
  selector: 'app-cashier',
  standalone: true,
  imports: [
    MatRadioModule,
    MatStepperModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatAutocompleteModule,
    MatSelectModule,
    MatBottomSheetModule,
    ShiftCheckComponent,
    CashierOrderCustomerComponent,
    CashierOrderDiningComponent,
    CashierOrderProductListComponent
  ],
  providers: [
    RouterDataService,
    CashierOrderService
  ],
  templateUrl: './cashier-order.component.html',
  styleUrl: './cashier-order.component.scss',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class CashierOrderComponent {
  @ViewChild('productList') private productList!: CashierOrderProductListComponent;
  @ViewChild('stepper') private myStepper!: MatStepper;
  @ViewChild('customer') private customerCom!: CashierOrderCustomerComponent;

  isHiddenCashier = signal<boolean>(false);
  forceShowCashier = false;

  order!: CashierOrderService['order'];
  products!: CashierOrderService['products'];
  toppingProducts!: CashierOrderService['toppingProducts'];

  constructor(
    private routerData: RouterDataService,
    private _bottomSheet: MatBottomSheet,
    private router: Router,
    private route: ActivatedRoute,
    private updateDataStore: UpdateDataStore,
    private cashierOrderService: CashierOrderService
  ) {
    effect(() => {
      if(!this.forceShowCashier) {
        this.isHiddenCashier.set(!this.updateDataStore.getData()?.currentShift?.shiftId);
      }
    });

    this.order = this.cashierOrderService.order;
    this.products = this.cashierOrderService.products;
    this.toppingProducts = this.cashierOrderService.toppingProducts;
  }

  ngOnInit() {
    this.routerData.getData().subscribe((data: { cashier: PosBackendCashierData }) => {
      this.cashierOrderService.rebuildBackendData(data.cashier);
    });

    this.isHiddenCashier.set(!this.updateDataStore.getData()?.currentShift?.shiftId);
  }

  showCashier(e: Event) {
    e.preventDefault();

    this.forceShowCashier = true;
    this.isHiddenCashier.set(false);
  }

  updateOrderItems(result: {items: PosInvoiceItem[], products: PosCategorizedProducts}) {
    this.cashierOrderService.updateOrderItems(result);
    this.myStepper.next();
  }

  summarize() {
    openOrderSummary(this._bottomSheet, {
      ...this.cashierOrderService.order()
    })
      .afterDismissed()
      .subscribe({
        next: (submited: boolean) => {
          if(submited) {
            this.cashierOrderService.reset();

            this.myStepper.reset();
            this.customerCom.clearCustomer();
            this.reset();

            this.router.navigate(['.'], { relativeTo: this.route, queryParams: {} });

            scrollTop();
          }
        }
      })
    ;
  }

  reset() {
    this.productList.reset();
  }
}
