import { ChangeDetectionStrategy, Component, ElementRef, EventEmitter, Output, ViewChild, signal } from '@angular/core';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { debounceTime, Subject, switchMap } from 'rxjs';
import { FormatStringPipe } from '@shared/pipes/format_str.pipe';
import { CustomerAutocompleteRecord, CustomerAutocompleteResult } from 'salehub_shared_contracts';
import { formatPhoneNumber, hideKeyboard } from '@shared/utils';
import { CashierOrderCustomerService } from '@features/cashier/components/cashier-order/order-customer/order-customer.service';
import { CashierOrderService } from '@features/cashier/pages/cashier-order/cashier-order.service';

@Component({
  selector: 'app-cashier-order-customer-selection',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatAutocompleteModule,
    FormatStringPipe
  ],
  templateUrl: './order-customer-selection.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class CashierOrderCustomerSelectionComponent {
  @Output() selectedCustomerEvent = new EventEmitter<boolean>();

  @ViewChild('searchCustomerInput') searchCustomerInput!: ElementRef<any>;

  private searchTerms = new Subject<string>();

  searchResultsSubscribedArray = signal<(CustomerAutocompleteResult | null)>(null);
  isAutocompleteNumericKeyboard = signal<boolean>(true);
  order!: CashierOrderService['order'];

  constructor(
    private customerService: CashierOrderCustomerService,
    private cashierOrderService: CashierOrderService
  ) {
    this.order = this.cashierOrderService.order;
  }

  ngOnInit() {
    this.searchTerms.pipe(
      // wait 300ms after each keystroke before considering the term
      debounceTime(300),

      // ignore new term if same as previous term -- commented this one out because
      // if a user went back and used delete to go back to their previous query, this
      // filter would consider it duplicate and not pass it on
      // distinctUntilChanged(),

      // switch to new search observable each time the term changes
      switchMap((term: string) => this.customerService.search(term))
    ).subscribe((value) => {
      // console.log(value);
      this.searchResultsSubscribedArray.set(value);
    });
  }

  onModelChange(text: string) {
    this.searchTerms.next(text);
  }

  clearSelectedValue() {
    this.cashierOrderService.customerService.clearCustomer();
  }

  selectCustomer($event: MatAutocompleteSelectedEvent) {
    const record: CustomerAutocompleteRecord = $event.option.value;
    this.cashierOrderService.customerService.selectCustomer(record);

    hideKeyboard(this.searchCustomerInput?.nativeElement);
    this.selectedCustomerEvent.emit();
  }

  displayAutoCompleteInput(data: CustomerAutocompleteRecord): string {
    if(data) {
      if(data.text) {
        return data.text;
      }

      if(data.phoneNumber) {
        if(data.name) {
          // console.log(data.phoneNumber, formatPhoneNumber(data.phoneNumber));
          return `${data.name} - ${formatPhoneNumber(data.phoneNumber)}`;
        }

        return data.phoneNumber;
      }
    }

    return '';
  }

  toggleAutocompleteNumericKeyboard() {
    this.isAutocompleteNumericKeyboard.set(!this.isAutocompleteNumericKeyboard());
  }
}
