import { Component, ViewEncapsulation, Output, EventEmitter, ViewChild, computed, ChangeDetectionStrategy } from '@angular/core';
import { MatRadioModule } from '@angular/material/radio';
import { MatStepperModule } from '@angular/material/stepper';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatSelectModule } from '@angular/material/select';
import { InputAddressComponent } from '@shared/components/input/input-place/components/input-address/input-address.component';
import { Place, PosInvoice } from 'salehub_shared_contracts';
import { isNumber, isValidVietnamesePhoneNumber } from '@shared/utils';
import { CashierOrderService } from '@features/cashier/pages/cashier-order/cashier-order.service';
import { CashierOrderCustomerSelectionComponent } from './order-customer-selection/order-customer-selection.component';
import { CashierOrderDeliveryComponent } from '../order-delivery/order-delivery.component';

@Component({
  selector: 'app-cashier-order-customer',
  standalone: true,
  imports: [
    MatRadioModule,
    MatStepperModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatAutocompleteModule,
    MatSelectModule,
    CashierOrderCustomerSelectionComponent,
    CashierOrderDeliveryComponent,
    InputAddressComponent
  ],
  templateUrl: './order-customer.component.html',
  styleUrl: './order-customer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None
})

export class CashierOrderCustomerComponent {
  @Output() customerEvent = new EventEmitter<boolean>();

  @ViewChild(CashierOrderCustomerSelectionComponent) customerSelection!: CashierOrderCustomerSelectionComponent;
  @ViewChild(CashierOrderDeliveryComponent) delivery!: CashierOrderDeliveryComponent;

  order!: CashierOrderService['order'];

  hasNextButton = computed(() => this._hasNextButton(this.order()));
  hasGenderError = computed(() => !this.order().customer.gender);
  hasPhoneError = computed(() => {
    console.log(this.order().customer?.phoneNumber, !isValidVietnamesePhoneNumber(this.order().customer?.phoneNumber as string));
    return !isValidVietnamesePhoneNumber(this.order().customer?.phoneNumber as string);
  });

  constructor(private cashierOrderService: CashierOrderService) {
    this.order = this.cashierOrderService.order;
  }

  onSelectedAddress(place: Place) {
    this.cashierOrderService.customerService.updateInvoiceAddress(place);
    this.delivery?.updateDeliveryFeeOutside?.(place.distance?.distance);
  }

  onChangingAddress() {
    this.cashierOrderService.customerService.removeInvoiceAddress();
    this.delivery?.updateDeliveryFeeOutside?.(undefined);
  }

  clearValue(name: string) {
    this.cashierOrderService.customerService.clearCustomerValue(name as keyof PosInvoice['customer']);
  }

  submitCustomer() {
    this.cashierOrderService.customerService.cleanCustomerValue();
    this.customerEvent.emit(true);
  }

  clearCustomer() {
    this.customerSelection.clearSelectedValue();
  }

  updateDeliveryOption(option: PosInvoice['delivery']['selfArrive']) {
    this.cashierOrderService.updateDeliveryOption(option);
  }

  _hasNextButton(order: PosInvoice) {
    if(order.diningOption === 'eatin') {
      return true;
    }
    if(order.diningOption === 'takeaway' && order.delivery?.selfArrive) {
      return true;
    }
    return !!(
      isValidVietnamesePhoneNumber(order.customer?.phoneNumber as string) &&
      order.customer.address?.fullAddress &&
      order.customer.gender &&
      order.delivery.company.id &&
      order.delivery.fee &&
      isNumber(order.delivery.fee)
    )
    ;
  }
}
