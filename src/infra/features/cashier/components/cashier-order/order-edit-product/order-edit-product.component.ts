import { ChangeDetectionStrategy, Component, Inject, signal, computed } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { PosInvoiceItem } from 'salehub_shared_contracts';

@Component({
  selector: 'app-cashier-order-edit-item',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule
  ],
  templateUrl: './order-edit-product.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CashierOrderEditProductComponent {
  originItem!: PosInvoiceItem;
  name = signal<string>('');
  price = signal<string>('');
  totalCost = signal<string>('');
  note = signal<string>('');

  hasButton = computed(() => Number(this.totalCost()) && Number(this.price() && !!this.name()));

  constructor(
    public dialogRef: MatDialogRef<CashierOrderEditProductComponent>,
    @Inject(MAT_DIALOG_DATA) public data: {
      invoiceItem: PosInvoiceItem
    }
  ) {
    this.originItem = JSON.parse(JSON.stringify(data.invoiceItem));
    this.name.set(data.invoiceItem.name);
    this.price.set(String(data.invoiceItem.price));
    this.totalCost.set(String(data.invoiceItem.totalCost));
    this.note.set(data.invoiceItem.note ?? '');
  }

  submit() {
    const totalCost = Number(this.totalCost());
    const price = Number(this.price());
    let isCustomItem = false;

    if(
      !this.originItem.isCustomItem &&
        (
          this.originItem.totalCost !== totalCost ||
          this.originItem.name !== this.name() ||
          this.originItem.price !== price
        )
    ) {
      isCustomItem = true;
    }

    this.dialogRef.close({
      _id: !isCustomItem ? this.originItem._id : 'custom',
      isCustomItem,
      totalCost,
      price,
      name: this.name(),
      note: this.note()
    });
  }
}
