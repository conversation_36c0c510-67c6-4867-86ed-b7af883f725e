import { ChangeDetectionStrategy, Component, computed, signal } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { InitDataStore } from '@core/store/init_data.store';
import { CashierOrderService } from '@features/cashier/pages/cashier-order/cashier-order.service';
import { InputFloatComponent } from '@shared/components/input/input-float/input-float.component';
import { FormatStringPipe } from '@shared/pipes/format_str.pipe';
import { getGoogleMapDirectionUrl, isNumber } from '@shared/utils';
import { Coordinates, PosInvoice } from 'salehub_shared_contracts';

@Component({
  selector: 'app-cashier-order-delivery',
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    FormatStringPipe,
    InputFloatComponent
  ],
  templateUrl: './order-delivery.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CashierOrderDeliveryComponent {
  inputDeliveryCompanyId!: string | undefined;
  deliveryDistance = signal<string>('');

  deliveryCompanies: CashierOrderService['deliveryCompanies'];
  deliveryFee: CashierOrderService['deliveryFee'];
  order: CashierOrderService['order'];

  hasDeliveryFeeError = computed(() => this._hasDeliveryFeeError(this.order()));
  googleMapUrl = computed(() => this._googleMapUrl(this.order()));

  constructor(
    private initStore: InitDataStore,
    private cashierOrderService: CashierOrderService
  ) {
    this.order = this.cashierOrderService.order;
    this.deliveryCompanies = this.cashierOrderService.deliveryCompanies;
    this.deliveryFee = this.cashierOrderService.deliveryFee;
  }

  ngOnInit() {
    this.inputDeliveryCompanyId = this.order().delivery.company.id;

    if(this.order().delivery?.distance) {
      this.deliveryDistance.set(String(this.order().delivery.distance));
      this.cashierOrderService.deliveryService.updateDeliveryFee(this.order().delivery.distance);
    }
  }

  changeDeliveryCompany() {
    this.cashierOrderService.deliveryService.setDeliveryCompany(this.inputDeliveryCompanyId as string);
    this.cashierOrderService.deliveryService.updateInvoiceDeliveryFee();
  }

  updateDeliveryFee(distance: number | null) {
    this.cashierOrderService.deliveryService.updateDeliveryFee(distance);
  }

  updateDeliveryFeeOutside(distance: number | undefined) {
    if(distance) {
      this.deliveryDistance.set(String(this.order().delivery.distance));
      this.cashierOrderService.deliveryService.updateDeliveryFee(distance);
    } else {
      this.cashierOrderService.deliveryService.clearDeliveryFee();
      this.deliveryDistance.set('');
      this.cashierOrderService.deliveryService.updateDeliveryFee(0);
    }
  }

  _hasDeliveryFeeError(order: PosInvoice) {
    return order.diningOption === 'takeaway' &&
     !order.delivery?.selfArrive &&
     !isNumber(order.delivery.fee)
    ;
  }

  _googleMapUrl(order: PosInvoice) {
    if(order.customer?.address?.lat &&
      order.customer?.address?.lng &&
      this.initStore.getData()?.store?.addressInfo?.lat &&
      this.initStore.getData()?.store?.addressInfo?.lng
    ) {
      return getGoogleMapDirectionUrl(
        this.initStore.getData()?.store?.addressInfo as Coordinates,
        order.customer.address as Coordinates
      );
    }

    return '';
  }
}
