import { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule } from '@angular/forms';
import { PosInvoice } from 'salehub_shared_contracts';
import { CashierOrderService } from '@features/cashier/pages/cashier-order/cashier-order.service';

@Component({
  selector: 'app-cashier-order-dining',
  standalone: true,
  imports: [
    MatRadioModule,
    FormsModule
  ],
  templateUrl: './order-dining.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CashierOrderDiningComponent {
  order!: CashierOrderService['order'];

  constructor(private cashierOrderService: CashierOrderService) {
    this.order = this.cashierOrderService.order;
  }

  updateDiningOption(option: PosInvoice['diningOption']) {
    this.cashierOrderService.updateDiningOption(option);
  }
}
