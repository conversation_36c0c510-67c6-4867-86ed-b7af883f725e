import { PosCategorizedProducts, PosInvoiceItem, PosProduct } from 'salehub_shared_contracts';
import { insertItemAtIndex } from '@shared/utils';
import { Injectable, signal } from '@angular/core';

@Injectable()
export class CashierOrderProductService {
  selectedItems = signal<PosInvoiceItem[]>([]);
  products = signal<PosCategorizedProducts>([]);

  buildProductOnViewInit(selectedItems: PosInvoiceItem[], products: PosCategorizedProducts, rebuildProducts: boolean) {
    // console.log('buildProductOnViewInit', JSON.parse(JSON.stringify(selectedItems)), JSON.parse(JSON.stringify(products)));

    selectedItems ||= [];
    /**
     * custom item, options item
     * phải add thêm vào product
     */
    selectedItems.forEach(item => {
      if((item.options && item.options?.length > 0) || item.isCustomItem) {
        const product: PosProduct = {
          _id: item._id,
          price: item.price,
          cost: item.totalCost,
          name: item.name,
          category: this.getMainProductCategoryName(products),
          active: true,
          selectedItem: item,
          editable: true,
          canChooseOptions: true
        };

        if(item.options) {
          this.buildProductWithOptions(product, item);
        }

        this.getMainProductItems(products).unshift(product);
      }
    });

    if(rebuildProducts) {
    /**
     * nay dat duoi
     */
      products.forEach(cat => {
        cat.items.forEach(product => {
          const item = this.getItem(selectedItems, product)?.item;

          if(item) {
            product.active = true;
            product.selectedItem = item;
          }
        });
      });
    }

    this.selectedItems.set(selectedItems);
    this.products.set(products);

    // console.log('product sau khi build', JSON.parse(JSON.stringify(products)));
  }

  cloneProduct(
    product: PosProduct,
    productList: PosProduct[],
    productIndex: number,
    productCategorizedIndex: number
  ) {
    const newProduct: PosProduct = {
      _id: product._id,
      price: product.price,
      cost: product.cost,
      name: product.name,
      category: product.category,
      editable: product.editable,
      canChooseOptions: product.canChooseOptions
    };

    /**
     * ở đây phải tạo reference mới không là lỗi
     * ko update ui
     */
    this.products.update(products => {
      insertItemAtIndex(products[productCategorizedIndex].items, productIndex + 1, newProduct);
      return [...products];
    });

    // console.log('cloned products', this.products());
  }

  buildItemOptions(item: PosInvoiceItem) {
    /**
     * lưu ý về số lượng trong options
     * ví dụ 1 người gọi 2 suất xôi thập cẩm thêm 1 trứng ốp
     * thì khi tính subtotal của options phải là 2 trứng ốp
     *
     * nếu thêm gọi 2 suất xôi thập cẩm thêm 3 trứng ốp
     * thì khi tính subtotal của options phải là 6 trứng ốp
     * => subtotal = option.quantity * item.quantity * option.price
     */
    if(item.options && item.options.length > 0) {
      item.options = item.options.map(opt => {
        opt.subTotal = opt.quantity * item.quantity * opt.price;
        opt.total = opt.subTotal - (opt.discount ?? 0);
        return opt;
      });
    }
  }

  buildProductWithOptions(product: PosProduct, item: PosInvoiceItem) {
    if(!item.options) {
      delete product.optionProducts;
      product.name = item.name;
    } else {
      product.optionProducts = item.options.map(opt => ({
        _id: opt._id,
        price: opt.price,
        cost: opt.totalCost,
        name: opt.name,
        category: this.getMainProductCategoryName(this.products()),
        active: true,
        editable: false,
        canChooseOptions: false
      }));

      product.name = this.getProductWithToppingName(item);
    }
  }

  getProductWithToppingName(item: PosInvoiceItem) {
    if(!item.options?.length) {
      return item.name;
    }

    return `${item.name} và ${item.options?.length} topping khác`;
  }

  buildProductWithTopping(
    products2: PosProduct[],
    productIndex: number,
    productCategorizedIndex: number,
    product2: PosProduct,
    item: PosInvoiceItem,
    originItemOptionsLength: number,
    toppingItems: PosInvoiceItem[]
  ) {
    const shouldClone = !originItemOptionsLength && !item.isCustomItem && !product2.hasCloneAnotherProduct;
    if(shouldClone) {
      this.cloneProduct(
        product2,
        products2,
        productIndex,
        productCategorizedIndex
      );
    }

    this.products.update(products => {
      const product = products[productCategorizedIndex]?.items?.[productIndex];

      if(product) {
        if(toppingItems?.length > 0) {
          /**
       * nếu trước đó chưa có topping
       * thì clone 1 thằng giông hệt
       *
       * isCustomItem thì nó đã clone rồi ko cần clone lại nữa
       */
          if(shouldClone) {
            product.hasCloneAnotherProduct = true;
          }

          item.options = toppingItems;
          this.buildItemOptions(item);
          product.name = this.getProductWithToppingName(item);
        } else {
          /**
       * nếu trước đó đã có topping
       * thì ĐÃ TỒN TẠI clone 1 thằng giông hệt
       * thì xóa thằng đó đi nếu thằng đó chưa đc chọn
       *
       * chạy từ cuối về đầu
       */
          if(item.options && item.options?.length > 0) {
            for (let i = products[productCategorizedIndex].items.length - 1; i > 0; i--) {
              if(products[productCategorizedIndex].items[i]._id === item._id && !products[productCategorizedIndex].items[i].active) {
                products.splice(i, 1);
              }
            }
          }

          delete item.options;
        }

        this.buildProductWithOptions(product, item);
      }

      return products;
    });
  }

  buildCustomItemResult(
    originIsCustomItem: boolean,
    originOptionsLength: number,
    product2: PosProduct,
    products2: PosProduct[],
    productIndex: number,
    productCategorizedIndex: number,
    item: PosInvoiceItem
  ) {
    const shouldClone = !originIsCustomItem && !product2.hasCloneAnotherProduct && !originOptionsLength;
    // console.log('shouldClone', shouldClone);

    if(shouldClone) {
      this.cloneProduct(
        product2,
        products2,
        productIndex,
        productCategorizedIndex
      );
    }

    this.products.update(products => {
      const product = products[productCategorizedIndex]?.items?.[productIndex];
      if(product) {
        /**
     * 3 trường hợp này đều đã có 1 clone bên dưới rồi
     */
        if(shouldClone) {
          product.hasCloneAnotherProduct = true;
        }

        product._id = item._id;
        product.name = this.getProductWithToppingName(item);
        product.cost = item.totalCost;
        product.price = item.price;

        if(product.selectedItem) {
          product.selectedItem.price = item.price;
          product.selectedItem.subTotal = product.selectedItem.quantity * item.price;
        }
      }

      return products;
    });
  }

  getTotal() {
    const products = this.selectedItems();

    if(!products?.length) {
      return 0;
    }

    return products.reduce(
      (acc, item) => {
        acc += item.subTotal;

        if(item.options && item.options?.length > 0) {
          return item.options.reduce(
            (d, e) => d + e.subTotal,
            acc
          );
        }

        return acc;
      },
      0
    );
  }

  /**
   * lấy ra thằng item đc selected
   * 1 thằng product _id có thể có nhiều trong arr selectedItems
   */
  getItem(selectedItems: PosInvoiceItem[], product: PosProduct) {
    let index!: number;
    let item!: PosInvoiceItem;

    for(let i = 0; i < selectedItems.length; i++) {
      const sItem = selectedItems[i];

      if(sItem._id === product._id) {
        let hasAllOptions = true;

        if(sItem.options && sItem.options?.length > 0) {
          if(!product.optionProducts?.length) {
            hasAllOptions = false;
          } else {
            for(let j = 0; j < sItem.options.length; j++) {
              if(!product.optionProducts.filter(opt => sItem.options?.[j]?._id === opt._id)[0]) {
                hasAllOptions = false;
              }
            }
          }
        }

        if(hasAllOptions) {
          index = i;
          item = sItem;
          break;
        }
      }
    }

    return {
      index,
      item
    };
  }

  addItem(productIndex: number, productCategorizedIndex: number, event?: Event) {
    this.products.update(products => {
      const product = products[productCategorizedIndex]?.items?.[productIndex];

      if(product) {
        product.active = true;

        if(!product.selectedItem) {
          product.selectedItem = {
            _id: product._id,
            name: product.name,
            price: product.price,
            totalCost: product.cost,
            quantity: 1,
            subTotal: product.price,
            total: product.price,
            discount: 0
          };

          this.selectedItems.update(items => [...items, product.selectedItem as PosInvoiceItem]);
        }
        /**
             * nếu ko có event nghĩa là bấm ra khoảng ngoài + -
             * nếu đã từng add rồi thì ko + thêm nữa
             */
        else if(event) {
          product.selectedItem.quantity += 1;
          product.selectedItem.subTotal = product.selectedItem.quantity * product.price;
          product.selectedItem.total = product.selectedItem.subTotal - product.selectedItem.discount;

          // build lai subtotal
          this.buildItemOptions(product.selectedItem);
        }
      }

      return products;
    });
  }

  removeItem(productIndex: number, productCategorizedIndex: number) {
    this.products.update(products => {
      if(products[productCategorizedIndex] && products[productCategorizedIndex].items[productIndex]) {
        const product = products[productCategorizedIndex].items?.[productIndex];

        if(product && product.selectedItem) {
          if(product.selectedItem.quantity > 0) {
            product.selectedItem.quantity -= 1;
            product.selectedItem.subTotal = product.selectedItem.quantity * product.price;
            product.selectedItem.total = product.selectedItem.subTotal - product.selectedItem.discount;

            // build lai subtotal
            this.buildItemOptions(product.selectedItem);
          }
        }

        if(!product.selectedItem?.quantity || product.selectedItem?.quantity <= 0) {
          product.active = false;

          /**
           * nếu product có product.optionProducts
           * nghĩa là nó đã từng clone 1 thằng bên dưới rồi
           * nên nếu xóa đi thì xóa khỏi array luôn
           */
          if(product.optionProducts && product.optionProducts.length > 0) {
            products.splice(productIndex, 1);
          }

          if(product.selectedItem) {
            this.selectedItems.update(items => items.filter(x => x.quantity > 0));
            delete product.selectedItem;
          }
        }
      }

      return products;
    });
  }

  getMainProductCategoryName(products: PosCategorizedProducts) {
    return products?.[0]?.category;
  }

  getMainProductItems(products: PosCategorizedProducts) {
    return products?.[0]?.items;
  }

  reset() {
    this.selectedItems.set([]);

    this.products.update(products => {
      products.forEach(p => {
        p.items.forEach(product => {
          product.active = false;
          delete product.selectedItem;
        });
      });

      return products;
    });
  }
}
