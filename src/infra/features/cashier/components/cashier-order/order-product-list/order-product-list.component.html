<div
  class="invoice-items-container"
  [class.topping]="isSelectingToppingItems()"
  >

  @if(toppingForItem()) {
  <div class="fw-bold text-center topping-name">
    Thêm Topping cho món:
    <span class="text-uppercase">
      {{ toppingForItem() }}
    </span>
  </div>

  <div class="cursor-pointer close-dialog" (click)="close()">
    <span class="material-symbols-outlined icon"> close </span>
  </div>
  }

  <div class="menu">
    <div
      class="invoice-header-container bg-white"
      emblaCarousel
      [options]="emblaCarouselOptions"
      >
      <ul class="invoice-header d-flex">
        @for (product of products(); track product.category) {
        <li
          class="user-select-none"
          [class.active]="$index === activeProductIndex()"
          (click)="selectProductListIndex($index)"
          >
          {{ product.category}}
        </li>
        }
      </ul>
    </div>


    @if(products()[activeProductIndex()]) {
    <ul class="invoice-items d-flex flex-wrap">
      @for (item of products()[activeProductIndex()].items; track item) {
        <li
          [class.selected] = item.active
          (click)="addItem($index, activeProductIndex())"
          >
          <div class="d-flex align-items-center">
            <div class="info">
              <div>
                <b>{{ item.name }}</b>
              </div>
              <div class="d-flex align-items-center mt-2">
                <div class="price">
                  {{ item.price | formatString: 'money'  }}
                </div>

                @if(!isSelectingToppingItems() && item.active && item.editable) {
                <span
                  class="material-symbols-outlined edit"
                  (click)="openCustomItem(products()[activeProductIndex()].items, $index, activeProductIndex())"
                  >
                  edit
                </span>
              }

              </div>
            </div>

            @if (item.active) {
            <div class="quantity ms-auto">
              <div class="d-flex align-items-center justify-content-end">
                @if(!isSelectingToppingItems() && item.canChooseOptions) {
                <div
                  class="material-symbols-outlined icon topping me-3"
                  (click)="openTopping($index, activeProductIndex())"
                  >
                  add_shopping_cart
                </div>
                }

                <div
                  class="material-symbols-outlined icon"
                  (click)="removeItem($index, activeProductIndex(), $event)"
                  >
                  remove
                </div>

                <div class="count mx-3">
                  {{ item.selectedItem?.quantity ?? 0 }}
                </div>

                <div
                  class="material-symbols-outlined icon"
                  (click)="addItem($index, activeProductIndex(), $event)"
                  >
                  add
                </div>
              </div>
            </div>
            }
          </div>



        </li>
      }
    </ul>
    }


    @if (hasButton()) {
    <div class="d-flex menu-btn border-top">
      <button
        class="reset text-uppercase btn btn-secondary me-3"
        (click)="reset()"
        >
        Chọn lại
      </button>
      <button
        class="add-to-cart text-uppercase btn btn-primary flex-grow-1"
        (click)="submit()"
        >
        Thêm vào đơn
        <span class="total d-block">
          {{ total() | formatString: 'money'  }} ₫
        </span>
      </button>
    </div>
    }

  </div>


</div>
