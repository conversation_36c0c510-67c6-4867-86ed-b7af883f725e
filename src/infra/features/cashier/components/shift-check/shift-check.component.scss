app-shift-check {
  font-size: 14px;

  .btn {
    font-size: 14px;
    text-transform: uppercase;
    border-radius: 0;
    margin-top: 15px;
  }


}

.shift-start {
  .select-employee, .mdc-form-field {
    width: 100%;
  }
  .select-employee {
    border: 1px solid #c0c0c0;
    border-radius: 5px;

    &.mat-mdc-checkbox-checked {
      background-color: #03A9F4;

      .mdc-form-field {
        color: #fff;
      }
    }
    .mdc-label {
      flex-grow: 1;
      font-size: 16px;
      padding: 17px 0;
    }
  }
  .submit {
    height: 60px;
    width: 100%;
    font-size: 16px;
    text-transform: uppercase;
    font-weight: 500;
  }
}
