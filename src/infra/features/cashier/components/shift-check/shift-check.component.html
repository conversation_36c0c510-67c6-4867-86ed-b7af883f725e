@if(!data().currentShift?.shiftId) {
  <div class="bg-danger p-3" style="border-bottom: 1px solid red">
    <div>Bạn đang chưa trong ca làm việc nào.</div>
    <div>
      <button
        class="btn btn-primary"
        (click)="start()"
        >
        Bắt đầu ca làm
      </button>
    </div>
  </div>
} @else if(!hideInShiftWarning) {
  <div class="bg-warning p-2 d-flex align-items-center" style="border-bottom: 1px solid #fdca31">
    <div class="small">
      Đang trong ca làm việc của <b>{{ employeeNames() }}</b>.
      <br />
      Ca làm bắt đầu từ {{ data().currentShift.startAtText }}
    </div>
    <div class="ms-auto">
      <a
        routerLink="/shift-change"
        class="btn btn-primary mt-0"
        >
        Kết ca
      </a>
    </div>
  </div>
}
