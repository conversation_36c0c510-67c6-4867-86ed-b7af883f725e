import { signal, computed, Component, Inject, ViewEncapsulation, Input, effect } from '@angular/core';

import { MatDialog } from '@angular/material/dialog';
import { Subscription } from 'rxjs';
import { RouterLink } from '@angular/router';
import { PosUpdateData } from 'salehub_shared_contracts';
import { UpdateDataStore } from '@core/store/update_data.store';
import { InitDataStore } from '@core/store/init_data.store';
import { ShiftStartDialog } from '../start-shift/start-shift.component';

@Component({
  selector: 'app-shift-check',
  standalone: true,
  imports: [
    RouterLink
  ],
  templateUrl: './shift-check.component.html',
  styleUrl: './shift-check.component.scss',

  encapsulation: ViewEncapsulation.None
})

export class ShiftCheckComponent {
  @Input() hideInShiftWarning!: string;

  data!: ReturnType<typeof signal<PosUpdateData>>;
  employeeNames = computed(() => {
    if(this.updateDataStore.getData()?.currentShift?.employees?.length) {
      return this.updateDataStore.getData()?.currentShift?.employees?.map(x => x.name).join(', ') as string;
    }
    return '';
  });

  subscription!: Subscription;

  constructor(
    private updateDataStore: UpdateDataStore,
    private initDataStore: InitDataStore,
    public dialog: MatDialog
  ) {}

  ngOnInit() {
    this.data = this.updateDataStore.getDataSignal();
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  start() {
    this.dialog.open(ShiftStartDialog, {
      data: this.initDataStore.getData().receiptionists,
      disableClose: true,
      panelClass: ['full-screen-modal', 'shift-start']
    });
  }
}
