<div class="d-flex align-items-center border-bottom p-2 sum-nav">
  <div class="cursor-pointer">
    <span
      class="material-symbols-outlined"
      style="font-size: 40px;"
      mat-button
      [matMenuTriggerFor]="menu"
      >
      more_horiz
    </span>

    <mat-menu #menu="matMenu">
      @if (!order.isFinalized && !order.isThirdPartyInvoice) {
      <button mat-menu-item (click)="openDiscount()">
        <div class="d-flex align-items-center">
          <span class="material-symbols-outlined me-3">
            sell
            </span>
          Thêm khuyến mại toàn đơn
        </div>
      </button>

      <button mat-menu-item (click)="openNote()">
        <div class="d-flex align-items-center">
          <span class="material-symbols-outlined me-3">
            description
            </span>
          Thêm ghi chú
        </div>
      </button>


      <button mat-menu-item (click)="edit()">
        <div class="d-flex align-items-center">
          <span class="material-symbols-outlined me-3">
            edit
            </span>
          Sửa món ăn
        </div>
      </button>
      }

      @if (order.invoiceId) {
      <button mat-menu-item (click)="print()">
        <div class="d-flex align-items-center">
          <span class="material-symbols-outlined me-3">
            print
            </span>
          In đơn hàng
        </div>
      </button>
      }

      @if (hasPreOrderButton) {
        <button mat-menu-item (click)="preOrder()">
          <div class="d-flex align-items-center">
            <span
              class="material-symbols-outlined me-3"
              >
              schedule
            </span>
            {{ !isPreOrder ? 'Thiết lập' : 'Hủy Thiết lập'}} đơn đặt trước
          </div>
        </button>
      }

      @if (!order.isFinalized && !order.isThirdPartyInvoice) {
      <button mat-menu-item (click)="showItemDiscountAndNote = true">
        <div class="d-flex align-items-center">
          <span
            class="material-symbols-outlined me-3"
            >
            visibility
            </span>
          Hiện Khuyến mại / Ghi chú từng món
        </div>
      </button>
      }

      @if (order.invoiceId) {
      <button mat-menu-item (click)="renderBill()">
        <div class="d-flex align-items-center">
          <span
            class="material-symbols-outlined me-3"
            >
            send
            </span>
          Gửi hóa đơn cho khách
        </div>
      </button>
      }

      @if (order.invoiceId && !order.isThirdPartyInvoice) {
        <button mat-menu-item (click)="copyInvoice()">
          <div class="d-flex align-items-center">
            <span
              class="material-symbols-outlined me-3"
              >
              content_copy
              </span>
            Copy đơn hàng
          </div>
        </button>
        }

      @if (!order.isThirdPartyInvoice && order.status !== 'cancelled') {
      <button mat-menu-item (click)="openDelete()">
        <div class="d-flex align-items-center">
          <span class="material-symbols-outlined me-3">
            delete
            </span>
          Hủy đơn hàng
        </div>
      </button>
      }
    </mat-menu>
  </div>

  <div class="flex-grow-1 px-3 fw-500 text-uppercase">
    <div class="d-flex align-items-center">
      <div class="me-2 invoice-id">
        {{ order.invoiceId ?? 'Đơn mới' }}
      </div>

      <div>
        @if (order.thirdPartyInvoice?.partyName === 'grab') {
          <img src="./assets/images/svg/grab-2.svg" width="40">
        } @else if (order.thirdPartyInvoice?.partyName === 'shopee') {
          <img src="./assets/images/svg/shopee2.svg" width="40">
        } @else if (order.diningOption === 'eatin') {
          <img src="./assets/images/svg/eatin.svg" width="30">
        } @else if (order.diningOption === 'takeaway') {
          <img src="./assets/images/svg/shipper.svg" width="40">
        }
      </div>

      @if (order.isFromAds) {
      <div class="ads d-flex align-items-center">
        <span class="material-symbols-outlined icon me-1">
          ads_click
          </span>
          ads
      </div>
      }
    </div>
  </div>

  @if (!order.isFinalized && !order.isThirdPartyInvoice) {
  <div
    (click)="edit()"
    class="me-4 cursor-pointer icon">
    <span class="material-symbols-outlined">
      edit
      </span>
  </div>
  }

  @if (order.invoiceId) {
  <div
    (click)="print()"
    class="me-4 cursor-pointer icon"
    >
    <span class="material-symbols-outlined">
      print
      </span>
  </div>
  }

  <div
    (click)="close(false)"
    class="cursor-pointer icon"
    >
    <span class="material-symbols-outlined">
      close
      </span>
  </div>
</div>




<div class="sum-body">
  @if (order.customer && order.customer.phoneNumber) {
  <div class="border-bottom p-3 px-2 sum-customer">
    <div class="d-flex align-items-center">
      <div class="me-3">
        <img
          [src]="order.customer.avatar ?? './assets/images/svg/default_avatar.svg'"
          width="50"
          />
      </div>
      <div class="flex-grow-1">
        <div class="text-primary fw-500 cursor-pointer" (click)="showCustomerInvoices()">
          {{ order.customer.phoneNumber | formatString: 'phone' }}
          @if (order.customer.name) {
            - {{ order.customer.name }}
          }
        </div>

        @if (order.customer.zaloName) {
          <div class="mt-2 mb-2">
            <span class="fw-500 text-dark">{{ order.customer.zaloName }} </span>
            <span class="text-muted ms-1">(Zalo)</span>
          </div>
        }



        @if (order.customer.lastOrderAtText) {
        <div class="mt-2">
          @if (order.customer.countOrders && order.customer.countOrders > 0) {
          Đã đặt <span class="fw-500">{{ order.customer.countOrders}} lần</span> với tổng tiền <span class="fw-500">{{ order.customer.totalSold| formatString: 'money' }}</span>
          }
        </div>

        <div class="mt-2 small">
          Lần cuối đặt đơn vào {{ order.customer.lastOrderAtText }}
        </div>
        }

        @if (order.customer.lastRatingText) {
          <div class="mt-2 small text-danger fw-500">
            {{ order.customer.lastRatingText }}
          </div>
          }

        @if (order.customer.address) {
        <div class="d-flex">
          <div class="text-muted flex-grow-1 small mt-2">
            {{ order.delivery.displayAddress }}
            @if (order.delivery.distance) { ({{ order.delivery.distance }} km) }
          </div>

          @if (mapPlaceDisplayUrl) {
          <div class="ms-auto d-flex ms-3 align-items-center">
            <a
              [href]="mapPlaceDisplayUrl"
              target="_blank"
              class="text-dark me-2 d-block"
              >
              <span class="material-symbols-outlined" style="font-size: 22px; margin-top: 3px;">
                map
                </span>
            </a>

            <a
              [href]="mapPlaceStreetViewUrl"
              target="_blank"
              class="text-dark d-block"
              >
              <span class="material-symbols-outlined" style="font-size: 22px; margin-top: 3px;">
                satellite
              </span>
            </a>
          </div>
          }
        </div>
        }


        @if (order.customer.note) {
          <div class="text-danger mt-2 fw-500">
            Ghi chú: {{ order.customer.note }}
          </div>
        }


      </div>

      <div class="text-end">
        <a
          href="tel:{{ order.customer.phoneNumber }}"
          (click)="call(order.customer.phoneNumber, $event)"
          class="text-black"
          >
          <span class="material-icons" style="font-size: 34px;">
              phone_enabled
            </span>
        </a>
      </div>
    </div>

    @if (order.customer.rank || order.customer.badges?.length || isOwner) {
    <div class="d-flex align-items-center">
      <div class="me-3" style="width: 50px">
      </div>
      <div class="flex-grow-1">
        @if (order.customer.rank || order.customer.badges?.length) {
          <div class="mt-2 badges d-flex flex-wrap">
            @if (order.customer.rank) {
              <span class="badge badge-warning fw-normal rounded-0 me-2 mb-1">#{{ order.customer.rank }}</span>
            }

            @if (order.customer.badges?.length) {
              @for (badge of order.customer.badges; track badge) {
                <span
                  class="badge fw-normal rounded-0 me-2 mb-1"
                  [class.badge-primary]="badge.type === 'primary'"
                  [class.badge-success]="badge.type === 'success'"
                  [class.badge-danger]="badge.type === 'danger'"
                  >
                  {{ badge.text }}
                </span>
              }
            }
          </div>
        }

        @if (isOwner) {
          <div class="mt-3 fw-500 cursor-pointer text-uppercase text-info small"
            (click)="showCustomerDetails()"
            >
            Xem chi tiết khách hàng
          </div>
        }
      </div>
    </div>
    }

  </div>
  }

  @if (order.delivery &&
    !order.delivery.selfArrive &&
    (
      (order.delivery.driver && order.delivery.driver.name) ||
      (order.delivery.company && order.delivery.company.name)
    )
    ) {
  <div class="border-bottom p-2 sum-driver">
    @if (order.delivery.company && order.delivery.company.name && !order.isThirdPartyInvoice) {
    <div class="d-flex align-items-center mb-3">
      <div class="text-center me-3" style="width: 50px;">
        <img
          [src]="order.delivery.company.avatar ?? './assets/images/svg/default_avatar.svg'"
          width="40"
          height="40"
          />
      </div>
      <div class="flex-grow-1">
        <div class="text-muted text-uppercase small mb-1">
          Đơn vị ship
        </div>

        <div class="fw-bold">
          {{ order.delivery.company.name }}
          @if (order.delivery.company.phoneNumber) {
          - {{ order.delivery.company.phoneNumber | formatString: 'phone' }}
          }
        </div>
      </div>

      @if (order.delivery.company.phoneNumber) {
      <div class="text-end">
        <a
          href="tel:{{ order.delivery.company.phoneNumber }}"
          (click)="call(order.delivery.company.phoneNumber, $event)"
          class="text-black"
          >
          <span class="material-icons" style="font-size: 34px;">
            phone_enabled
          </span>
        </a>
      </div>
      }
    </div>
    }

    @if (order.delivery.driver && order.delivery.driver.phoneNumber) {
    <div class="d-flex align-items-center">
      <div class="text-center me-3" style="width: 50px;">
        <img
          [src]="order.delivery.driver.avatar ?? './assets/images/svg/default_avatar.svg'"
          width="40"
          height="40"
          />
      </div>
      <div class="flex-grow-1">
        <div class="text-muted text-uppercase small mb-1">
          Tài xế
        </div>

        <div>
          {{ order.delivery.driver.phoneNumber | formatString: 'phone' }}
          @if (order.delivery.driver.name) {
            - {{ order.delivery.driver.name }}
          }
        </div>
      </div>

      <div class="text-end">
        <a
          href="tel:{{ order.delivery.driver.phoneNumber }}"
          (click)="call(order.delivery.driver.phoneNumber, $event)"
          class="text-black"
          >
          <span class="material-icons" style="font-size: 34px;">
              phone_enabled
            </span>
        </a>
      </div>
    </div>
    }

    @if (order.delivery.note) {
      <div class="my-3 fw-bold ps-3 note" style="margin-left: 50px">
        {{ order.delivery.note }}
      </div>
    }

  </div>
  }

  @if(order.feedback && order.feedback.rating) {
    <div class="border-bottom py-3 px-4 feedback">
      <div class="text-uppercase small mb-2 fw-500">
        Phản hồi từ khách hàng
      </div>

      <div class="d-flex align-items-center">
        <div class="d-flex align-items-center">
          <div class="d-flex align-items-center justify-content-center">
            @for(r of ratingArr; track r) {
              <div
                class="rating-star me-1"
                [class.fill]="$index < order.feedback.rating"
                >
              </div>
            }
          </div>
          <div class="text-muted small ms-2">
            {{ order.feedback.rating }}.0
          </div>
        </div>

        <div class="ms-auto text-muted small">
          {{ order.feedback.createdAt | formatDate: 'dmyhm' }}
        </div>
      </div>

      <div class="review-content my-3">
        @if(order.feedback.reasons && order.feedback.reasons.length > 0) {
        <div class="d-flex fw-bold mb-2">
          {{ order.feedback.reasons.join(', ') }}
        </div>
        }

        @if(order.feedback.comments && order.feedback.comments.length > 0) {
        <div class="mb-3">
          <span [innerHTML]="order.feedback.comments.join('\n') | replaceLineBreaks"></span>
        </div>
      }

        @if(order.feedback.photos && order.feedback.photos.length > 0) {
        <div class="mb-3">
          @for(photo of order.feedback.photos; track photo) {
          <img
            [src]="photo.thumbnail"
            (click)="openImage(photo.url)"
            class="me-2 mb-2 cursor-pointer"
            />
          }
        </div>
        }

        @if(order.feedback.reply && order.feedback.reply.content) {
        <div class="reply">
          {{ order.feedback.reply.content }}
        </div>
        }
      </div>
    </div>
  }

  @for (item of order.items; track item) {
  <div class="p-3 px-2 invoice-items">
    <div class="d-flex align-items-baseline">
      <div class="qnt pe-3">
        <b class="pe-1">{{ item.quantity }}</b>
        <span class="multi">⨯</span>
      </div>
      <div class="item">
        <div>
          <b>{{ item.name }}</b>
        </div>

        @if (showItemDiscountAndNote && !order.isFinalized && !order.isThirdPartyInvoice) {
        <div class="mt-2">
          <span
            class="material-symbols-outlined text-muted cursor-pointer"
            (click)="openNote(item)"
            >
            description
            </span>
          <span
            class="material-symbols-outlined text-muted cursor-pointer ms-3"
            (click)="openDiscount(item)"
            >
            sell
            </span>
        </div>
        }

      </div>
      <div class="total">
        <div>
          {{ item.subTotal  | formatString: 'money' }}
        </div>

        @if (item.discount > 0) {
        <div class="mt-2">
          <span
            class="material-symbols-outlined text-success align-middle me-2"
            style="font-size: 16px;"
            >
            loyalty
          </span>
          {{ -item.discount  | formatString: 'money' }}
        </div>
        }

        @if (isOwner && item.grossProfit !== undefined) {
        <div class="mt-1 small">
          <span class="text-muted me-1">{{ item.grossProfitPct }}% / </span>
          <span class="text-success">{{ item.grossProfit  | formatString: 'money' }}</span>
        </div>
        }

      </div>
    </div>

    @if (item.instruction) {
      <div class="d-flex align-items-baseline mt-1">
        <div class="qnt pe-3 flex-shrink-0"></div>
        <div>
          <div class="instruction small">
            {{ item.instruction }}
          </div>
        </div>
      </div>
    }


    @if (item.options && item.options.length > 0) {
      <div class="d-flex align-items-baseline mt-3">
        <div class="qnt pe-3"></div>
        <div class="item si-label text-uppercase">Gọi thêm</div>
      </div>

      @for (opt of item.options; track opt) {
      <div class="d-flex align-items-baseline mt-3">
        <div class="qnt pe-3">

        </div>
        <div class="item">
          @if (opt.quantity > 1) {
          <b class="pe-1">{{ opt.quantity }}</b>
          <span class="multi">⨯</span>
          }

          {{ opt.name }}

          @if (showItemDiscountAndNote && !order.isFinalized && !order.isThirdPartyInvoice) {
          <span
            class="material-symbols-outlined text-muted cursor-pointer ms-2"
            style="font-size: 16px;"
            (click)="openDiscount(opt)"
            >
            sell
            </span>
          }
        </div>
        <div class="total">
          <div>
            {{ opt.subTotal  | formatString: 'money' }}
          </div>

          @if (opt.discount > 0) {
          <div class="mt-2">
            <span
              class="material-symbols-outlined text-success align-middle me-2"
              style="font-size: 16px;"
              >
              loyalty
            </span>
            {{ -opt.discount  | formatString: 'money' }}
          </div>
          }

          @if (isOwner && opt.grossProfit !== undefined) {
          <div class="mt-1 small">
            <span class="text-muted me-1">{{ opt.grossProfitPct }}% / </span>
            <span class="text-success">{{ opt.grossProfit  | formatString: 'money' }}</span>
          </div>
          }


        </div>
      </div>
      }
    }

    @if (item.note) {
    <div class="d-flex align-items-baseline mt-3">
      <div class="qnt pe-3"></div>
      <div class="full-col2-container">
        <div class="text-uppercase text-muted note-label mb-1">Ghi chú món ăn</div>

        <div class="note">
          <b [innerHTML]="item.note | replaceLineBreaks"></b>
        </div>
      </div>
    </div>
    }


  </div>
  }

  @if (order.note || order.internalNote) {
    <div class="px-3 pb-3 border-top">
      @if (order.note) {
        <div class="mt-3">
          <div class="text-uppercase text-muted note-label mb-1">Ghi chú đơn hàng</div>
          <div class="note">
            <b [innerHTML]="order.note | replaceLineBreaks"></b>
          </div>
        </div>
      }

      @if (order.internalNote) {
        <div class="mt-3">
          <div class="text-uppercase text-muted note-label mb-1">Ghi chú nội bộ</div>
          <div class="note text-danger">
            <b [innerHTML]="order.internalNote | replaceLineBreaks"></b>
          </div>
        </div>
      }
    </div>
  }




  <div class="p-3 border-top">
    @if (order.discounts && order.discounts.length > 0) {
      <div class="py-3 mb-4 border-bottom">
        <div class="text-uppercase mb-2">
          <b>Giảm giá</b>
        </div>

        @for (discount of order.discounts; track discount) {
          <div class="d-flex py-2">
            <div class="me-2">
              {{ discount.name }}
            </div>

            <div class="ms-auto text-end">
              <div class="d-flex align-items-center">
                <span
                  class="material-symbols-outlined text-success me-2"
                  style="font-size: 16px;"
                  >
                  loyalty
                </span>
                {{ -discount.amount  | formatString: 'money' }}
              </div>
            </div>
          </div>
        }
      </div>
    }

  <div class="d-flex pt-1 pb-2">
    <div class="text-muted">
      Tổng tiền <b>{{ order.summarize.countItems }}</b> món
    </div>

    <div class="ms-auto text-muted text-end">
      <div>
        {{ order.summarize.subTotal  | formatString: 'money' }}
      </div>
    </div>
  </div>

    @if (order.discountWholeOrder.amount > 0 && order.discountWholeOrder.amount !== order.summarize.totalDiscount) {
      <div class="d-flex pt-1 pb-2">
        <div class="text-muted">
          {{ order.discountWholeOrder.name ?? 'Giảm giá toàn hóa đơn' }}
        </div>

        <div class="ms-auto text-muted text-end">
          <div>
            {{ order.discountWholeOrder.amount | formatString: 'money' }}
          </div>
        </div>
      </div>
    }

    @if (order.summarize.totalDiscount > 0) {
    <div class="d-flex pt-1 pb-2">
      <div class="text-muted">
      Tổng giảm giá
      </div>

      <div class="ms-auto text-muted text-end">
        <div>
          {{ order.summarize.totalDiscount  | formatString: 'money' }}
        </div>
      </div>
    </div>
    }

    @if (order.thirdPartyInvoice?.partyName && order.summarize.thirdPartyTotalFee  !== undefined) {
    <div class="d-flex pt-1 pb-2">
      <div class="text-muted">
      Phí {{ order.thirdPartyInvoice?.partyName?.toUpperCase() }}
      </div>

      <div class="ms-auto text-muted text-end">
        <div>
          {{ order.summarize.thirdPartyTotalFee  | formatString: 'money' }}
        </div>
      </div>
    </div>
    }

    @if (order.delivery.fee !== undefined) {
    <div class="d-flex align-items-center pt-1 pb-2">
      <div class="text-muted">
        Phí Ship @if (order.delivery.distance) { ({{ order.delivery.distance }} km) }
      </div>

      <div class="ms-auto text-end d-flex align-items-center">
        @if (mapDirUrl) {
          <div>
            <a
              [href]="mapDirUrl"
              target="_blank"
              class="text-dark me-3 d-block"
              >
              <span class="material-symbols-outlined" style="font-size: 22px; margin-top: 3px;">
                near_me
                </span>
            </a>
          </div>
          }

        <div>
          {{ order.delivery.fee  | formatString: 'money' }}
        </div>
      </div>
    </div>
    }


    <div class="d-flex pt-1 pb-2">
      <div class="text-muted">
        Thu của khách
      </div>

      <div class="ms-auto text-end">
        <div>
          <b>
            {{ order.summarize.totalRevenue  | formatString: 'money' }}
          </b>
        </div>
      </div>
    </div>



    @if (isOwner && order.summarize.grossProfit !== undefined) {
    <div class="d-flex pt-1">
      <div class="text-muted">
        Lãi
      </div>

      <div class="ms-auto text-end ">
        <div>
          <span class="text-muted small d-inline-block me-2">
            {{ order.summarize.grossProfitPct }}% /
          </span>

          <b class="text-success">
            {{ order.summarize.grossProfit  | formatString: 'money' }}
          </b>
        </div>
      </div>
    </div>
    }


    @if (order.isThirdPartyInvoice) {
      <div class="border-top border-bottom d-flex py-2 mt-3 align-items-center small">
        <div class="text-muted">
          Tham chiếu
        </div>

        <div class="ms-auto text-muted text-end">
          @if (order.thirdPartyInvoice?.billId) {
          <div class="tbill-id">
            <b>{{ order.thirdPartyInvoice?.billId }}</b>
          </div>
          }
          @if (order.thirdPartyInvoice?.id) {
          <div>
            {{ order.thirdPartyInvoice?.id }}
          </div>
          }
          @if (order.thirdPartyInvoice?.code) {
          <div>
            {{ order.thirdPartyInvoice?.code }}
          </div>
          }
        </div>
      </div>
    } @else {
      <div class="py-2 mt-3">
        <mat-form-field appearance="outline" class="w-100 payment-method" [class.disabled]="order.isFinalized">
          <mat-label>Hình thức thanh toán</mat-label>
          <mat-select [(ngModel)]="order.paymentMethod">
            <mat-option value="cash">Tiền mặt</mat-option>
            <mat-option value="bank">Chuyển khoản</mat-option>
            <mat-option value="unspecified">Hiện tại chưa rõ</mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      @if (isPreOrder) {
      <div class="pt-2 mb-5">
        <div
          class="input-datetime"
          [class.has-error]="preOrderError"
          >

          <label>Khách hẹn giờ giao hàng</label>

          <input
            type="datetime-local"
            placeholder="Khách hẹn giờ giao hàng"
            [(ngModel)]="preOrderTime"
            (change)="convertPreOrderDateTime($event)"
            >
        </div>
        @if (preOrderError) {
        <div class="text-danger my-3">
          {{ preOrderError }}
        </div>
        }
      </div>
      }
    }

    @if (order.timeline && order.timeline.length > 0) {
    <div class="border-bottom invoice-timeline py-3">
      @for (tl of order.timeline; track tl) {

      <div
        class="invoice-timeline-item {{ tl.class ? tl.class : '' }}"
        [class.active]="tl.isActive"
        [class.processing]="order.status !== 'completed' && order.status !== 'cancelled'"
        >
        {{ tl.text }}
      </div>

      }
    </div>
    }


    @if (hasButton) {
    <div
      class="d-flex my-4 invoice-actions"
      style="height: 50px;"
      [style.visibility]="hasActionButtons ? 'visible' : 'hidden'"
      >
        <button
          class="reset text-uppercase btn btn-danger me-2 d-flex align-items-center justify-content-center"
          (click)="setSave()"
          [disabled]="isSubmitting"
          >
          @if (!isSubmitting || order.status !== 'preparing') {
            {{ saveButtonText }}
          } @else {
            <div class="dot-loader"><div class="dot-pulse"></div></div>
          }
        </button>

        @if (hasCompleteButton) {
        <button
          class="add-to-cart text-uppercase btn btn-success flex-grow-1 d-flex align-items-center justify-content-center"
          (click)="setCompleted()"
          [disabled]="isSubmitting"
          >
          @if (!isSubmitting || order.status !== 'completed') {
            <span class="material-symbols-outlined me-2">
              done
              </span>
              {{ order.status === 'preorder' ? 'Đã Hoàn thành' : 'Hoàn thành' }}
          } @else {
            <div class="dot-pulse"></div>
          }

        </button>
        } @else if (hasDeliveryButton) {
        <button
          class="add-to-cart text-uppercase btn btn-success flex-grow-1 d-flex align-items-center justify-content-center"
          (click)="setDelivery()"
          [disabled]="isSubmitting"
          >
          @if (!isSubmitting) {
            <span class="material-symbols-outlined me-2">
              done
              </span>
              Vận chuyển
          } @else {
            <div class="dot-pulse"></div>
          }

        </button>
        } @else if(hasPrepareButton) {
         <button
          class="add-to-cart text-uppercase btn btn-warning flex-grow-1 d-flex align-items-center justify-content-center"
          (click)="setPreparing()"
          [disabled]="isSubmitting"
          >
          Chuẩn bị
        </button>
        }

    </div>
    }
  </div>
</div>


@if (isRenderBill) {
  <div class="invoice-bill-hidden">
    <app-cashier-order-bill
      [order]="order"
      (renderedEvent)="shareBill()"
      >
    </app-cashier-order-bill>
  </div>
}


<!-- <app-invoice-bill [invoice]="invoice"></app-invoice-bill> -->
