import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogActions, MatDialogClose, MatDialogContent, MatDialogRef, MatDialogTitle } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { FormatStringPipe } from '@shared/pipes/format_str.pipe';
import { PosInvoice } from 'salehub_shared_contracts';

@Component({
  selector: 'invoice-note-dialog',
  templateUrl: 'note.dialog.html',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatButtonModule,
    MatDialogTitle,
    MatD<PERSON>ogContent,
    MatDialogActions,
    MatSelectModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NoteInvoiceDialog {
  noteType = 'internalNote';

  constructor(
    public dialogRef: MatDialogRef<NoteInvoiceDialog>,
    @Inject(MAT_DIALOG_DATA) public data: {
      note: string,
      title: string,
      invoice: PosInvoice,
      isItemNote: boolean
    }
  ) {}

  close(): void {
    this.dialogRef.close(this.data.note);
  }
}
