.invoice-summarize {
  $sheetMaxHeight: 95vh;

  app-cashier-order-summary {
    display: block;
    position: relative;

    .invoice-id {
      font-size: 14px;
    }
    .ads {
      background: #fef4ea;
      border-radius: 40px;
      font-size: 11px;
      text-transform: uppercase;
      color: #e50000;
      display: flex;
      padding: 4px 10px;
      margin-left: 10px;


      .icon {
        font-size: 14px;
      }
    }

    .tbill-id {
      font-size: 16px;
      color: #000;
      margin-bottom: 5px;
      font-weight: 700;
    }


  }
  .sum-nav {
    height: 50px;

    .icon, .material-symbols-outlined {
      font-size: 30px;
    }
  }

  .feedback {
    .reply {
      background: #ededed;
      border: 1px solid #ccc;
      border-radius: 3px;
      padding: 10px;
      margin-top: 10px;
    }
  }

  .sum-body {
    max-height: calc($sheetMaxHeight - 50px);
    overflow: auto;
    padding-bottom: 50px; //fix ios toolbar
    word-break: break-word;
  }
  .mat-bottom-sheet-container {
    font-size: 15px;
    padding: 0;
    max-height: $sheetMaxHeight;
    letter-spacing: -0.2px;
  }
  .si-label {
    font-size: 13px;
    color: #777;
  }
  .multi {
    font-size: 17px;
    color: #7d7d7d;
    font-weight: 700;
  }
  .invoice-items {
    font-size: 18px;
  }
  .item {
    width: calc(100% - 155px);
    line-height: 1.5;
  }
  .qnt {
    width: 55px;
  }
  .note {
    &-label {
      font-size: 12px;
    }
    & {
      color:#ff9800;
      font-size: 18px;
    }
  }
  .total {
    width: 100px;
    font-size: 17px;
    text-align: right;
    color: #6d6d6d;
  }
  .payment-method {
    &.disabled {
      pointer-events: none;
    }
  }
  .full-col2-container {
    width: calc(100% - 55px)
  }

  .invoice-actions {
    overflow: hidden;

    button {
      font-size: 13px;
      white-space: nowrap;
    }
    .reset {
      min-width: 70px;
    }
  }

  .instruction {
    font-size: 13.5px;
    color: #999;
  }


  .sum-driver {
    img {
      border-radius: 100%;
      object-fit: cover;
      width: 40px;
      height: 40px;
    }
  }

  .invoice-timeline {
    padding-left: 20px;
    position: relative;
    color: #9b9b9b;

    &::before {
      content: '';
      position: absolute;
      border-left: 1px solid #ddd;
      height: calc(100% - 10px);
      left: 10px;
      top: 5px;
    }

    &-item {
      position: relative;
      padding-left: 5px;
      padding-bottom: 10px;
      font-size: 13px;
      word-break: break-word;



      &.active {
        color: #ff9800;

        &.processing {
          &::before {
            background-color: #ff9800;
            animation: ripple 1.2s linear infinite;
          }
        }
      }

      &::before {
        content: "";
        position: absolute;
        background-color: #4CAF50;
        width: 8px;
        height: 8px;
        left: -14px;
        top: 5px;
        border-radius: 100%;
      }
    }
  }

  .invoice-bill-hidden {
    position: absolute;
    opacity: 0;
    pointer-events:none;
    top: -100000px;
    left: -100000px;
  }


}

invoice-delivery-dialog {
  .mat-button-toggle {
    font-size: 13px;
  }
}

.delivery-confirm {
  border: 0 !important;

 .mat-button-toggle-appearance-standard {
    border: 1px solid #bdbdbd;

    &.no-confirm {
      opacity: .3;
    }
  }
  .mat-button-toggle-appearance-standard.mat-button-toggle-checked {
    background: none;
  }
  .icon {
    border: 1px solid #000;
    border-radius: 100%;
    padding: 2px;
    margin-right: 10px;
    font-size: 13px;
  }
}


@media screen and (min-width: 500px) {
  .invoice-summarize {
    .mat-bottom-sheet-container {
      width: 500px;
    }
  }
}


@-webkit-keyframes ripple{
  0%{
      opacity:1;
      -webkit-transform:scale(0);
      transform:scale(0)
  }
  100%{
      opacity:0;
      -webkit-transform:scale(1);
      transform:scale(1)
  }
}
@keyframes ripple{
  0%{
      opacity:1;
      -webkit-transform:scale(0);
      transform:scale(0)
  }
  100%{
      opacity:0;
      -webkit-transform:scale(1);
      transform:scale(1)
  }
}
