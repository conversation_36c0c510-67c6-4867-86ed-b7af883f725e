import { Component, Inject } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";

@Component({
  selector: 'app-review-gallery-dialog',
  templateUrl: 'review.image.component.html'
})
export class ReviewGalleryDialog {
  constructor(
    public dialogRef: MatDialogRef<ReviewGalleryDialog>,
    @Inject(MAT_DIALOG_DATA) public image: string
  ) {}

  setData(data: string) {
    this.image = data;
  }

  close(): void {
    this.dialogRef.close(false);
  }
}