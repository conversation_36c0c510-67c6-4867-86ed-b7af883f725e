import { ChangeDetectionStrategy, Component } from "@angular/core";
import { MatButtonModule } from "@angular/material/button";
import { MatDialogActions, MatDialogClose, MatDialogContent, MatDialogRef, MatDialogTitle } from "@angular/material/dialog";

@Component({
  selector: 'invoice-delete-dialog',
  templateUrl: 'delete.dialog.html',
  standalone: true,
  imports: [
    MatButtonModule,
    MatDialogTitle,
    MatDialogActions
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeleteInvoiceDialog {
  constructor(
    public dialogRef: MatDialogRef<DeleteInvoiceDialog>
  ) {}

  close(value: boolean): void {
    this.dialogRef.close(value);
  }
}