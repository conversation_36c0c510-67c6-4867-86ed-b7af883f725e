import {
  MAT_BOTTOM_SHEET_DATA,
  MatBottomSheetRef
} from '@angular/material/bottom-sheet';
import { MatMenuModule } from '@angular/material/menu';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';

import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import {
  MatDialog
} from '@angular/material/dialog';
import { Router } from '@angular/router';
import html2canvas from 'html2canvas';
import { FormatStringPipe } from '@shared/pipes/format_str.pipe';
import { formatDateTimeLocalValue, scrollTop, getGoogleMapDirectionUrl, getGoogleMapDisplayUrl, getGoogleMapStreetViewUrl } from '@shared/utils';
import { HttpService } from '@core/services/http.service';
import { ReplaceLineBreaksPipe } from '@shared/pipes/line_breaks.pipe';
import { PageMessageService } from '@core/services/page_message.service';
import { AppChannelService } from '@core/services/app_channel.service';

import { FormatDatePipe } from '@shared/pipes/format_date.pipe';
import { HttpLoaderService } from '@core/services/http_loader.service';
import { AppCommonService } from '@core/services/common.service';
import { ROUTER_LINKS, Coordinates, InvoiceBill, PosInvoice, PosInvoiceItem, StoreConfigs } from 'salehub_shared_contracts';
import { InitDataStore } from '@core/store/init_data.store';
import { ReviewGalleryDialog } from './review.dialog';
import { HttpContext } from '@angular/common/http';
import { HTTP_REQUEST_OPTIONS } from '@core/tokens/http-context-token';
import { buildFullPosOrder } from '@features/cashier/utils/order.util';
import { DeliveryInvoiceDialog } from './delivery-dialog/delivery-dialog.component';
import { NoteInvoiceDialog } from './note-dialog/note-dialog.component';
import { DeleteInvoiceDialog } from './delete-dialog/delete-dialog.component';
import { DiscountDialog } from './discount-dialog/discount-dialog.component';
import { CashierOrderBillComponent } from '../order-bill/order-bill.component';

@Component({
  selector: 'app-cashier-order-summary',
  templateUrl: 'order-summary.component.html',
  standalone: true,
  imports: [
    MatMenuModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    ReplaceLineBreaksPipe,
    FormatDatePipe,
    FormatStringPipe,
    MatDatepickerModule,
    CashierOrderBillComponent
  ],
  styleUrl: './order-summary.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})

export class CashierOrderSummaryComponent {
  isSubmitting = false;
  showItemDiscountAndNote = false;
  isPreOrder!: boolean;
  preOrderTime!: string | undefined;
  preOrderError!: string | undefined;
  originalInvoiceStatus!: PosInvoice['status'];
  isOwner = false;
  store!: StoreConfigs;
  isRenderBill = false;
  ratingArr = new Array(5).fill('');

  constructor(
    private _bottomSheetRef: MatBottomSheetRef<CashierOrderSummaryComponent>,
    private http: HttpService,
    @Inject(MAT_BOTTOM_SHEET_DATA) public order: PosInvoice,
    public dialog: MatDialog,
    private router : Router,
    private pageMessage: PageMessageService,
    private appChannel: AppChannelService,
    private loader: HttpLoaderService,
    initStore: InitDataStore,
    private commonService: AppCommonService
  ) {
    this.originalInvoiceStatus = order.status;
    this.isPreOrder = order.status === 'preorder';
    this.isOwner = !!initStore.getData().user?.isOwner;
    this.store = initStore.getData().store;

    if(this.order.scheduledOrderInfo?.expectedDeliveryTime) {
      this.order.scheduledOrderInfo.expectedDeliveryTime = new Date(this.order.scheduledOrderInfo.expectedDeliveryTime);
      this.preOrderTime = formatDateTimeLocalValue(this.order.scheduledOrderInfo.expectedDeliveryTime);
    }

    // console.log(this.order.timeline.length);

    // this.openCustomItem();

    // setTimeout(() => this.setDelivery(), 100);
    // this.openNote();
  }

  convertPreOrderDateTime(event: Event) {
    if(this.isPreOrder) {
      const { value } = (event.target as HTMLInputElement);
      if(value) {
        const date = new Date(value);

        if(date.getTime() <= Date.now()) {
          this.preOrderError = 'Ngày giờ giao hàng sai, ngày giờ phải lớn hơn thời điểm hiện tại.';
        } else {
          this.preOrderError = undefined;
          this.order.scheduledOrderInfo = {
            expectedDeliveryTime: date
          };
        }
      }
    }
  }

  openDiscount(item?: PosInvoiceItem): void {
    const dialogRef = this.dialog.open(DiscountDialog, {
      data: item
        ? {
          discount: {
            amount: item.discount ?? 0
          },
          max: item.total,
          title: `Giảm giá cho ${item.quantity} suất ${item.name}`
        }
        : {
          discount: this.order.discountWholeOrder,
          max: this.order.summarize.totalRevenue,
          title: 'Giảm giá trên tổng giá trị đơn hàng'
        }
    });

    dialogRef.afterClosed()
      .subscribe(
        {
          next: (discount: PosInvoice['discountWholeOrder']) => {
            if(discount?.amount !== undefined) {
              if(!item) {
                this.order.discountWholeOrder = discount;
                this.order.discounts = [
                  {
                    amount: discount.amount,
                    name: discount.name ?? 'Giảm giá toàn hóa đơn'
                  }
                ];
              } else {
                item.discount = discount.amount;
              }

              buildFullPosOrder(this.order);
            }
          },
          error: () => {
          }
        }
      );
  }

  openDelete(): void {
    const dialogRef = this.dialog.open(DeleteInvoiceDialog, {
      data: this.order
    });

    dialogRef.afterClosed()
      .subscribe(
        {
          next: (value: boolean) => {
            if(value) {
              this.http.post<PosInvoice>(
                'cashier',
                'cancel_pos_invoice',
                {
                  _id: this.order._id
                },
                {
                  context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
                    useLoader: true
                  })
                }
              )
                .subscribe(
                  {
                    next: (data) => {
                      if(data.status) {
                        this.order.status = data.status;
                        this.order.statusText = data.statusText;
                        this.order.isFinalized = true;
                      }
                      this.close(true);
                    },
                    error: () => {
                    }
                  }
                );
            }
          },
          error: () => {
          }
        }
      );
  }

  openNote(item?: PosInvoiceItem): void {
    const dialogRef = this.dialog.open(NoteInvoiceDialog, {
      data: {
        note: item?.note,
        title: item ? `Ghi chú cho ${item?.quantity} suất ${item?.name}` : 'Ghi chú',
        invoice: this.order,
        isItemNote: !!item
      }
    });

    dialogRef.afterClosed()
      .subscribe(
        {
          next: (value: string) => {
            if(item) {
              item.note = value;
            }
          },
          error: () => {
          }
        }
      );
  }

  openImage(url: string) {
    this.dialog.open(ReviewGalleryDialog, {
      data: url,
      disableClose: true,
      panelClass: ['full-screen-modal']
    });
  }

  call(phone: string, event: Event) {
    this.appChannel.launch(`tel:${phone}`, event);
  }

  print() {
    if(!this.commonService.hasFlutterAppCommunicate()) {
      this.pageMessage.error('Máy của bạn không hỗ trợ.');
      return;
    }

    this.http.get<{pos_print_invoice: InvoiceBill}>(
      'cashier',
      `pos_print_invoice&id=${this.order._id}`,
      {
        context: new HttpContext().set(HTTP_REQUEST_OPTIONS, {
          useLoader: true
        })
      }
    ).subscribe({
      next: (data) => {
        this.appChannel.send({
          type: 'printer',
          data: data.pos_print_invoice
        });
      }
    });
  }

  preOrder() {
    if(this.order.invoiceId) {
      this.pageMessage.error('Không thể sửa hóa đơn hiện tại thành Đơn đặt trước, vui lòng tạo đơn mới.');
      return;
    }

    this.isPreOrder = !this.isPreOrder;

    if(this.isPreOrder) {
      this.order.status = 'preorder';
      this.order.scheduledOrderInfo = {};
    } else {
      this.order.status = this.originalInvoiceStatus;
      delete this.order.scheduledOrderInfo;
      this.preOrderTime = undefined;
    }
  }

  edit() {
    this.router.navigate(
      ['cashier/order'],
      {
        queryParams: {
          _id: this.order._id
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  copyInvoice() {
    this.router.navigate(
      ['cashier/order'],
      {
        queryParams: {
          copy_id: this.order._id
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  submit() {
    buildFullPosOrder(this.order);

    if(!this.order.items?.length) {
      this.pageMessage.error('Không thể lưu hóa đơn trống.');
      return;
    }

    this.isSubmitting = true;

    this.http.post('cashier', 'save_pos_invoice', this.order)
      .subscribe(
        {
          next: () => {
            this.isSubmitting = false;
            this.close(true);
          },
          error: () => {
            this.isSubmitting = false;
          }
        }
      );
  }

  setPreparing() {
    this.isSubmitting = true;

    buildFullPosOrder(this.order);

    this.http.post('cashier', 'set_pos_invoice_preparing', this.order)
      .subscribe(
        {
          next: () => {
            this.isSubmitting = false;
            this.close(true);
          },
          error: () => {
            this.isSubmitting = false;
          }
        }
      );
  }

  setDelivery() {
    const dialogRef = this.dialog.open(DeliveryInvoiceDialog, {
      disableClose: true
    });

    dialogRef.afterClosed()
      .subscribe(
        {
          next: (value: { deliveryTime: Date, sendDeliveryStatusToCustomer: boolean }) => {
            if(value) {
              this.order.status = 'delivering';
              this.order.sendDeliveryStatusToCustomer = value.sendDeliveryStatusToCustomer;
              this.order.times ||= {};
              this.order.times.deliveredAt = value.deliveryTime;

              return this.submit();
            }
          },
          error: () => {
          }
        }
      );
  }

  setCompleted() {
    this.order.status = 'completed';
    return this.submit();
  }

  setSave() {
    if(this.order.status !== 'preorder') {
      this.order.status = 'preparing';
    }

    return this.submit();
  }

  showCustomerDetails() {
    this.router.navigate(
      ['frame'],
      {
        queryParams: {
          url: `/manage/customer/view?id=${this.order.customer._id}`
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  showCustomerInvoices() {
    this.router.navigate(
      [ROUTER_LINKS.cashier.invoices],
      {
        queryParams: {
          customer_id: this.order.customer._id
        }
      }
    ).then(() => {
      this.close(false);
      scrollTop();
    });
  }

  saveAs(uri: string, filename: string) {
    const link = document.createElement('a');

    if (typeof link.download === 'string') {
      link.href = uri;
      link.download = filename;

      // Firefox requires the link to be in the body
      document.body.appendChild(link);

      // simulate click
      link.click();

      // remove the link when done
      document.body.removeChild(link);
    } else {
      window.open(uri);
    }
  }

  renderBill() {
    if(!this.isRenderBill) {
      this.loader.setLoader(true);
      this.isRenderBill = true;
    }
  }

  async shareBill() {
    try {
      const canvas = await html2canvas(
        document.querySelector('app-invoice-bill') as HTMLElement,
        {
          useCORS: true
        }
      );
      const dataUrl = canvas.toDataURL();

      const blob = await (await fetch(dataUrl)).blob();

      this.loader.setLoader(false);

      let isSuccessfullCopy = false;
      let isSuccessfullShare = false;

      if(navigator.clipboard?.write) {
        try {
          await navigator.clipboard.write([
            new ClipboardItem({
              'image/png': blob
            })
          ]);
          this.pageMessage.success('Đã copy bill vào clipboard');
          isSuccessfullCopy = true;
        } catch(err: any) {
          this.pageMessage.error(`Lỗi khi copy bill vào clipboard: ${err.message}`);
        }
      } else {
        this.pageMessage.error('App không hỗ trợ copy vào clipboard.');
      }

      isSuccessfullShare = await this.appChannel.share({
        text: `Hóa đơn: ${this.order.invoiceId}`,
        files: [
          new File(
            [blob],
            'bill_image.png',
            {
              type: blob.type,
              lastModified: new Date().getTime()
            }
          )
        ],
        base64Image: dataUrl
      });

      if(!isSuccessfullCopy && !isSuccessfullShare) {
        this.pageMessage.msg('Lưu bill vào máy...', 'info');
        this.saveAs(dataUrl, 'bill_image.png');
      }
    } catch(e: any) {
      this.pageMessage.error(e.message);
    }

    this.isRenderBill = false;
  }

  close(submited: boolean) {
    this._bottomSheetRef.dismiss(submited);
  }

  get hasPreOrderButton() {
    return !this.order.invoiceId && !this.order.isFinalized && !this.order.isThirdPartyInvoice;
  }

  get hasActionButtons() {
    return this.order.paymentMethod &&
    (
      this.order.status !== 'preorder' ||
      (this.order.status === 'preorder' &&
        !this.preOrderError &&
        this.order.scheduledOrderInfo?.expectedDeliveryTime
      )
    );
  }

  get hasCompleteButton() {
    return this.order.paymentMethod &&
    this.order.paymentMethod !== 'unspecified' &&
    !this.order.isThirdPartyInvoice &&
    !this.hasDeliveryButton &&
    this.order.status !== 'preorder'
    ;
  }

  get hasDeliveryButton() {
    return this.order.paymentMethod &&
    this.order.paymentMethod !== 'unspecified' &&
    this.order.diningOption === 'takeaway' &&
    this.order.customer?.phoneNumber &&
    this.order.delivery?.company?.name &&
    !this.order.isThirdPartyInvoice &&
    this.order.status !== 'preorder'
    ;
  }

  get hasPrepareButton() {
    return this.order.status === 'preorder' && !!this.order.invoiceId;
  }

  get hasButton() {
    return !this.order.isThirdPartyInvoice &&
      !this.order.isFinalized &&
      !['delivering', 'completed'].includes(this.order.status as any)
    ;
  }

  get saveButtonText() {
    if(this.order.status !== 'preorder') {
      if(!this.order.invoiceId) {
        return 'Chuẩn bị';
      }
    }

    return 'Lưu';
  }

  get mapDirUrl() {
    if(
      this.store?.addressInfo?.lat &&
      this.store?.addressInfo?.lng &&
      this.order.customer?.address?.lat &&
      this.order.customer?.address?.lng
    ) {
      return getGoogleMapDirectionUrl(
        this.store.addressInfo as Coordinates,
        this.order.customer.address as Coordinates
      );
    }

    return '';
  }

  get mapPlaceDisplayUrl() {
    if(
      this.order.customer?.address?.lat &&
      this.order.customer?.address?.lng
    ) {
      return getGoogleMapDisplayUrl(
        this.order.customer.address as Coordinates
      );
    }

    return '';
  }

  get mapPlaceStreetViewUrl() {
    if(
      this.order.customer?.address?.lat &&
      this.order.customer?.address?.lng
    ) {
      return getGoogleMapStreetViewUrl(
        this.order.customer.address as Coordinates
      );
    }

    return '';
  }
}
