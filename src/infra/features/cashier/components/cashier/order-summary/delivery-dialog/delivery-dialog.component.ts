import { Mat<PERSON><PERSON>ogA<PERSON>, Mat<PERSON><PERSON>ogClose, MatD<PERSON>ogContent, MatD<PERSON>ogRef, MatDialogTitle } from "@angular/material/dialog";
import { formatDateTimeLocalValue } from "@shared/utils";
import { MatButtonToggleModule } from "@angular/material/button-toggle";
import { ChangeDetectionStrategy, Component } from "@angular/core";
import { MatSelectModule } from "@angular/material/select";
import { FormatStringPipe } from "@shared/pipes/format_str.pipe";
import { MatButtonModule } from "@angular/material/button";
import { FormsModule } from "@angular/forms";
import { MatInputModule } from "@angular/material/input";
import { MatFormFieldModule } from "@angular/material/form-field";
import { NoteInvoiceDialog } from "../note-dialog/note-dialog.component";

@Component({
  selector: 'invoice-delivery-dialog',
  templateUrl: 'delivery.dialog.html',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    FormsModule,
    MatButtonModule,
    MatDialogTitle,
    MatDialogContent,
    MatDialogActions,
    MatSelectModule,
    MatButtonToggleModule
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeliveryInvoiceDialog {
  deliveryTime: string;
  deliveryTimeError = false;
  sendDeliveryStatusToCustomer = true;

  constructor(
    public dialogRef: MatDialogRef<NoteInvoiceDialog>) {
    this.deliveryTime = formatDateTimeLocalValue(new Date());
  }

  convertDeliveryTime(event: Event) {
    const { value } = (event.target as HTMLInputElement);
    if(value) {
      this.deliveryTime = formatDateTimeLocalValue(new Date(value));
    }
  }

  exit(): void {
    this.dialogRef.close();
  }

  close(): void {
    this.dialogRef.close({
      deliveryTime: new Date(this.deliveryTime),
      sendDeliveryStatusToCustomer: this.sendDeliveryStatusToCustomer
    });
  }
}