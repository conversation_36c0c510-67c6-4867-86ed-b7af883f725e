.invoice-bill {
  width: 100%;
  max-width: 600px;
  color: #000;

  .invoice-id {
    text-transform: uppercase;
    font-weight: 700;
    margin-top: 18px;
    font-size: 17px;
  }

  .res-info {
    font-weight: 500;
  }

  .bill-customer {
    border: 1px solid #cfcfcf;
    border-radius: 8px;

    &-header {
      font-size: 14px;
      text-transform: uppercase;
      border-bottom: 1px solid #ddd;
      padding: 10px;
      font-weight: bold;
    }
    &-body {
      padding: 10px;

      .label {
        width: 120px;
        color: #6d6d6d;
      }
      .value {
        width: calc(100% - 120px);
      }
    }
  }
  .bill-main-container {
    border: 1px solid #cfcfcf;
    border-radius: 8px;
  }
  .bill-item-header {
    font-size: 14px;
  }
  .bill-item {
    padding: 10px;
    border-bottom: 1px solid #ddd;


    &.no-border {
      border: 0;
    }

    .product {
      width: calc(100%  - 210px);
    }
    .qty {
      width: 50px;
    }
    .unit {
      width: 60px;
    }
    .itotal {
      width: 100px;
    }
  }
  .discounts {
    .name {
      width: calc(100% - 100px);
    }
    .value {
      width: 100px;
    }
  }

  .final-total {
    font-size: 18px;
  }
  .note {
    color: #ff9800;
  }
  .icon-discount {
    font-size: 15px;
    margin-right: 5px;
    color: #07bd07;
  }

  .px-10px {
    padding: 0 10px;
  }
  .py-10px {
    padding: 10px 0;
  }
  .p-10px {
    padding: 10px;
  }
  .ps-10px {
    padding-left: 10px;
  }
}

