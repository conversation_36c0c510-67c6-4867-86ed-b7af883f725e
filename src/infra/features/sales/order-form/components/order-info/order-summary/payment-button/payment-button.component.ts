import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-payment-button',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule
  ],
  templateUrl: './payment-button.component.html',
  styleUrls: ['./payment-button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PaymentButtonComponent {
  @Input() totalAmount = 0;
  @Input() isValid = false;
  @Output() submitClicked = new EventEmitter<void>();

  onSubmitClick(): void {
    if (this.isValid) {
      this.submitClicked.emit();
    }
  }
}
