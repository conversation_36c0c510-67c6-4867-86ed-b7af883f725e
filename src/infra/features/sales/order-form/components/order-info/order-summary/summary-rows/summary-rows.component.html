<ng-container *ngIf="summary as summ">
  <div class="summary-rows">
    <!-- Tổng tiền hàng -->
    <div class="summary-row">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.TOTAL_ITEMS_AMOUNT' | translate }}</div>
      <div class="value">{{ summ.totalItemsAmount | currency:'VND':'symbol':'1.0-0' }}</div>
    </div>

    <!-- Giảm giá -->
    <div class="summary-row" *ngIf="summ.discountAmount">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.DISCOUNT_AMOUNT' | translate }}</div>
      <div class="value text-danger">-{{ summ.discountAmount | currency:'VND':'symbol':'1.0-0' }}</div>
    </div>

    <!-- <PERSON><PERSON> vận chuyển -->
    <div class="summary-row" *ngIf="summ.deliveryFee">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.DELIVERY_FEE' | translate }}</div>
      <div class="value">{{ summ.deliveryFee | currency:'VND':'symbol':'1.0-0' }}</div>
    </div>

    <!-- Phụ thu -->
    <div class="summary-row">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.SURCHARGE_FEE' | translate }}</div>
      <div class="value input-value">
        <mat-form-field appearance="outline" class="amount-input">
          <input matInput type="number" min="0" [ngModel]="summ.surchargeFee || 0"
            (ngModelChange)="onSurchargeFeeChange($event)" [placeholder]="'COMMON.ENTER_AMOUNT' | translate">
          <span matSuffix>VND</span>
        </mat-form-field>
      </div>
    </div>

    <!-- Khách cần trả -->
    <div class="summary-row total-row">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.FINAL_AMOUNT' | translate }}</div>
      <div class="value fw-bold">{{ summ.finalAmount | currency:'VND':'symbol':'1.0-0' }}</div>
    </div>

    <!-- Khách thanh toán -->
    <div class="summary-row">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.PAID_AMOUNT' | translate }}</div>
      <div class="value input-value">
        <mat-form-field appearance="outline" class="amount-input">
          <input matInput type="number" min="0" [ngModel]="summ.paidAmount || 0"
            (ngModelChange)="onPaidAmountChange($event)" [placeholder]="'COMMON.ENTER_AMOUNT' | translate"
            [matAutocomplete]="auto">
          <span matSuffix>VND</span>
          <mat-autocomplete #auto="matAutocomplete">
            <div class="quick-payment-options">
              <div class="quick-payment-row">
                <mat-option *ngFor="let amount of quickPaymentOptions.slice(0, 4)" [value]="amount">
                  {{ amount | currency:'VND':'symbol':'1.0-0' }}
                </mat-option>
              </div>
              <div class="quick-payment-row">
                <mat-option *ngFor="let amount of quickPaymentOptions.slice(4, 8)" [value]="amount">
                  {{ amount | currency:'VND':'symbol':'1.0-0' }}
                </mat-option>
              </div>
            </div>
          </mat-autocomplete>
        </mat-form-field>
      </div>
    </div>

    <!-- Phương thức thanh toán -->
    <div class="summary-row">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.PAYMENT_METHOD' | translate }}</div>
      <div class="value">
        <mat-radio-group [ngModel]="paymentMethod" (ngModelChange)="onPaymentMethodChange($event)" class="payment-method-group">
          <div class="payment-radio-buttons">
            <mat-radio-button value="cash">{{ 'SALES.ORDER_FORM.PAYMENT_METHOD.CASH' | translate }}</mat-radio-button>
            <mat-radio-button value="bank_transfer">{{ 'SALES.ORDER_FORM.PAYMENT_METHOD.BANK_TRANSFER' | translate }}</mat-radio-button>

            <button mat-icon-button [matMenuTriggerFor]="moreMenu">
              <mat-icon>more_vert</mat-icon>
            </button>

            <mat-menu #moreMenu="matMenu">
              <button mat-menu-item (click)="onPaymentMethodChange('credit_card')">
                <mat-icon>credit_card</mat-icon>
                {{ 'SALES.ORDER_FORM.PAYMENT_METHOD.CREDIT_CARD' | translate }}
                <mat-icon *ngIf="paymentMethod === 'credit_card'">check</mat-icon>
              </button>
              <button mat-menu-item (click)="onPaymentMethodChange('ewallet')">
                <mat-icon>account_balance_wallet</mat-icon>
                {{ 'SALES.ORDER_FORM.PAYMENT_METHOD.EWALLET' | translate }}
                <mat-icon *ngIf="paymentMethod === 'ewallet'">check</mat-icon>
              </button>
              <button mat-menu-item (click)="openMixedPaymentDialog()">
                <mat-icon>shuffle</mat-icon>
                {{ 'SALES.ORDER_FORM.PAYMENT_METHOD.MIXED' | translate }}
                <mat-icon *ngIf="isMixedPayment">check</mat-icon>
              </button>
            </mat-menu>
          </div>

          <!-- Bank options khi chọn chuyển khoản -->
          <div *ngIf="paymentMethod === 'bank_transfer'" class="bank-transfer-options">
            <div class="qr-code">
              <img src="https://images.squarespace-cdn.com/content/v1/5d3f241fa4e0350001fa20d5/*************-AIZAXV2978MGIDQE0GT7/qr-code.png" alt="QR Code" width="100">
            </div>
            <mat-form-field appearance="outline" class="bank-select">
              <mat-label>{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.BANK_ACCOUNT' | translate }}</mat-label>
              <mat-select [ngModel]="selectedBank" (ngModelChange)="onBankChange($event)">
                <mat-option *ngFor="let bank of mockBankList" [value]="bank.id">
                  {{ bank.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </mat-radio-group>

        <!-- Hiển thị thông tin thanh toán hỗn hợp nếu có -->
        <div *ngIf="isMixedPayment && order?.payment?.payments && order.payment.payments.length > 1" class="mixed-payment-info">
          <div class="mixed-payment-label">{{ 'SALES.ORDER_FORM.PAYMENT_METHOD.MIXED_DETAILS' | translate }}:</div>
          <div class="mixed-payment-details">
            <ng-container *ngFor="let payment of order.payment.payments; let last = last">
              {{ payment.amount | currency:'VND':'symbol':'1.0-0' }}
              {{ 'SALES.ORDER_FORM.PAYMENT_METHOD.' + payment.paymentMethod.toUpperCase() | translate }}
              {{ !last ? ', ' : '' }}
            </ng-container>
          </div>
          <button mat-icon-button (click)="openMixedPaymentDialog()">
            <mat-icon>edit</mat-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- Trả lại khách -->
    <div class="summary-row" *ngIf="summ.change > 0">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.CHANGE' | translate }}</div>
      <div class="value text-success">{{ summ.change | currency:'VND':'symbol':'1.0-0' }}</div>
    </div>

    <!-- Công nợ khách -->
    <div class="summary-row" *ngIf="summ.debt > 0">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.DEBT' | translate }}</div>
      <div class="value text-warning">{{ summ.debt | currency:'VND':'symbol':'1.0-0' }}</div>
    </div>

    <!-- Dư nợ hiện tại -->
    <div class="summary-row" *ngIf="summ.currentDebt > 0">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.CURRENT_DEBT' | translate }}</div>
      <div class="value text-warning">{{ summ.currentDebt | currency:'VND':'symbol':'1.0-0' }}</div>
    </div>

    <!-- Thời gian giao hàng -->
    <div class="summary-row">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.DELIVERY_TIME' | translate }}</div>
      <div class="value">
        <mat-form-field appearance="outline" class="w-100">
          <mat-select [ngModel]="deliveryOption" (ngModelChange)="onDeliveryOptionChange($event)">
            <mat-option value="asap">{{ 'SALES.ORDER_FORM.DELIVERY_TIME.ASAP' | translate }}</mat-option>
            <mat-option value="scheduled">{{ 'SALES.ORDER_FORM.DELIVERY_TIME.SCHEDULED' | translate }}</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field *ngIf="deliveryOption === 'scheduled'" appearance="outline" class="w-100 mt-2">
          <mat-label>{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.SCHEDULED_DELIVERY_TIME' | translate }}</mat-label>
          <input matInput [matDatepicker]="deliveryPicker" [ngModel]="scheduledTime" (dateChange)="onScheduledTimeChange($event)">
          <mat-datepicker-toggle matSuffix [for]="deliveryPicker"></mat-datepicker-toggle>
          <mat-datepicker #deliveryPicker></mat-datepicker>
        </mat-form-field>
      </div>
    </div>

    <!-- Trạng thái -->
    <div class="summary-row">
      <div class="label">{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.STATUS' | translate }}</div>
      <div class="value">
        <mat-form-field appearance="outline" class="w-100">
          <mat-select [ngModel]="order?.status" (ngModelChange)="onStatusChange($event)">
            <mat-option *ngFor="let status of mockOrderStatuses" [value]="status.id">
              {{ status.name }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>
</ng-container>
