<div class="payment-details">
  <!-- Row 1: Thông Tin Cơ Bản -->
  <div class="basic-info-row">
    <!-- <PERSON><PERSON><PERSON> bên trái -->
    <div class="left-column">
      <!-- Nh<PERSON> viên bán hàng -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'COMMON.EMPLOYEE' | translate }}</mat-label>
        <mat-select [value]="order?.createdBy?._id" (selectionChange)="onEmployeeChange($event.value)">
          <mat-option *ngFor="let employee of mockEmployeeList" [value]="employee._id">
            {{ employee.name }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="!order?.createdBy">{{ 'VALIDATION.REQUIRED' | translate }}</mat-error>
      </mat-form-field>

      <!-- <PERSON><PERSON><PERSON> b<PERSON> hàng -->
      <mat-form-field appearance="outline" class="w-100 mt-2">
        <mat-label>{{ 'COMMON.SALE_CHANNEL' | translate }}</mat-label>
        <mat-select [value]="order?.saleChannel?._id" (selectionChange)="onSaleChannelChange($event.value)">
          <mat-option *ngFor="let channel of mockSaleChannelList" [value]="channel._id">
            {{ channel.displayName }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="!order?.saleChannel">{{ 'VALIDATION.REQUIRED' | translate }}</mat-error>
      </mat-form-field>
    </div>

    <!-- Phần bên phải -->
    <div class="right-column">
      <!-- Ngày giờ tạo đơn -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'COMMON.CREATED_AT' | translate }}</mat-label>
        <input matInput [matDatepicker]="picker" [value]="order?.times?.createdAt" (dateChange)="onCreatedAtChange($event.value)">
        <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
        <mat-datepicker #picker></mat-datepicker>
      </mat-form-field>
    </div>
  </div>

  <!-- Row 2: Các Nút Hành Động -->
  <div class="action-buttons-row">
    <button mat-button color="primary" (click)="onAddTagClick()">
      <mat-icon>label</mat-icon>
      {{ 'COMMON.ADD_TAG' | translate }}
    </button>

    <button mat-button color="primary" (click)="onNoteClick()">
      <mat-icon>note</mat-icon>
      {{ 'COMMON.NOTE' | translate }}
    </button>

    <button mat-button color="primary" (click)="onDiscountClick()">
      <mat-icon>discount</mat-icon>
      {{ 'COMMON.DISCOUNT' | translate }}
    </button>
  </div>

  <!-- Hiển thị ChipFormFieldComponent khi showTagInput là true -->
  <div class="tags-input-row" *ngIf="showTagInput()">
    <app-chip-form-field
      [ngModel]="tagNames()"
      (valuesChange)="onTagNamesChange($event)"
      placeholder="{{ 'SALES.ORDER_FORM.ORDER_SUMMARY.ENTER_TAGS' | translate }}"
      [label]="'SALES.ORDER_FORM.ORDER_SUMMARY.TAGS' | translate"
    ></app-chip-form-field>
  </div>

  <!-- Hiển thị tags nếu có -->
  <div class="tags-row" *ngIf="order?.tags && order.tags.length > 0 && !showTagInput()">
    <div class="tag-chips">
      <span class="tag-chip" *ngFor="let tag of order.tags">
        {{ tag.name }}
        <mat-icon class="small-icon">close</mat-icon>
      </span>
    </div>
  </div>

  <!-- Hiển thị ghi chú nếu có -->
  <div class="note-row" *ngIf="order?.note">
    <div class="note public-note">
      <strong>{{ 'COMMON.PUBLIC_NOTE' | translate }}:</strong> {{ order.note }}
    </div>
  </div>

  <div class="note-row" *ngIf="order?.internalNote">
    <div class="note internal-note">
      <strong>{{ 'COMMON.INTERNAL_NOTE' | translate }}:</strong> {{ order.internalNote }}
    </div>
  </div>

  <!-- Hiển thị giảm giá nếu có -->
  <div class="discount-row" *ngIf="order?.discounts && order.discounts.length > 0">
    <div class="discount-info">
      <strong>{{ 'COMMON.DISCOUNT_INFO' | translate }}:</strong>
      <span *ngFor="let discount of order.discounts; let last = last">
        {{ discount.type === 'percentage' ? discount.value + '%' : (discount.value | currency:'VND':'symbol':'1.0-0') }}
        {{ discount.name ? '(' + discount.name + ')' : '' }}
        {{ !last ? ', ' : '' }}
      </span>
    </div>
  </div>
</div>
