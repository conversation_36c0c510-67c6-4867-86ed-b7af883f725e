import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { CreateOrderRequest } from 'salehub_shared_contracts/requests/sales/create_order';
import { OrderPayment } from 'salehub_shared_contracts/entities/oms/order/order_components/order_payment';
import { PaymentDetailsComponent } from './payment-details/payment-details.component';
import { PaymentButtonComponent } from './payment-button/payment-button.component';
import { SummaryRowsComponent } from './summary-rows/summary-rows.component';
import { OrderSummaryService } from './order-summary.service';

@Component({
  selector: 'app-order-summary',
  standalone: true,
  imports: [
    CommonModule,
    MatButtonModule,
    MatIconModule,
    TranslateModule,
    PaymentDetailsComponent,
    PaymentButtonComponent,
    SummaryRowsComponent
  ],
  templateUrl: './order-summary.component.html',
  styleUrls: ['./order-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OrderSummaryComponent implements OnInit {
  // Inject service
  private orderSummaryService = inject(OrderSummaryService);

  // Input/Output properties
  @Input() order: CreateOrderRequest;
  @Output() orderUpdated = new EventEmitter<CreateOrderRequest>();
  @Output() submitOrder = new EventEmitter<CreateOrderRequest>();

  // Variables
  isOrderValid = false;
  summary = {
    totalItemsAmount: 0,
    discountAmount: 0,
    deliveryFee: 0,
    surchargeFee: 0,
    finalAmount: 0,
    paidAmount: 0,
    change: 0,
    debt: 0,
    currentDebt: 0
  };

  ngOnInit(): void {
    this.calculateSummary();
  }

  // Phương thức để tính toán tóm tắt đơn hàng
  calculateSummary(): void {
    if (this.order) {
      this.summary = this.orderSummaryService.calculateSummary(this.order);
      this.isOrderValid = this.orderSummaryService.validateOrder(this.order);
    }
  }

  // Phương thức xử lý khi thông tin thanh toán cập nhật
  onPaymentUpdated(payment: OrderPayment): void {
    if (this.order) {
      this.order.payment = payment;
      this.orderUpdated.emit(this.order);
      this.calculateSummary();
    }
  }

  // Phương thức xử lý khi thông tin tóm tắt cập nhật
  onSummaryUpdated(data: any): void {
    if (this.order && data) {
      // Cập nhật phụ thu nếu có
      if (data.surchargeFee !== undefined) {
        if (!this.order.customerFare) {
          this.order.customerFare = {};
        }
        this.order.customerFare.surchargeFee = data.surchargeFee;
      }

      // Cập nhật các trường khác nếu cần
      this.orderUpdated.emit(this.order);
      this.calculateSummary();
    }
  }

  // Phương thức xử lý khi người dùng nhấn nút thanh toán
  onSubmitClicked(): void {
    if (this.isOrderValid) {
      const finalOrder = this.orderSummaryService.buildCreateOrderRequest(this.order);
      this.submitOrder.emit(finalOrder);
    }
  }

  // Phương thức cập nhật dữ liệu cơ bản
  onBasicInfoUpdated(data: { createdBy: any, saleChannel: any, createdAt: Date }): void {
    if (this.order) {
      this.order.createdBy = data.createdBy;
      this.order.saleChannel = data.saleChannel;
      if (data.createdAt) {
        this.order.times = {
          ...this.order.times,
          createdAt: data.createdAt
        };
      }
      this.orderUpdated.emit(this.order);
    }
  }
}
