import { mockCustomerAutocompleteList } from '@/mock/shared/customer_autocomplete_list.mock';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

/**
 * Service xử lý các tác vụ liên quan đến khách hàng
 */
@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  /**
   * Tìm kiếm khách hàng dựa trên query (tên hoặc số điện thoại)
   * @param query Từ khóa tìm kiếm (tên hoặc số điện thoại)
   * @returns Danh sách khách hàng phù hợp với query
   */
  getCustomerSuggestions(query: string): Observable<any[]> {
    if (!query || query.length < 3) {
      return of([]);
    }

    const lowercaseQuery = query.toLowerCase();
    const filteredCustomers = mockCustomerAutocompleteList.filter(customer =>
      customer.name.toLowerCase().includes(lowercaseQuery) ||
      customer.phoneNumber.includes(query)
    );

    return of(filteredCustomers);
  }

  /**
   * Cập nhật thông tin khách hàng vào đơn hàng
   * @param order Đơn hàng cần cập nhật
   * @param customer Thông tin khách hàng mới
   * @returns Đơn hàng đã được cập nhật
   */
  updateCustomer(order: any, customer: any): any {
    if (!order.customer) {
      order.customer = {};
    }

    // Cập nhật thông tin khách hàng
    order.customer = {
      ...order.customer,
      ...customer
    };

    return order;
  }
}
