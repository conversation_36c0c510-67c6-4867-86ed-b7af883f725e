<mat-expansion-panel [expanded]="expanded" class="mb-3">
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_INFO' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>

  <div class="row">
    <!-- S<PERSON> điện thoại -->
    <div class="col-12 col-md-6 mb-3">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_PHONE' | translate }}</mat-label>
        <input matInput [(ngModel)]="customer.phoneNumber"
               (input)="onPhoneSearch($any($event.target).value)"
               (change)="onInputChange()"
               [matAutocomplete]="phoneAuto">
        <mat-autocomplete #phoneAuto="matAutocomplete">
          <mat-option *ngFor="let suggestion of customerSuggestions"
                      [value]="suggestion.phoneNumber"
                      (click)="selectCustomer(suggestion)">
            <div class="d-flex align-items-center">
              <div>
                <div class="fw-bold">{{ suggestion.phoneNumber }}</div>
                <div class="text-muted small">{{ suggestion.name }}</div>
              </div>
            </div>
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
    </div>

    <!-- Tên khách hàng -->
    <div class="col-12 col-md-6 mb-3">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_NAME' | translate }}</mat-label>
        <input matInput [(ngModel)]="customer.name"
               (input)="onNameSearch($any($event.target).value)"
               (change)="onInputChange()"
               [matAutocomplete]="nameAuto">
        <mat-autocomplete #nameAuto="matAutocomplete">
          <mat-option *ngFor="let suggestion of customerSuggestions"
                      [value]="suggestion.name"
                      (click)="selectCustomer(suggestion)">
            <div class="d-flex align-items-center">
              <div>
                <div class="fw-bold">{{ suggestion.name }}</div>
                <div class="text-muted small">{{ suggestion.phoneNumber }}</div>
              </div>
            </div>
          </mat-option>
        </mat-autocomplete>
      </mat-form-field>
    </div>
  </div>

  <!-- Thông tin chi tiết về khách hàng khi đã chọn -->
  <div class="row mb-3" *ngIf="showCustomerDetails && customer.score">
    <div class="col-12">
      <div class="customer-details p-3 border rounded bg-light">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <!-- Điểm số -->
          <div class="score-display">
            <span class="badge bg-primary rounded-pill p-2">
              {{ customer.score.value }}/10
            </span>
            <span class="ms-2 text-muted small">{{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_SCORE' | translate }}</span>
          </div>

          <!-- Thống kê -->
          <div class="statistics">
            <div class="d-flex gap-3">
              <div class="statistic-item text-center">
                <div class="text-success fw-bold">{{ customer.score.orderStatusPercent.success }}%</div>
                <div class="text-muted small">{{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_SUCCESS_RATE' | translate }}</div>
              </div>
              <div class="statistic-item text-center">
                <div class="text-danger fw-bold">{{ customer.score.orderStatusPercent.return }}%</div>
                <div class="text-muted small">{{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_RETURN_RATE' | translate }}</div>
              </div>
              <div class="statistic-item text-center">
                <div class="text-warning fw-bold">{{ customer.score.orderStatusPercent.others }}%</div>
                <div class="text-muted small">{{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_OTHER_RATE' | translate }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Thông tin đơn hàng -->
        <div class="order-info mb-3 text-center text-muted">
          {{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_ORDER_COUNT' | translate }} <span class="fw-bold">{{ customer.countOrders }}</span>
          {{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_TOTAL_VALUE' | translate }}: <span class="fw-bold">{{ customer.totalSold | number }} VND</span>
        </div>

        <!-- Tags -->
        <div class="tags" *ngIf="customer.tags && customer.tags.length > 0">
          <mat-chip-set>
            <mat-chip *ngFor="let tag of customer.tags"
                    [class]="getChipColor(tag.type)">
              {{ tag.text }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>
    </div>
  </div>

  <!-- Địa chỉ -->
  <div class="row mb-3">
    <div class="col-12">
      <input-place
        [defaultValue]="customer.address"
        [placeholder]="'SALES.ORDER_FORM.ORDER.CUSTOMER_ADDRESS' | translate"
        (selectedPlace)="onAddressChange($event)">
      </input-place>
    </div>
  </div>

  <!-- Ghi chú -->
  <div class="row">
    <div class="col-12">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.ORDER.CUSTOMER_NOTE' | translate }}</mat-label>
        <textarea matInput [(ngModel)]="customer.note"
                  rows="3"
                  (change)="onNoteChange()"></textarea>
      </mat-form-field>
    </div>
  </div>
</mat-expansion-panel>
