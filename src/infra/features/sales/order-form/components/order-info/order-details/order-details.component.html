<mat-expansion-panel [expanded]="panelExpanded" (opened)="panelExpanded = true" (closed)="panelExpanded = false" class="order-details-panel">
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'SALES.ORDER_FORM.ORDER_DETAILS.TITLE' | translate }}
    </mat-panel-title>
    <mat-panel-description *ngIf="items?.length">
      {{ items.length }} {{ 'SALES.ORDER_FORM.ORDER_DETAILS.ITEMS' | translate }}
    </mat-panel-description>
  </mat-expansion-panel-header>

  <!-- Tìm kiếm sản phẩm -->
  <div class="search-container" (click)="openProductMenu()">
    <mat-form-field appearance="outline" class="search-input">
      <mat-label>{{ 'SALES.ORDER_FORM.ORDER_DETAILS.SEARCH_PLACEHOLDER' | translate }}</mat-label>
      <input
        matInput
        #searchInput
        [ngModel]="searchKeyword()"
        (ngModelChange)="onSearchKeywordChange($event)"
        placeholder="{{ 'SALES.ORDER_FORM.ORDER_DETAILS.SEARCH_PLACEHOLDER' | translate }}"
        (click)="onInputClick($event)"
      />
      <button *ngIf="searchKeyword()" matSuffix mat-icon-button aria-label="Clear" (click)="clearSearch()">
        <mat-icon>close</mat-icon>
      </button>
    </mat-form-field>
  </div>

  <!-- Danh sách sản phẩm -->
  <div class="product-list">
    <!-- Tiêu đề cột -->
    <div class="product-list-header">
      <div class="product-name">{{ 'SALES.ORDER_FORM.ORDER_DETAILS.PRODUCT_NAME' | translate }}</div>
      <div class="product-quantity">{{ 'SALES.ORDER_FORM.ORDER_DETAILS.QUANTITY' | translate }}</div>
      <div class="product-price">{{ 'SALES.ORDER_FORM.ORDER_DETAILS.PRICE' | translate }}</div>
      <div class="product-total">{{ 'SALES.ORDER_FORM.ORDER_DETAILS.TOTAL' | translate }}</div>
      <div class="product-actions">{{ 'SALES.ORDER_FORM.ORDER_DETAILS.ACTIONS' | translate }}</div>
    </div>

    <!-- Danh sách sản phẩm -->
    <div class="product-list-items">
      <div *ngIf="!items?.length" class="no-products">
        {{ 'SALES.ORDER_FORM.ORDER_DETAILS.NO_PRODUCTS' | translate }}
      </div>

      <div *ngFor="let item of items; let i = index" class="product-item">
        <!-- Tên sản phẩm -->
        <div class="product-name">
          <div class="product-name-main">{{ item.product?.name }}</div>
          <div class="product-name-info" *ngIf="item.product?.variant || item.product?.unit">
            <span *ngIf="item.product?.variant" class="variant-info clickable" (click)="openVariantSelector(i)">
              <span *ngFor="let attr of item.product?.variant?.attributes || []">
                {{ attr.name }}: {{ attr.value }}
              </span>
              <mat-icon class="edit-icon">edit</mat-icon>
            </span>
            <span *ngIf="item.product?.unit" class="unit-info clickable" (click)="openUnitSelector(i)">
              {{ 'VARIANT_SELECTOR.UNIT' | translate }}: {{ item.product?.unit?.unitName }}
              <mat-icon class="edit-icon">edit</mat-icon>
            </span>
          </div>
        </div>

        <!-- Số lượng -->
        <div class="product-quantity">
          <div class="quantity-controls">
            <button mat-icon-button color="primary" class="quantity-btn" (click)="decreaseQuantity(i)">
              <mat-icon>remove</mat-icon>
            </button>
            <input
              type="number"
              min="1"
              class="form-control quantity-input"
              [ngModel]="item.quantity"
              (ngModelChange)="updateQuantity(i, $event)"
            />
            <button mat-icon-button color="primary" class="quantity-btn" (click)="increaseQuantity(i)">
              <mat-icon>add</mat-icon>
            </button>
          </div>
        </div>

        <!-- Giá -->
        <div class="product-price">
          <input
            type="number"
            min="0"
            class="form-control"
            [ngModel]="item.product?.userOverride?.price"
            (ngModelChange)="updatePrice(i, $event)"
          />
          <!-- Hiển thị giảm giá nếu có -->
          <div *ngIf="item.discount && item.discount > 0" class="discount-info">
            <div class="original-price">
              {{ item.product?.price | currency:'VND':'symbol':'1.0-0' }}
            </div>
            <div class="discount-amount">
              -{{ item.discount | currency:'VND':'symbol':'1.0-0' }}
            </div>
          </div>
        </div>

        <!-- Thành tiền -->
        <div class="product-total">
          {{ calculateItemTotal(item) | currency:'VND':'symbol':'1.0-0' }}
        </div>

        <!-- Thao tác -->
        <div class="product-actions">
          <button
            mat-icon-button
            color="warn"
            [matTooltip]="'SALES.ORDER_FORM.ORDER_DETAILS.REMOVE' | translate"
            (click)="removeItem(i)"
          >
            <mat-icon>delete</mat-icon>
          </button>

          <!-- Nút thêm modifier (tuỳ chọn) -->
          <button
            *ngIf="item.product?.linkedModifierGroupIds?.length"
            mat-icon-button
            color="primary"
            [matTooltip]="'SALES.ORDER_FORM.ORDER_DETAILS.ADD_MODIFIERS' | translate"
            (click)="openModifiersSheet(i)"
          >
            <mat-icon>add_circle</mat-icon>
          </button>

          <!-- Nút more với menu -->
          <button
            mat-icon-button
            [matMenuTriggerFor]="moreMenu"
            [matTooltip]="'SALES.ORDER_FORM.ORDER_DETAILS.MORE_OPTIONS' | translate"
          >
            <mat-icon>more_vert</mat-icon>
          </button>

          <!-- Menu cho nút more -->
          <mat-menu #moreMenu="matMenu">
            <button mat-menu-item (click)="openPromotionDialog(i)">
              <mat-icon color="accent">loyalty</mat-icon>
              <span>{{ 'SALES.ORDER_FORM.ORDER_DETAILS.ADD_PROMOTION' | translate }}</span>
            </button>
            <button mat-menu-item (click)="openNoteDialog(i)">
              <mat-icon color="primary">note</mat-icon>
              <span>{{ 'SALES.ORDER_FORM.ORDER_DETAILS.ADD_NOTE' | translate }}</span>
            </button>
          </mat-menu>
        </div>

        <!-- Hiển thị Note nếu có -->
        <div class="product-note" *ngIf="item.note || item.internalNote">
          <div class="modifier-item note-item">
            <div class="product-name">
              <div class="product-name-main">{{ 'SALES.ORDER_FORM.ORDER_DETAILS.NOTE' | translate }}: {{ item.note || item.internalNote }}</div>
            </div>
          </div>
        </div>

        <!-- Hiển thị Modifier Groups nếu có -->
        <div class="modifier-groups" *ngIf="item.modifierGroups && item.modifierGroups.length > 0">
          <div *ngFor="let group of item.modifierGroups; let g = index" class="modifier-group">
            <div *ngFor="let modifier of group.modifiers; let m = index" class="modifier-item">
              <div class="product-name">
                <div class="product-name-main">{{ modifier.product?.name }}</div>
              </div>
              <div class="product-quantity">
                <div class="quantity-controls">
                  <button mat-icon-button color="primary" class="quantity-btn quantity-btn-sm"
                         (click)="decreaseModifierQuantity(i, group._id, m)">
                    <mat-icon>remove</mat-icon>
                  </button>
                  <span class="modifier-quantity">{{ modifier.quantity }}</span>
                  <button mat-icon-button color="primary" class="quantity-btn quantity-btn-sm"
                         (click)="increaseModifierQuantity(i, group._id, m)">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
              </div>
              <div class="product-price">
                {{ modifier.product?.price | currency:'VND':'symbol':'1.0-0' }}
              </div>
              <div class="product-total">
                {{ modifier.quantity * (modifier.product?.price || 0) * item.quantity | currency:'VND':'symbol':'1.0-0' }}
              </div>
              <div class="product-actions">
                <button mat-icon-button color="warn" class="remove-btn-sm"
                        (click)="removeModifier(i, group._id, m)">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</mat-expansion-panel>

<!-- Template cho overlay product selection -->
<!-- <ng-template #productSelectionTpl>
  <div class="product-selection-panel">
    <div class="product-selection-header">
      <span>{{ 'SALES.ORDER_FORM.ORDER_DETAILS.SELECT_PRODUCTS' | translate }}</span>
      <button mat-icon-button color="primary" (click)="closeMenu()">
        <mat-icon>close</mat-icon>
      </button>
    </div>
    <app-product-selection
      [list]="productList"
      [data]="items"
      [searchTerm]="searchKeyword()"
      (addSelectedItems)="addItems($event)"
    ></app-product-selection>
  </div>
</ng-template> -->

