<div class="order-tabs-container">
  <mat-tab-group
    [selectedIndex]="activeTabIndex()"
    (selectedIndexChange)="onTabChange($event)"
    class="order-tabs">
    <mat-tab *ngFor="let order of tabs(); let i = index">
      <ng-template mat-tab-label>
        <div class="tab-label">
          <span>{{ getTabLabel(order, i) }}</span>
          <button
            mat-icon-button
            class="tab-close-btn"
            [matTooltip]="'SALES.ORDER_FORM.CLOSE_TAB' | translate"
            (click)="onRemoveTab(i, $event)">
            <mat-icon>close</mat-icon>
          </button>
        </div>
      </ng-template>
    </mat-tab>
  </mat-tab-group>

  <button
    mat-mini-fab
    color="primary"
    class="add-tab-btn"
    [disabled]="isAddTabDisabled()"
    [matTooltip]="'SALES.ORDER_FORM.ADD_TAB' | translate"
    (click)="onAddTab()">
    <mat-icon>add</mat-icon>
  </button>
</div>
