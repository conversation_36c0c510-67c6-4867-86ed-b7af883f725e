.order-content-container {
  display: flex;
  width: 100%;
  // height: calc(100vh - 180px); // <PERSON><PERSON><PERSON> cao trang trừ đi header, tabs, footer
  padding: 1rem;
  gap: 1rem;

  .order-content-left {
    flex: 7; // Chiếm 70% chi<PERSON>u rộng khi ở desktop
    height: 100%;

    .scrollbar-container {
      height: 100%;

      .order-sections {
        display: flex;
        flex-direction: column;
        gap: 1rem;

        .order-section {
          background-color: #fff;
          border-radius: 0.5rem;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          padding: 1rem;

          h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
            margin-bottom: 1rem;
            font-size: 1.1rem;
            font-weight: 500;
          }
        }
      }
    }
  }

  .order-content-right {
    flex: 3; // Chiếm 30% chiều rộng khi ở desktop
    height: 100%;

    .scrollbar-container {
      height: 100%;

      .order-section {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        padding: 1rem;

        h3 {
          margin-top: 0;
          border-bottom: 1px solid #eee;
          padding-bottom: 0.5rem;
          margin-bottom: 1rem;
          font-size: 1.1rem;
          font-weight: 500;
        }
      }
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .order-content-container {
    flex-direction: column;
    height: auto;

    .order-content-left,
    .order-content-right {
      flex: auto;
      width: 100%;
      height: auto;
      max-height: none;

      .scrollbar-container {
        height: auto;
        max-height: none;
      }
    }

    .order-content-left {
      margin-bottom: 1rem;
    }
  }
}
