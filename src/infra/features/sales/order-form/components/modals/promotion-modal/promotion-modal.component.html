<!-- Dialog header -->
<h2 mat-dialog-title>{{ 'SALES.PROMOTION.TITLE' | translate }}</h2>

<!-- Dialog content -->
<mat-dialog-content>
  <!-- Tổng tiền -->
  <div class="mb-3">
    <label class="form-label">{{ 'SALES.PROMOTION.TOTAL_AMOUNT' | translate }}</label>
    <div class="form-control">{{ totalAmount | number }} đ</div>
  </div>

  <!-- Radio buttons cho loại giảm giá -->
  <mat-radio-group [(ngModel)]="discountType" (ngModelChange)="onDiscountChange()" class="d-flex flex-column gap-3 mb-3">
    <!-- Gi<PERSON>m theo số tiền -->
    <mat-radio-button value="amount" class="d-flex align-items-center">
      <div class="d-flex align-items-center gap-3 w-100">
        <span>{{ 'SALES.PROMOTION.BY_AMOUNT' | translate }}</span>
        <mat-form-field class="flex-grow-1" *ngIf="discountType === 'amount'">
          <input matInput type="number" [(ngModel)]="discountValue" (ngModelChange)="onDiscountChange()"
                 [max]="totalAmount" min="0" [placeholder]="'SALES.PROMOTION.ENTER_AMOUNT' | translate">
        </mat-form-field>
      </div>
    </mat-radio-button>

    <!-- Giảm theo phần trăm -->
    <mat-radio-button value="percent" class="d-flex align-items-center">
      <div class="d-flex align-items-center gap-3 w-100">
        <span>{{ 'SALES.PROMOTION.BY_PERCENT' | translate }}</span>
        <mat-form-field class="flex-grow-1" *ngIf="discountType === 'percent'">
          <input matInput type="number" [(ngModel)]="discountValue" (ngModelChange)="onDiscountChange()"
                 max="100" min="0" [placeholder]="'SALES.PROMOTION.ENTER_PERCENT' | translate">
          <span matSuffix>%</span>
        </mat-form-field>
      </div>
    </mat-radio-button>

    <!-- Giảm theo coupon -->
    <mat-radio-button value="coupon" class="d-flex align-items-center">
      <div class="d-flex align-items-center gap-3 w-100">
        <span>{{ 'SALES.PROMOTION.BY_COUPON' | translate }}</span>
        <mat-form-field class="flex-grow-1" *ngIf="discountType === 'coupon'">
          <input matInput type="text" [placeholder]="'SALES.PROMOTION.ENTER_COUPON' | translate">
        </mat-form-field>
      </div>
    </mat-radio-button>
  </mat-radio-group>

  <!-- Nội dung khuyến mãi -->
  <mat-form-field class="w-100 mb-3">
    <mat-label>{{ 'SALES.PROMOTION.NAME' | translate }}</mat-label>
    <input matInput [(ngModel)]="promotionName" [placeholder]="'SALES.PROMOTION.ENTER_NAME' | translate">
  </mat-form-field>

  <!-- Tổng tiền cuối -->
  <div class="mb-3">
    <label class="form-label">{{ 'SALES.PROMOTION.FINAL_AMOUNT' | translate }}</label>
    <div class="form-control">{{ finalAmount | number }} đ</div>
  </div>
</mat-dialog-content>

<!-- Dialog actions -->
<mat-dialog-actions align="end">
  <button mat-button (click)="onCancel()">
    {{ 'COMMON.CANCEL' | translate }}
  </button>
  <button mat-raised-button color="primary" (click)="onConfirm()" [disabled]="!isValid()">
    {{ 'COMMON.CONFIRM' | translate }}
  </button>
</mat-dialog-actions>
