import { Component, Inject, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatBottomSheetRef, MAT_BOTTOM_SHEET_DATA } from '@angular/material/bottom-sheet';
import { MatRadioModule } from '@angular/material/radio';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { PromotionModalData, PromotionModalResult } from './models/promotion-modal.model';

@Component({
  selector: 'app-promotion-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatRadioModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    TranslateModule
  ],
  templateUrl: './promotion-modal.component.html',
  styleUrls: ['./promotion-modal.component.scss']
})
export class PromotionModalComponent {
  // Các biến local
  discountType: 'amount' | 'percent' | 'coupon';
  discountValue: number;
  promotionName: string;
  finalAmount: number;
  totalAmount: number;
  
  // Dữ liệu từ inject
  data: PromotionModalData;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData: PromotionModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData: PromotionModalData,
    @Optional() private dialogRef?: MatDialogRef<PromotionModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<PromotionModalComponent>
  ) {
    // Kết hợp dữ liệu từ dialogData hoặc bottomSheetData
    this.data = this.dialogData || this.bottomSheetData || { 
      totalAmount: 0
    };
    
    // Khởi tạo giá trị ban đầu từ data được truyền vào hoặc giá trị mặc định
    this.totalAmount = this.data.totalAmount;
    this.discountType = this.data.discountType || 'amount';
    this.discountValue = this.data.discountValue || 0;
    this.promotionName = this.data.promotionName || '';
    this.finalAmount = this.data.finalAmount || this.totalAmount;
  }

  // Xử lý khi thay đổi loại giảm giá hoặc giá trị
  onDiscountChange(): void {
    if (this.discountType === 'amount') {
      this.finalAmount = this.totalAmount - (this.discountValue || 0);
    } else if (this.discountType === 'percent') {
      this.finalAmount = this.totalAmount * (1 - (this.discountValue || 0) / 100);
    } else {
      this.finalAmount = this.totalAmount; // Coupon chưa có logic
    }
    // Đảm bảo finalAmount không âm
    this.finalAmount = Math.max(0, this.finalAmount);
  }

  // Kiểm tra xem form có hợp lệ không
  isValid(): boolean {
    if (this.discountType === 'amount') {
      return this.discountValue >= 0 && this.discountValue <= this.totalAmount;
    } else if (this.discountType === 'percent') {
      return this.discountValue >= 0 && this.discountValue <= 100;
    } else if (this.discountType === 'coupon') {
      // Coupon chưa có logic validation
      return true;
    }
    return false;
  }

  // Xử lý khi nhấn nút hủy
  onCancel(): void {
    this.close(null);
  }

  // Xử lý khi nhấn nút xác nhận
  onConfirm(): void {
    if (this.isValid()) {
      const result: PromotionModalResult = {
        totalAmount: this.totalAmount,
        discountType: this.discountType,
        discountValue: this.discountValue,
        promotionName: this.promotionName,
        finalAmount: this.finalAmount,
        discounts: this.data.discounts
      };
      this.close(result);
    }
  }

  /**
   * Đóng modal
   */
  private close(result: PromotionModalResult | null): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
