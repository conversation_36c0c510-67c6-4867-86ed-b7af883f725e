.modal-container {
  padding: 20px;
  max-width: 800px;
  width: 100%;
}

.modal-title {
  margin-bottom: 20px;
  color: var(--bs-primary);
  font-weight: 500;
}

.payment-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  margin-bottom: 10px;
}

.payments-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.payment-method-row {
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.payment-details {
  margin-top: 16px;
  padding: 16px;
  border-radius: 8px;
  background-color: #f0f0f0;
}

.add-payment-btn-container {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.payment-summary {
  margin-top: 20px;
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f5f5f5;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Styling cho quick amount buttons */
.quick-amounts {
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.quick-amounts-row {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.quick-amount-btn {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f0f0f0;
  cursor: pointer;
  text-align: center;
  transition: all 0.2s;

  &:hover {
    background-color: #e0e0e0;
  }
}

/* QR Code styling */
.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}

.qr-code-image {
  max-width: 100%;
  height: auto;
  max-height: 200px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .quick-amounts-row {
    flex-wrap: wrap;
  }

  .quick-amount-btn {
    min-width: calc(50% - 4px);
  }
}

// Đảm bảo overlay panel luôn hiển thị đúng và đủ rộng
::ng-deep .cdk-overlay-container {
  z-index: 10000;
}

::ng-deep .cdk-overlay-pane {
  min-width: 300px !important;
  width: auto !important;
  max-width: 100% !important;
}

::ng-deep .cdk-overlay-pane.mat-mdc-autocomplete-panel-above,
::ng-deep .cdk-overlay-pane.mat-mdc-autocomplete-panel-below {
  min-width: 300px !important;
  width: auto !important;
  max-width: 100% !important;
}

::ng-deep .mat-mdc-autocomplete-panel,
::ng-deep .mat-autocomplete-panel {
  visibility: visible !important;
  opacity: 1 !important;
  z-index: 10001 !important;
  transform: none !important;
  max-height: none !important;
  display: block !important;
  min-width: 300px !important;
  width: auto !important;
}

::ng-deep .mat-mdc-autocomplete-hidden,
::ng-deep .mat-autocomplete-hidden {
  visibility: visible !important;
  opacity: 1 !important;
  display: block !important;
}

::ng-deep .mdc-menu-surface--open {
  display: block !important;
}

::ng-deep .mat-mdc-form-field {
  overflow: visible !important;
}

::ng-deep .mdc-text-field {
  overflow: visible !important;
}

::ng-deep .mat-mdc-text-field-wrapper {
  overflow: visible !important;
}

.quick-amount-panel {
  &::ng-deep .mat-mdc-option {
    height: auto;
    line-height: normal;
    padding: 0;
  }
}

.d-none {
  display: none !important;
}

// Làm cho container không bị overflow hidden
::ng-deep .mat-mdc-dialog-container,
::ng-deep .mat-bottom-sheet-container {
  overflow: visible !important;
}

::ng-deep .mdc-dialog__container {
  overflow: visible !important;
}

::ng-deep .mat-mdc-dialog-surface {
  overflow: visible !important;
}
