<!-- <PERSON><PERSON><PERSON><PERSON> từ mat-dialog sang đảm bảo hoạt động với cả dialog và bottom sheet -->
<div class="serial-number-modal">
  <h2>{{ 'WAREHOUSE.SERIAL_NUMBER_DIALOG.TITLE' | translate }}</h2>

  <div class="modal-content">
    <div class="product-info">
      <h3>{{ product.name }}</h3>
      <p class="sku">{{ product.sku }}</p>
    </div>

    <div class="serial-count">
      <p>{{ 'WAREHOUSE.SERIAL_NUMBER_DIALOG.TOTAL_SERIALS' | translate }}: {{ serials.length }}</p>
      <p>{{ 'WAREHOUSE.SERIAL_NUMBER_DIALOG.ACTUAL_QUANTITY' | translate }}: {{ getActualQuantity() }}</p>
    </div>

    <div class="serial-list">
      <div class="serial-header">
        <div class="serial-number">{{ 'WAREHOUSE.SERIAL_NUMBER_DIALOG.SERIAL_NUMBER' | translate }}</div>
        <div class="serial-status">{{ 'WAREHOUSE.SERIAL_NUMBER_DIALOG.STATUS_LABEL' | translate }}</div>
      </div>

      <div class="serial-row" *ngFor="let serial of serials; let i = index">
        <mat-form-field class="serial-number">
          <input matInput [value]="serial.serialNumber" disabled>
        </mat-form-field>

        <mat-form-field class="serial-status">
          <mat-select [(ngModel)]="serial.status">
            <mat-option *ngFor="let status of serialStatuses" [value]="status.value">
              {{ status.label | translate }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div>

  <div class="modal-actions">
    <button mat-button (click)="onCancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" (click)="onConfirm()">
      {{ 'COMMON.CONFIRM' | translate }}
    </button>
  </div>
</div>
