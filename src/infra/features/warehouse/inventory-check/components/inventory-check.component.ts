import { ChangeDetectionStrategy, Component, On<PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { <PERSON><PERSON><PERSON><PERSON>Bar } from '@angular/material/snack-bar';
import { Observable, Subject, takeUntil, map, forkJoin, finalize } from 'rxjs';
import { NotificationService } from '@/core/services/notification.service';
import { ConfirmationService } from '@/core/services/confirmation.service';
import { TabWithCountPipe } from '@shared/pipes/tab-with-count.pipe';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';

import { InventoryCheckService } from '../services/inventory-check.service';
import {
  ProductListItem,
  EmbeddedProductSerial,
  InventoryCheckItem,
  Warehouse,
  Employee,
  SaveInventoryCheckResult,
  SerialNumberModalData,
  ProductFilterModalData
} from '@features/warehouse/inventory-check/models/api/inventory-check.dto';
import {
  ProductFilterResult,
  InventoryCheckExtended
} from '@features/warehouse/inventory-check/models/view/inventory-check.view-model';
import { SharedProductSearchComponent } from '@/shared/components/product-selection/product-search/product-search.component';
import { ProductRowComponent } from './product-row/product-row.component';
import { InventoryCheckSummaryComponent } from './summary/summary.component';
import { SimpleNoteModalService } from '@/shared/modals/common/simple-note-modal';
import { BatchModalComponent, BatchData } from '@/shared/modals/warehouse/batch-modal';
import { ProductFilterModalComponent } from '@/features/warehouse/inventory-check/components/product-filter-modal';

/**
 * Component kiểm kho (Inventory Check)
 * Cho phép người dùng kiểm kê hàng hóa, so sánh số lượng thực tế với số lượng trong hệ thống
 */
@Component({
  selector: 'app-inventory-check',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatDialogModule,
    MatBottomSheetModule,
    MatAutocompleteModule,
    SharedProductSearchComponent,
    ProductRowComponent,
    InventoryCheckSummaryComponent,
    TabWithCountPipe
  ],
  templateUrl: './inventory-check.component.html',
  styleUrls: ['./inventory-check.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [InventoryCheckService]
})
export class InventoryCheckComponent implements OnInit, OnDestroy {
  /**
   * Danh sách kho
   */
  warehouses: Warehouse[] = [];

  /**
   * Kho đã chọn
   */
  selectedWarehouse: string = '';

  /**
   * Danh sách sản phẩm kiểm kho
   */
  inventoryItems$: Observable<InventoryCheckItem[]>;

  /**
   * Observable cho InventoryCheck
   */
  inventoryCheck$: Observable<InventoryCheckExtended | null>;

  /**
   * Subject để hủy các subscription khi component bị hủy
   */
  private destroy$ = new Subject<void>();

  /**
   * Trạng thái đã chọn kho hay chưa
   */
  warehouseSelected = false;

  /**
   * Trạng thái đã thêm sản phẩm hay chưa
   */
  hasProducts = false;

  /**
   * Danh sách nhân viên
   */
  employees: Employee[] = [];

  /**
   * Nhân viên đã chọn
   */
  selectedEmployee: string = '';

  /**
   * Ngày kiểm kho
   */
  inventoryDate: Date = new Date();

  /**
   * Ghi chú kiểm kho
   */
  inventoryNote: string = '';

  /**
   * Tab đang được chọn
   */
  selectedTabIndex = 0;

  /**
   * Trạng thái đang tải
   */
  isLoading = false;

  /**
   * Observable cho danh sách sản phẩm đã lọc theo tab
   */
  filteredItems$: Observable<InventoryCheckItem[]>;

  /**
   * Observable cho danh sách sản phẩm khớp (differenceQuantity = 0)
   */
  matchedItems$: Observable<InventoryCheckItem[]>;

  /**
   * Observable cho danh sách sản phẩm lệch (differenceQuantity != 0)
   */
  differentItems$: Observable<InventoryCheckItem[]>;

  /**
   * Observable cho danh sách sản phẩm chưa kiểm (actualQuantity = undefined)
   */
  uncheckedItems$: Observable<InventoryCheckItem[]>;

  constructor(
    public inventoryCheckService: InventoryCheckService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private translate: TranslateService,
    private router: Router,
    private notificationService: NotificationService,
    private confirmationService: ConfirmationService,
    private responsiveModalService: ResponsiveModalService,
    private simpleNoteModalService: SimpleNoteModalService
  ) {
    this.inventoryCheck$ = this.inventoryCheckService.inventoryCheck$;

    // Sử dụng inventoryItems$ từ service
    this.inventoryItems$ = this.inventoryCheckService.inventoryItems$;

    // Khởi tạo các Observable để lọc sản phẩm theo từng tab
    this.filteredItems$ = this.inventoryItems$;

    // Lọc sản phẩm khớp (differenceQuantity = 0 và actualQuantity đã được nhập)
    this.matchedItems$ = this.inventoryItems$.pipe(
      map(items => items.filter(item =>
        item.actualQuantity !== undefined &&
        item.differenceQuantity === 0
      ))
    );

    // Lọc sản phẩm lệch (differenceQuantity != 0)
    this.differentItems$ = this.inventoryItems$.pipe(
      map(items => items.filter(item =>
        item.actualQuantity !== undefined &&
        item.differenceQuantity !== 0
      ))
    );

    // Lọc sản phẩm chưa kiểm (actualQuantity = undefined)
    this.uncheckedItems$ = this.inventoryItems$.pipe(
      map(items => items.filter(item =>
        item.actualQuantity === undefined
      ))
    );
  }

  ngOnInit(): void {
    // Lấy danh sách kho
    this.inventoryCheckService.getWarehouses().subscribe(warehouses => {
      this.warehouses = warehouses;
    });

    // Tạm thời sử dụng danh sách nhân viên trống
    this.employees = [];

    // Subscribe đến danh sách sản phẩm kiểm kho từ service
    this.inventoryCheckService.inventoryItems$.pipe(
      takeUntil(this.destroy$)
    ).subscribe((items: InventoryCheckItem[]) => {
      this.hasProducts = items.length > 0;
    });

    // Subscribe đến InventoryCheck để cập nhật thông tin
    this.inventoryCheck$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(inventoryCheck => {
      if (inventoryCheck) {
        // Cập nhật thông tin kho nếu đã có
        if (inventoryCheck.warehouse && inventoryCheck.warehouse._id) {
          this.selectedWarehouse = inventoryCheck.warehouse._id;
          this.warehouseSelected = true;
        }

        // Cập nhật thông tin nhân viên nếu đã có
        if (inventoryCheck.createdBy && inventoryCheck.createdBy._id) {
          this.selectedEmployee = inventoryCheck.createdBy._id;
        }

        // Cập nhật ngày kiểm kho nếu đã có
        if (inventoryCheck.createdAt) {
          this.inventoryDate = new Date(inventoryCheck.createdAt);
        }

        // Cập nhật ghi chú nếu đã có
        if (inventoryCheck.adjustmentReason && inventoryCheck.adjustmentReason.description) {
          this.inventoryNote = inventoryCheck.adjustmentReason.description;
        }
      }
    });

    // Kiểm tra xem có ID phiếu kiểm kho trong URL không
    this.checkForInventoryCheckId();
  }

  /**
   * Kiểm tra xem có ID phiếu kiểm kho trong URL không
   */
  private checkForInventoryCheckId(): void {
    // Lấy ID từ URL nếu có
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');

    if (id) {
      this.isLoading = true;
      this.inventoryCheckService.loadInventoryCheck(id).subscribe({
        next: (result: InventoryCheckExtended) => {
          this.isLoading = false;
          console.log('Đã tải phiếu kiểm kho:', result);

          // Thông báo cho người dùng
          this.snackBar.open(
            `Đã tải phiếu kiểm kho ${id}`,
            this.translate.instant('COMMON.CLOSE'),
            { duration: 3000 }
          );
        },
        error: (error: Error) => {
          this.isLoading = false;
          console.error('Lỗi khi tải phiếu kiểm kho:', error);

          // Thông báo lỗi cho người dùng
          this.snackBar.open(
            `Lỗi khi tải phiếu kiểm kho: ${error.message}`,
            this.translate.instant('COMMON.CLOSE'),
            { duration: 3000 }
          );
        }
      });
    }
  }

  /**
   * Xử lý khi chọn kho
   */
  onWarehouseChange(warehouseId: string): void {
    this.selectedWarehouse = warehouseId;
    this.warehouseSelected = !!warehouseId;

    // Khởi tạo InventoryCheck mới với kho đã chọn
    if (warehouseId) {
      const selectedWarehouse = this.warehouses.find(w => w._id === warehouseId);
      if (selectedWarehouse) {
        this.inventoryCheckService.initInventoryCheck();
        this.inventoryCheckService.setWarehouse({
          _id: selectedWarehouse._id,
          name: selectedWarehouse.name
        });
      }
    }
  }

  /**
   * Xử lý khi chọn sản phẩm từ SharedProductSearchComponent
   */
  onProductSelected(product: ProductListItem): void {
    if (!this.selectedWarehouse) return;

    // Hiển thị loading indicator
    this.isLoading = true;

    // Thêm sản phẩm vào danh sách kiểm kho
    this.inventoryCheckService.addProductToInventory(product).pipe(
      finalize(() => {
        this.isLoading = false;
      })
    ).subscribe({
      next: (success: boolean) => {
        if (success) {
          console.log('Đã thêm sản phẩm vào danh sách kiểm kho:', product.name);
        } else {
          console.log('Không thể thêm sản phẩm vào danh sách kiểm kho:', product.name);
        }
      },
      error: (error: Error) => {
        console.error('Lỗi khi thêm sản phẩm vào danh sách kiểm kho:', error);
      }
    });
  }

  /**
   * Mở dialog lọc sản phẩm để thêm sản phẩm vào phiếu kiểm kho
   */
  openFilterDialog(): void {
    if (!this.selectedWarehouse) {
      this.snackBar.open(
        this.translate.instant('INVENTORY_CHECK.WAREHOUSE_REQUIRED'),
        this.translate.instant('COMMON.CLOSE'),
        { duration: 3000 }
      );
      return;
    }

    this.isLoading = true;

    // Lấy danh sách danh mục và vị trí kho
    forkJoin({
      categories: this.inventoryCheckService.getProductCategories(),
      locations: this.inventoryCheckService.getWarehouseLocations(this.selectedWarehouse)
    }).pipe(
      finalize(() => {
        this.isLoading = false;
      })
    ).subscribe({
      next: (_) => {
        // Mở modal lọc sản phẩm
        this.responsiveModalService.open<
          ProductFilterModalComponent,
          ProductFilterModalData,
          ProductFilterResult
        >(
          ProductFilterModalComponent,
          {
            data: {
              warehouseId: this.selectedWarehouse,
              current: {
                category: [], // Mặc định không có danh mục nào được chọn
                warehouseLocation: null // Mặc định không có vị trí kho nào được chọn
              }
            }
          }
        ).then((result: ProductFilterResult | undefined) => {
          if (result && result.products && result.products.length > 0) {
            // Hiển thị loading indicator
            this.isLoading = true;

            // Thêm các sản phẩm đã lọc vào danh sách kiểm kho
            const addProductObservables = result.products.map((product: ProductListItem) => {
              return this.inventoryCheckService.addProductToInventory(product);
            });

            // Đợi tất cả các sản phẩm được thêm vào
            forkJoin(addProductObservables).pipe(
              finalize(() => {
                this.isLoading = false;
              })
            ).subscribe({
              next: () => {
                // Hiển thị thông báo thành công
                this.snackBar.open(
                  this.translate.instant('INVENTORY_CHECK.PRODUCTS_ADDED', { count: result.products.length }),
                  this.translate.instant('COMMON.CLOSE'),
                  { duration: 3000 }
                );
              },
              error: (error) => {
                console.error('Error adding filtered products:', error);
              }
            });
          }
        }).catch(error => {
          console.error('Error opening product filter modal:', error);
        });
      },
      error: (error) => {
        console.error('Error loading filter data:', error);
        this.snackBar.open(
          this.translate.instant('COMMON.ERROR_LOADING_DATA'),
          this.translate.instant('COMMON.CLOSE'),
          { duration: 3000 }
        );
      }
    });
  }

  /**
   * Xử lý khi chuyển tab
   */
  onTabChange(event: {index: number}): void {
    this.selectedTabIndex = event.index;
  }

  /**
   * Lấy danh sách sản phẩm theo tab đang chọn
   */
  getItemsByTab(tabIndex: number): Observable<InventoryCheckItem[]> {
    switch (tabIndex) {
      case 0: // Tất cả
        return this.inventoryItems$;
      case 1: // Khớp
        return this.matchedItems$;
      case 2: // Lệch
        return this.differentItems$;
      case 3: // Chưa kiểm
        return this.uncheckedItems$;
      default:
        return this.inventoryItems$;
    }
  }

  /**
   * Mở modal ghi chú cho sản phẩm
   */
  async openNoteDialog(productId: string): Promise<void> {
    const currentInventoryCheck = this.inventoryCheckService.getInventoryCheck();
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    const item = currentInventoryCheck.items[itemIndex];
    // Lấy ghi chú từ item, nếu không có thì sử dụng chuỗi rỗng
    const note = (item as any).note || '';

    try {
      const result = await this.simpleNoteModalService.open('COMMON.NOTE', note);

      if (result !== undefined) {
        // Tạm thời bỏ qua việc cập nhật ghi chú
        console.log('Cập nhật ghi chú cho sản phẩm:', productId, result);

        // Trong phiên bản Clean Architecture, cần cập nhật phương thức này
        // để sử dụng use case thay vì trực tiếp xử lý trong service
      }
    } catch (error) {
      console.error('Lỗi khi mở modal ghi chú đơn giản:', error);
    }
  }

  /**
   * Mở dialog quản lý serial cho sản phẩm
   */
  async openSerialDialog(productId: string): Promise<void> {
    const currentInventoryCheck = this.inventoryCheckService.getInventoryCheck();
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    const item = currentInventoryCheck.items[itemIndex];

    // Tạm thời bỏ qua việc lấy thông tin sản phẩm từ service
    // Sử dụng thông tin sản phẩm từ item hiện tại
    try {
      // Tạm thời bỏ qua việc mở modal serial
      console.log('Mở modal quản lý serial cho sản phẩm:', productId);

      // Trong phiên bản Clean Architecture, cần cập nhật phương thức này
      // để sử dụng use case thay vì trực tiếp xử lý trong service
    } catch (error) {
      console.error('Error opening serial number modal:', error);
    }
  }

  /**
   * Cập nhật variant và đơn vị tính cho sản phẩm
   */
  updateProductVariant(data: { productId: string, variantId: string, unitId?: string }): void {
    this.inventoryCheckService.updateProductVariant(data.productId, data.variantId, data.unitId);
  }

  /**
   * Cập nhật số lượng thực tế của lô
   */
  updateBatchQuantity(data: { productId: string, batchId: string, quantity: number }): void {
    this.inventoryCheckService.updateBatchQuantity(data.productId, data.batchId, data.quantity);
  }

  /**
   * Mở modal thêm lô hàng mới
   * @param productId ID sản phẩm cần thêm lô
   */
  async openBatchDialog(productId: string): Promise<void> {
    const currentInventoryCheck = this.inventoryCheckService.getInventoryCheck();
    if (!currentInventoryCheck) return;

    const itemIndex = currentInventoryCheck.items.findIndex(
      (item: InventoryCheckItem) => item.product.productId === productId
    );

    if (itemIndex === -1) return;

    try {
      const result = await this.responsiveModalService.open<
        BatchModalComponent,
        BatchData,
        BatchData
      >(
        BatchModalComponent,
        {
          data: {
            batchNumber: '',
            manufacturingDate: new Date(),
            expiryDate: null,
            quantity: null
          }
        }
      );

      if (result) {
        // Tạm thời bỏ qua việc thêm lô mới
        console.log('Thêm lô mới:', result);

        // Trong phiên bản Clean Architecture, cần cập nhật phương thức này
        // để sử dụng use case thay vì trực tiếp xử lý trong service
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thêm lô hàng:', error);
    }
  }

  /**
   * Xử lý khi chọn nhân viên
   */
  onEmployeeChange(employeeId: string): void {
    if (!employeeId) return;

    // Tìm thông tin nhân viên
    const selectedEmployee = this.employees.find(e => e._id === employeeId);
    if (!selectedEmployee) return;

    // Tạm thời bỏ qua việc cập nhật thông tin nhân viên
    console.log('Cập nhật thông tin nhân viên:', selectedEmployee);

    this.selectedEmployee = employeeId;
  }

  /**
   * Xử lý khi thay đổi ngày kiểm kho
   */
  onDateChange(date: Date): void {
    if (!date) return;

    // Tạm thời bỏ qua việc cập nhật ngày kiểm kho
    console.log('Cập nhật ngày kiểm kho:', date);

    this.inventoryDate = date;
  }

  /**
   * Xử lý khi thay đổi ghi chú
   */
  onNoteChange(note: string): void {
    // Tạm thời bỏ qua việc cập nhật ghi chú
    console.log('Cập nhật ghi chú:', note);

    this.inventoryNote = note;
  }

  /**
   * Lưu phiếu kiểm kho dưới dạng nháp
   */
  saveDraft(): void {
    if (!this.hasProducts) return;

    // Kiểm tra xem đã chọn kho chưa
    if (!this.selectedWarehouse) {
      this.notificationService.error(this.translate.instant('INVENTORY_CHECK.WAREHOUSE_REQUIRED'));
      return;
    }

    // Kiểm tra xem đã chọn nhân viên chưa
    if (!this.selectedEmployee) {
      this.notificationService.error(this.translate.instant('INVENTORY_CHECK.EMPLOYEE_REQUIRED'));
      return;
    }

    // Validate ghi chú
    if (this.inventoryNote && this.inventoryNote.length > 500) {
      this.notificationService.error(this.translate.instant('INVENTORY_CHECK.VALIDATION.NOTE_MAX_LENGTH'));
      return;
    }

    // Lưu phiếu kiểm kho
    this.isLoading = true;
    this.inventoryCheckService.saveInventoryCheck('draft').pipe(
      finalize(() => {
        this.isLoading = false;
      })
    ).subscribe({
      next: (_: unknown) => {
        this.notificationService.success(this.translate.instant('INVENTORY_CHECK.SAVE_DRAFT_SUCCESS'));
      },
      error: (error: Error) => {
        this.notificationService.error(this.translate.instant('INVENTORY_CHECK.SAVE_DRAFT_ERROR'));
        console.error('Error saving draft:', error);
      }
    });
  }

  /**
   * Hoàn thành phiếu kiểm kho
   */
  completeInventoryCheck(): void {
    if (!this.hasProducts) return;

    // Kiểm tra xem đã chọn kho chưa
    if (!this.selectedWarehouse) {
      this.notificationService.error(this.translate.instant('INVENTORY_CHECK.WAREHOUSE_REQUIRED'));
      return;
    }

    // Kiểm tra xem đã chọn nhân viên chưa
    if (!this.selectedEmployee) {
      this.notificationService.error(this.translate.instant('INVENTORY_CHECK.EMPLOYEE_REQUIRED'));
      return;
    }

    // Validate ghi chú
    if (this.inventoryNote && this.inventoryNote.length > 500) {
      this.notificationService.error(this.translate.instant('INVENTORY_CHECK.VALIDATION.NOTE_MAX_LENGTH'));
      return;
    }

    // Kiểm tra xem đã kiểm đủ sản phẩm chưa
    const inventoryCheck = this.inventoryCheckService.getInventoryCheck();
    const uncheckedItems = inventoryCheck.items.filter((item: InventoryCheckItem) => item.actualQuantity === undefined);

    if (uncheckedItems.length > 0) {
      // Hiển thị dialog xác nhận
      this.confirmationService.confirm({
        title: this.translate.instant('INVENTORY_CHECK.CONFIRM_COMPLETE_TITLE'),
        message: this.translate.instant('INVENTORY_CHECK.CONFIRM_COMPLETE_MESSAGE', { count: uncheckedItems.length }),
        confirmText: this.translate.instant('COMMON.CONTINUE'),
        cancelText: this.translate.instant('COMMON.CANCEL')
      }).subscribe(result => {
        if (result) {
          this.saveCompleteInventoryCheck();
        }
      });
    } else {
      this.saveCompleteInventoryCheck();
    }
  }

  /**
   * Lưu và hoàn thành phiếu kiểm kho
   */
  private saveCompleteInventoryCheck(): void {
    // Kiểm tra xem có sản phẩm nào có chênh lệch không
    const inventoryCheck = this.inventoryCheckService.getInventoryCheck();
    const itemsWithDifference = inventoryCheck.items.filter(
      (item) => item.actualQuantity !== undefined &&
      item.actualQuantity !== null &&
      item.differenceQuantity !== 0
    );

    // Hiển thị thông báo xác nhận nếu có sản phẩm chênh lệch
    if (itemsWithDifference.length > 0) {
      this.confirmationService.confirm({
        title: this.translate.instant('INVENTORY_CHECK.CONFIRM_STOCK_UPDATE_TITLE'),
        message: this.translate.instant('INVENTORY_CHECK.CONFIRM_STOCK_UPDATE_MESSAGE', { count: itemsWithDifference.length }),
        confirmText: this.translate.instant('COMMON.CONFIRM'),
        cancelText: this.translate.instant('COMMON.CANCEL')
      }).subscribe(result => {
        if (result) {
          this.processCompleteInventoryCheck();
        }
      });
    } else {
      this.processCompleteInventoryCheck();
    }
  }

  /**
   * Xử lý hoàn thành phiếu kiểm kho và cập nhật tồn kho
   */
  private processCompleteInventoryCheck(): void {
    this.isLoading = true;

    this.inventoryCheckService.saveInventoryCheck('completed').pipe(
      finalize(() => {
        this.isLoading = false;
      })
    ).subscribe({
      next: (result: SaveInventoryCheckResult) => {
        this.notificationService.success(this.translate.instant('INVENTORY_CHECK.COMPLETE_SUCCESS'));

        // Hiển thị thông báo về việc cập nhật tồn kho
        if (result.stockAdjustment) {
          this.notificationService.info(this.translate.instant('INVENTORY_CHECK.STOCK_ADJUSTMENT_CREATED'));
        }

        // Chuyển hướng đến trang danh sách phiếu kiểm kho
        this.router.navigate(['/warehouse/inventory-check-list']);
      },
      error: (error: Error) => {
        this.notificationService.error(this.translate.instant('INVENTORY_CHECK.COMPLETE_ERROR'));
        console.error('Error completing inventory check:', error);
      }
    });
  }

  /**
   * In phiếu kiểm kho
   */
  printInventoryCheck(): void {
    if (!this.hasProducts) return;

    // Tạm thời bỏ qua việc in phiếu kiểm kho
    console.log('In phiếu kiểm kho');
  }

  /**
   * Hàm trackBy cho ngFor để tối ưu hiệu suất
   * @param index Index của item trong mảng
   * @param item Item trong mảng
   * @returns ID của sản phẩm để theo dõi
   */
  trackByProductId(_: number, item: InventoryCheckItem): string {
    return item.product.productId || '';
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
