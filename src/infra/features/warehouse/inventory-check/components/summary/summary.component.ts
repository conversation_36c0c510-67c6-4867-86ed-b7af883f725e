import { Component, Input, computed, signal, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { InventoryCheckExtended } from '@features/warehouse/inventory-check/models/view/inventory-check.view-model';

/**
 * Component hiển thị thông tin tổng hợp của phiếu kiểm kho
 */
@Component({
  selector: 'app-inventory-check-summary',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatDividerModule,
    MatIconModule,
    TranslateModule
  ],
  templateUrl: './summary.component.html',
  styleUrls: ['./summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class InventoryCheckSummaryComponent {
  /**
   * Signal để theo dõi dữ liệu kiểm kho
   */
  private inventoryCheckSignal = signal<InventoryCheckExtended | null>(null);

  /**
   * Setter cho Input inventoryCheck
   */
  @Input() set inventoryCheck(value: InventoryCheckExtended | null) {
    this.inventoryCheckSignal.set(value);
  }

  /**
   * Computed signal cho số lượng sản phẩm đã kiểm
   */
  checkedProductsCount = computed(() => {
    const inventoryCheck = this.inventoryCheckSignal();
    if (!inventoryCheck || !inventoryCheck.items) return 0;

    if (inventoryCheck.summary?.stats?.checkedItemsCount !== undefined) {
      return inventoryCheck.summary.stats.checkedItemsCount;
    }

    return inventoryCheck.items.filter(
      item => item.actualQuantity !== undefined && item.actualQuantity !== null
    ).length;
  });

  /**
   * Computed signal cho số lượng sản phẩm chưa kiểm
   */
  uncheckedProductsCount = computed(() => {
    const inventoryCheck = this.inventoryCheckSignal();
    if (!inventoryCheck || !inventoryCheck.items) return 0;

    if (inventoryCheck.summary?.stats?.uncheckedItemsCount !== undefined) {
      return inventoryCheck.summary.stats.uncheckedItemsCount;
    }

    return this.totalProductsCount() - this.checkedProductsCount();
  });

  /**
   * Computed signal cho tổng số sản phẩm
   */
  totalProductsCount = computed(() => {
    const inventoryCheck = this.inventoryCheckSignal();
    if (!inventoryCheck || !inventoryCheck.items) return 0;

    if (inventoryCheck.summary?.stats?.totalItemsCount !== undefined) {
      return inventoryCheck.summary.stats.totalItemsCount;
    }

    return inventoryCheck.items.length;
  });

  /**
   * Computed signal cho phần trăm hoàn thành
   */
  completionPercentage = computed(() => {
    const inventoryCheck = this.inventoryCheckSignal();
    if (!inventoryCheck || !inventoryCheck.items) return 0;

    if (inventoryCheck.summary?.stats?.completionPercentage !== undefined) {
      return inventoryCheck.summary.stats.completionPercentage;
    }

    return this.totalProductsCount() > 0
      ? Math.round((this.checkedProductsCount() / this.totalProductsCount()) * 100)
      : 0;
  });

  /**
   * Getter cho inventoryCheck hiện tại
   */
  get currentInventoryCheck(): InventoryCheckExtended | null {
    return this.inventoryCheckSignal();
  }



  /**
   * Định dạng số tiền
   */
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0
    }).format(value);
  }
}
