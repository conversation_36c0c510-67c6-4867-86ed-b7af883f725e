.summary-container {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;

  h5 {
    margin: 0 0 16px 0;
    font-weight: 500;
    color: #333;
  }
}

.completion-info {
  margin-bottom: 8px;
}

.progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.progress {
  flex: 1;
  height: 8px;
  background-color: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 8px;
}

.progress-bar {
  height: 100%;
  background-color: #4caf50;
  transition: width 0.3s ease;
}

.progress-text {
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}

.product-counts {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.count-item {
  font-size: 0.9rem;
  
  .count-label {
    color: #666;
    margin-right: 4px;
  }
  
  .count-value {
    font-weight: 500;
  }
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.summary-label {
  display: flex;
  align-items: center;
  
  mat-icon {
    margin-right: 8px;
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
  
  .increase-icon {
    color: #4caf50;
  }
  
  .decrease-icon {
    color: #f44336;
  }
  
  .total-icon {
    color: #2196f3;
  }
}

.summary-value {
  text-align: right;
  
  .quantity {
    font-weight: 500;
    font-size: 1.1rem;
  }
  
  .value {
    color: #666;
    font-size: 0.9rem;
    
    &.positive {
      color: #4caf50;
    }
    
    &.negative {
      color: #f44336;
    }
  }
}

.summary-row.total {
  font-weight: bold;
  padding-top: 16px;
  
  .quantity {
    font-size: 1.2rem;
  }
  
  .value {
    font-size: 1rem;
    font-weight: 500;
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .summary-header {
    flex-direction: column;
  }
  
  .product-counts {
    flex-direction: column;
    gap: 4px;
  }
}
