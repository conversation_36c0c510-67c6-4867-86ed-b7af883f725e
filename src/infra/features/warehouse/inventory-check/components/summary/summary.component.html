<div class="summary-container">
  <div class="summary-header">
    <h5>{{ 'INVENTORY_CHECK.SUMMARY' | translate }}</h5>
    <div class="completion-info">
      <div class="progress-container">
        <div class="progress">
          <div class="progress-bar" [style.width.%]="completionPercentage()"></div>
        </div>
        <div class="progress-text">{{ completionPercentage() }}%</div>
      </div>
      <div class="product-counts">
        <div class="count-item">
          <span class="count-label">{{ 'INVENTORY_CHECK.CHECKED_PRODUCTS' | translate }}:</span>
          <span class="count-value">{{ checkedProductsCount() }}</span>
        </div>
        <div class="count-item">
          <span class="count-label">{{ 'INVENTORY_CHECK.UNCHECKED_PRODUCTS' | translate }}:</span>
          <span class="count-value">{{ uncheckedProductsCount() }}</span>
        </div>
        <div class="count-item">
          <span class="count-label">{{ 'INVENTORY_CHECK.TOTAL_PRODUCTS' | translate }}:</span>
          <span class="count-value">{{ totalProductsCount() }}</span>
        </div>
      </div>
    </div>
  </div>

  <mat-divider></mat-divider>

  <!-- Tổng lệch tăng -->
  <div class="summary-row">
    <div class="summary-label">
      <mat-icon class="increase-icon">arrow_upward</mat-icon>
      {{ 'INVENTORY_CHECK.TOTAL_INCREASE' | translate }}
    </div>
    <div class="summary-value">
      <div class="quantity">{{ currentInventoryCheck?.summary?.totalIncrease?.quantity || 0 }}</div>
      <div class="value">{{ formatCurrency(currentInventoryCheck?.summary?.totalIncrease?.value || 0) }}</div>
    </div>
  </div>

  <!-- Tổng lệch giảm -->
  <div class="summary-row">
    <div class="summary-label">
      <mat-icon class="decrease-icon">arrow_downward</mat-icon>
      {{ 'INVENTORY_CHECK.TOTAL_DECREASE' | translate }}
    </div>
    <div class="summary-value">
      <div class="quantity">{{ currentInventoryCheck?.summary?.totalDecrease?.quantity || 0 }}</div>
      <div class="value">{{ formatCurrency(currentInventoryCheck?.summary?.totalDecrease?.value || 0) }}</div>
    </div>
  </div>

  <mat-divider></mat-divider>

  <!-- Tổng chênh lệch -->
  <div class="summary-row total">
    <div class="summary-label">
      <mat-icon class="total-icon">calculate</mat-icon>
      {{ 'INVENTORY_CHECK.TOTAL_DIFFERENCE' | translate }}
    </div>
    <div class="summary-value">
      <div class="quantity">{{ currentInventoryCheck?.summary?.totalDifference?.quantity || 0 }}</div>
      <div class="value" [ngClass]="{
        'positive': (currentInventoryCheck?.summary?.totalDifference?.value || 0) > 0,
        'negative': (currentInventoryCheck?.summary?.totalDifference?.value || 0) < 0
      }">{{ formatCurrency(currentInventoryCheck?.summary?.totalDifference?.value || 0) }}</div>
    </div>
  </div>
</div>
