.product-row {
  display: flex;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  align-items: center;

  &:hover {
    background-color: #f9f9f9;
  }
}

.product-actions {
  display: flex;
  min-width: 100px;

  button {
    margin-right: 4px;
    position: relative;

    .serial-count {
      position: absolute;
      top: -5px;
      right: -5px;
      background-color: #f44336;
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      font-size: 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
    }
  }
}

.product-info {
  flex: 1;
  padding: 0 10px;

  .product-name {
    font-weight: 500;
    font-size: 16px;
  }

  .product-sku {
    color: #666;
    font-size: 13px;
  }
}

.product-unit,
.product-stock,
.product-difference,
.product-difference-value {
  width: 100px;
  text-align: center;
  padding: 0 8px;
}

.product-actual {
  width: 120px;
  text-align: center;
  padding: 0 8px;

  .quantity-input {
    width: 100%;

    ::ng-deep .mat-mdc-form-field-infix {
      padding: 8px 0;
      width: auto;
    }
  }
}

.positive {
  color: #4caf50;
}

.negative {
  color: #f44336;
}

.note-row {
  display: flex;
  padding: 0 12px 12px 120px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
  background-color: #fffde7; // Màu nền vàng nhạt cho ghi chú

  .note-label {
    display: flex;
    align-items: center;
    font-weight: 500;
    margin-right: 8px;
    color: #795548;

    mat-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
  }

  .note-content {
    flex: 1;
    padding: 4px 0;
    color: #333;
    white-space: pre-line; // Giữ nguyên các dòng mới trong ghi chú
  }
}

.variant-row {
  display: flex;
  padding: 0 12px 12px 120px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;

  .variant-label {
    font-weight: 500;
    margin-right: 8px;
  }

  .variant-value {
    display: flex;
    align-items: center;

    span {
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }

    button {
      margin-left: 8px;
      width: 24px;
      height: 24px;
      line-height: 24px;

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
        line-height: 16px;
      }
    }
  }
}

.batch-container {
  padding: 0 12px 12px 120px;
  border-bottom: 1px solid #f0f0f0;
}

.batch-row {
  display: flex;
  padding: 8px 0;
  align-items: center;
  border-bottom: 1px dashed #e0e0e0;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }

  .batch-info {
    flex: 1;

    .batch-code {
      font-weight: 500;
      font-size: 14px;
    }

    .batch-expiry {
      color: #666;
      font-size: 12px;
    }
  }

  .batch-stock {
    width: 100px;
    text-align: center;
    position: relative;

    &::before {
      content: attr(data-label);
      display: none;
    }
  }

  .batch-actual {
    width: 120px;
    position: relative;

    &::before {
      content: attr(data-label);
      display: none;
    }

    .quantity-input {
      width: 100%;

      ::ng-deep .mat-mdc-form-field-infix {
        padding: 8px 0;
        width: auto;
      }
    }
  }
}

.add-batch-button {
  margin-top: 8px;
  font-size: 13px;
}

/* Responsive styles */
@media (max-width: 992px) {
  .product-row {
    flex-wrap: wrap;
  }

  .product-info {
    width: calc(100% - 100px);
    margin-bottom: 10px;
  }

  .product-unit,
  .product-stock,
  .product-difference,
  .product-difference-value {
    width: 25%;
  }

  .product-actual {
    width: 25%;
  }
}

@media (max-width: 768px) {
  .product-row {
    flex-direction: column;
    align-items: flex-start;
    padding: 16px 12px;
  }

  .product-actions {
    margin-bottom: 10px;
  }

  .product-info {
    width: 100%;
    padding: 0;
    margin-bottom: 15px;
  }

  .product-unit,
  .product-stock,
  .product-actual,
  .product-difference,
  .product-difference-value {
    width: 100%;
    text-align: left;
    padding: 4px 0;
    display: flex;
    justify-content: space-between;

    &::before {
      content: attr(data-label);
      font-weight: 500;
    }
  }

  .note-row {
    padding: 0 12px 12px 12px;
    flex-direction: column;

    .note-label {
      margin-bottom: 4px;
    }

    .note-content {
      width: 100%;
    }
  }

  .variant-row {
    padding: 0 12px 12px 12px;
    flex-direction: column;

    .variant-label {
      margin-bottom: 4px;
    }

    .variant-value {
      flex-wrap: wrap;

      span {
        margin-bottom: 4px;
        margin-right: 8px;
      }

      button {
        margin-left: 0;
        margin-top: 4px;
      }
    }
  }

  .batch-container {
    padding: 0 12px 12px 12px;
  }

  .batch-row {
    flex-direction: column;
    align-items: flex-start;

    .batch-info {
      width: 100%;
      margin-bottom: 8px;
    }

    .batch-stock,
    .batch-actual {
      width: 100%;
      text-align: left;
      padding: 4px 0;
      display: flex;
      justify-content: space-between;

      &::before {
        content: attr(data-label);
        font-weight: 500;
        display: block;
      }
    }
  }
}
