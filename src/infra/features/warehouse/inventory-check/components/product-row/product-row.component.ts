import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { OrderItemVariantUnitSelectionModalService } from '@/shared/modals/sales/order/order-item-variant-unit-selection-modal';
import { NotificationService } from '@/core/services/notification.service';
import { ConfirmationService } from '@/core/services/confirmation.service';
import { InventoryCheckItem, EmbeddedProductSerial } from '@features/warehouse/inventory-check/models/api/inventory-check.dto';
import { ActualQuantityUpdateEvent, BatchQuantityUpdateEvent } from '@features/warehouse/inventory-check/models/view/inventory-check.view-model';

/**
 * Component hiển thị một dòng sản phẩm trong danh sách kiểm kho
 */
@Component({
  selector: 'app-product-row',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatIconModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatTooltipModule,
    MatBottomSheetModule
  ],
  templateUrl: './product-row.component.html',
  styleUrls: ['./product-row.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductRowComponent implements OnChanges {
  /**
   * Dữ liệu sản phẩm
   */
  @Input() item!: InventoryCheckItem;

  /**
   * Signal để theo dõi thay đổi của item
   */
  itemSignal = signal<InventoryCheckItem | null>(null);

  constructor(
    private notificationService: NotificationService,
    private confirmationService: ConfirmationService,
    private cdr: ChangeDetectorRef,
    private orderItemVariantUnitSelectionModalService: OrderItemVariantUnitSelectionModalService
  ) {}

  /**
   * Xử lý khi item thay đổi
   */
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['item']) {
      this.itemSignal.set(changes['item'].currentValue);
      // Đánh dấu component cần được kiểm tra lại
      this.cdr.markForCheck();
    }
  }

  /**
   * Sự kiện khi cập nhật số lượng thực tế
   */
  @Output() updateActualQuantity = new EventEmitter<ActualQuantityUpdateEvent>();

  /**
   * Sự kiện khi xóa sản phẩm
   */
  @Output() removeProduct = new EventEmitter<string>();

  /**
   * Sự kiện khi thêm ghi chú
   */
  @Output() addNote = new EventEmitter<string>();

  /**
   * Sự kiện khi mở dialog quản lý serial
   */
  @Output() openSerialDialog = new EventEmitter<string>();

  /**
   * Sự kiện khi chọn variant hoặc đơn vị tính
   */
  @Output() selectVariant = new EventEmitter<{ productId: string, variantId: string, unitId?: string }>();

  /**
   * Sự kiện khi cập nhật số lượng thực tế của lô
   */
  @Output() updateBatchQuantity = new EventEmitter<BatchQuantityUpdateEvent>();

  /**
   * Sự kiện khi thêm lô mới
   */
  @Output() addBatch = new EventEmitter<string>();

  /**
   * Xử lý khi thay đổi số lượng thực tế
   */
  onActualQuantityChange(quantity: number): void {
    // Validate số lượng
    if (quantity === null || quantity === undefined) {
      this.notificationService.error('INVENTORY_CHECK.VALIDATION.ACTUAL_QUANTITY_REQUIRED');
      return;
    }

    if (quantity < 0) {
      this.notificationService.error('INVENTORY_CHECK.VALIDATION.ACTUAL_QUANTITY_POSITIVE');
      return;
    }

    if (quantity > 1000000) {
      this.notificationService.error('INVENTORY_CHECK.VALIDATION.ACTUAL_QUANTITY_MAX');
      return;
    }

    this.updateActualQuantity.emit({
      productId: this.item.product.productId || '',
      quantity
    });

    // Đánh dấu component cần được kiểm tra lại
    this.cdr.markForCheck();
  }

  /**
   * Xử lý khi nhấn nút xóa
   */
  onRemove(): void {
    this.confirmationService.confirm({
      title: 'INVENTORY_CHECK.CONFIRM.REMOVE_PRODUCT_TITLE',
      message: 'INVENTORY_CHECK.CONFIRM.REMOVE_PRODUCT_MESSAGE'
    }).subscribe(result => {
      if (result) {
        this.removeProduct.emit(this.item.product.productId || '');
        this.notificationService.success('INVENTORY_CHECK.PRODUCT.REMOVE_SUCCESS');
      }
    });
  }

  /**
   * Xử lý khi nhấn nút thêm ghi chú
   */
  onAddNote(): void {
    this.addNote.emit(this.item.product.productId || '');
  }

  /**
   * Xử lý khi nhấn nút quản lý serial
   */
  onOpenSerialDialog(): void {
    this.openSerialDialog.emit(this.item.product.productId || '');
  }

  /**
   * Kiểm tra xem sản phẩm có quản lý theo serial không
   */
  hasSerials(): boolean {
    return this.item.serialDetails !== undefined;
  }

  /**
   * Lấy số lượng serial có trạng thái in_stock hoặc assigned
   * @returns Số lượng serial có trạng thái in_stock hoặc assigned
   */
  getSerialCount(): number {
    const currentItem = this.itemSignal() || this.item;
    if (!currentItem || !currentItem.serialDetails || !Array.isArray(currentItem.serialDetails)) {
      return 0;
    }

    return currentItem.serialDetails.filter(
      (serial: EmbeddedProductSerial) => serial.status === 'in_stock' || serial.status === 'assigned'
    ).length;
  }

  /**
   * Kiểm tra xem sản phẩm có quản lý theo lô không
   */
  hasBatches(): boolean {
    return this.item.batchDetails !== undefined;
  }

  /**
   * Xử lý khi thay đổi số lượng thực tế của lô
   */
  onBatchQuantityChange(batchId: string | undefined, quantity: number): void {
    // Validate số lượng
    if (quantity === null || quantity === undefined) {
      this.notificationService.error('INVENTORY_CHECK.VALIDATION.BATCH_QUANTITY_REQUIRED');
      return;
    }

    if (quantity < 0) {
      this.notificationService.error('INVENTORY_CHECK.VALIDATION.BATCH_QUANTITY_POSITIVE');
      return;
    }

    if (quantity > 1000000) {
      this.notificationService.error('INVENTORY_CHECK.VALIDATION.BATCH_QUANTITY_MAX');
      return;
    }

    // Nếu batchId không tồn tại, sử dụng _id
    if (!batchId && this.item.batchDetails && this.item.batchDetails.length > 0) {
      const batch = this.item.batchDetails.find(b => b.actualQuantity === quantity);
      if (batch) {
        batchId = batch.batch._id;
      }
    }

    if (!batchId) {
      this.notificationService.error('INVENTORY_CHECK.VALIDATION.BATCH_ID_REQUIRED');
      return;
    }

    this.updateBatchQuantity.emit({
      productId: this.item.product.productId || '',
      batchId,
      quantity
    });

    // Đánh dấu component cần được kiểm tra lại
    this.cdr.markForCheck();
  }

  /**
   * Xử lý khi nhấn nút thêm lô mới
   */
  onAddBatch(): void {
    this.addBatch.emit(this.item.product.productId || '');
  }

  /**
   * Kiểm tra xem sản phẩm có ghi chú không
   */
  hasNote(): boolean {
    const currentItem = this.itemSignal() || this.item;
    return currentItem && currentItem.note !== undefined && currentItem.note !== null && currentItem.note.trim() !== '';
  }

  /**
   * Kiểm tra xem sản phẩm có variant không
   */
  hasVariants(): boolean {
    return (
      (this.item.product.variant && Object.keys(this.item.product.variant).length > 0) ||
      ((this.item.product as any).variants && Array.isArray((this.item.product as any).variants) && (this.item.product as any).variants.length > 0) ||
      !!(this.item.product as any).hasVariants
    );
  }

  /**
   * Mở modal để chọn variant
   */
  openVariantSelector(): void {
    // Lấy danh sách variants từ sản phẩm
    const product = this.item.product;

    // Lấy danh sách variants
    let variants: Array<{
      variantId: string;
      attributes: Array<{name: string; value: string}>;
      price?: number;
      sku?: string;
    }> = [];

    // Kiểm tra xem có danh sách variants không
    if ((product as any).variants && Array.isArray((product as any).variants)) {
      variants = (product as any).variants;
    }

    // Nếu không có variants nhưng có variant object
    if ((!variants || variants.length === 0) && product.variant) {
      // Tạo một variant từ variant object hiện tại
      const currentVariantAttributes: Array<{name: string; value: string}> = [];
      for (const [key, value] of Object.entries(product.variant)) {
        currentVariantAttributes.push({
          name: key,
          value: value.toString()
        });
      }

      if (currentVariantAttributes.length > 0) {
        variants = [{
          variantId: 'current',
          attributes: currentVariantAttributes,
          price: product.price,
          sku: product.sku
        }];
      }
    }

    if (variants.length === 0) {
      console.warn('No variants available for product:', product.productId);
      return;
    }

    // Tạo dữ liệu cho modal
    this.orderItemVariantUnitSelectionModalService.open({
      variants: variants,
      currentValue: {
        variant: (product as any)._selectedVariant || { variantId: '', attributes: [] },
        unit: product.unit || { unitName: '', conversionRate: 1, isBaseUnit: true, price: 0 }
      },
      units: (product as any).units || []
    }).then(result => {
      if (result) {
        this.selectVariant.emit({
          productId: product.productId || '',
          variantId: result.variant ? result.variant.variantId : '',
          unitId: result.unit ? result.unit.unitName : undefined
        });

        // Đánh dấu component cần được kiểm tra lại
        this.cdr.markForCheck();
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn variant:', error);
    });
  }
}
