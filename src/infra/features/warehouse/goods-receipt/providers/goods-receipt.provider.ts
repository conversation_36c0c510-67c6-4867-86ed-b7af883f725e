import { Provider } from '@angular/core';
import { GoodsReceiptRepository } from '@domain/repositories/warehouse/goods-receipt.repository';
import { GoodsReceiptRepositoryImpl } from '../services/goods-receipt-repository.impl';
import { GoodsReceiptUseCase } from '@application/use-cases/warehouse/goods-receipt/goods-receipt.usecase';

/**
 * Provider cho Goods Receipt
 * Cung cấp các dependency injection cho component
 */
export const GOODS_RECEIPT_PROVIDERS: Provider[] = [
  {
    provide: GoodsReceiptRepository,
    useClass: GoodsReceiptRepositoryImpl
  },
  GoodsReceiptUseCase
];
