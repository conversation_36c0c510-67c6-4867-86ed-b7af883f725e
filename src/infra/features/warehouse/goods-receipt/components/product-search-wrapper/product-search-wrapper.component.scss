/* Styles cho component bao bọc tìm kiếm sản phẩm */
:host {
  display: block;
  width: 100%;
}

.product-search-wrapper {
  width: 100%;
}

/* Responsive styles */
@media (max-width: 768px) {
  .product-search-wrapper {
    flex-direction: column;

    .flex-grow-1 {
      width: 100%;
      margin-right: 0 !important;
      margin-bottom: 0.5rem;
    }

    .action-buttons {
      display: flex;
      width: 100%;
      justify-content: space-between;

      button {
        flex: 1;
        margin: 0 0.25rem;

        &:first-child {
          margin-left: 0;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
