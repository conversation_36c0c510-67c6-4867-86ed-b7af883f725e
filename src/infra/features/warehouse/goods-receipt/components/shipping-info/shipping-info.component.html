<mat-expansion-panel>
  <mat-expansion-panel-header>
    <mat-panel-title>
      {{ 'SALES.ORDER_FORM.SHIPPING_INFO.TITLE' | translate }}
    </mat-panel-title>
  </mat-expansion-panel-header>

  <div class="row" [formGroup]="shippingForm">
    <div class="col-md-6">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.RECEIVER_NAME' | translate }}</mat-label>
        <input matInput formControlName="receiverName">
      </mat-form-field>
    </div>
    <div class="col-md-6">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.RECEIVER_PHONE' | translate }}</mat-label>
        <input matInput formControlName="receiverPhone">
      </mat-form-field>
    </div>
    <div class="col-12">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_ADDRESS' | translate }}</mat-label>
        <textarea matInput rows="2" formControlName="deliveryAddress"></textarea>
      </mat-form-field>
    </div>
    <div class="col-md-6">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.WEIGHT' | translate }}</mat-label>
        <input matInput type="number" formControlName="weight">
        <span matSuffix>kg</span>
      </mat-form-field>
    </div>
    <div class="col-md-6">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'SALES.ORDER_FORM.SHIPPING_INFO.DELIVERY_DATE' | translate }}</mat-label>
        <input matInput [matDatepicker]="deliveryDatePicker" formControlName="deliveryDate">
        <mat-datepicker-toggle matSuffix [for]="deliveryDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #deliveryDatePicker></mat-datepicker>
      </mat-form-field>
    </div>
  </div>
</mat-expansion-panel>
