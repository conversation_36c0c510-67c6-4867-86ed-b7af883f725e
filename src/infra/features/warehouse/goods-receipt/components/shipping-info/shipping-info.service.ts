import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TransportInfo } from '../../models/api/goods-receipt.dto';

/**
 * Service xử lý logic thông tin vận chuyển
 */
@Injectable({
  providedIn: 'root'
})
export class ShippingInfoService {
  constructor(private fb: FormBuilder) { }

  /**
   * Tạo form thông tin vận chuyển
   * @returns FormGroup cho thông tin vận chuyển
   */
  createShippingForm(): FormGroup {
    return this.fb.group({
      receiverName: [''],
      receiverPhone: [''],
      deliveryAddress: [''],
      weight: [0],
      deliveryDate: [null]
    });
  }

  /**
   * Lấy dữ liệu form để lưu
   * @param form Form thông tin vận chuyển
   * @returns Dữ liệu thông tin vận chuyển
   */
  getFormData(form: FormGroup): Partial<TransportInfo> {
    return form.value;
  }

  /**
   * C<PERSON>p nhật form với dữ liệu có sẵn
   * @param form Form thông tin vận chuyển
   * @param data Dữ liệu thông tin vận chuyển
   */
  patchFormData(form: FormGroup, data: Partial<TransportInfo>): void {
    if (data) {
      form.patchValue(data);
    }
  }
}
