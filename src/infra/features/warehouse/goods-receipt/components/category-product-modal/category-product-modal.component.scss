:host {
  display: block;
}

.modal-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  max-width: 100%;
}

.modal-content {
  min-height: 250px;
  max-height: 60vh;
  margin-bottom: 20px;
  overflow-y: auto;
}

.modal-title {
  margin-bottom: 0;
  font-size: 1.25rem;
  font-weight: 500;
}

h3 {
  margin-top: 0;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.54);
}

.category-selection {
  mat-list-option {
    height: 48px;
  }
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-bottom: 8px;
  margin-bottom: 0;
}
