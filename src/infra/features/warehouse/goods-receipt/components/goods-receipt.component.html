<div class="container-fluid">
  <div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
      <h3>{{ 'WAREHOUSE.GOODS_RECEIPT.TITLE' | translate }}</h3>
    </div>
    <div class="card-body">
      <div class="row">
        <!-- <PERSON><PERSON><PERSON> bên trái (col-md-8): <PERSON><PERSON> sách sản phẩm -->
        <div class="col-md-8">
          <!-- Tìm kiếm sản phẩm -->
          <app-product-search-wrapper
            (productSelected)="onProductSelected($event)"
            (addFromCategory)="onAddFromCategory()"
            (printList)="onPrintList()">
          </app-product-search-wrapper>

          <!-- Ki<PERSON>m tra chất lượng -->
          <app-quality-check
            [employeeList]="employeeList"
            (dataChange)="onQualityCheckChanged($event)">
          </app-quality-check>

          <!-- Danh s<PERSON>ch sản phẩm -->
          <app-product-list
            [warehouseLocations]="warehouseLocations"
            [warehouseId]="selectedWarehouseId"
            (itemsChanged)="onItemsChanged($event)"
            (subtotalChanged)="onSubtotalChanged($event)">
          </app-product-list>

          <!-- Thông tin vận chuyển -->
          <app-transport-info
            [initialData]="transportInfo"
            (dataChange)="onTransportInfoChanged($event)">
          </app-transport-info>
        </div>

        <!-- Phần bên phải (col-md-4): Thông tin tổng quan và tài chính -->
        <div class="col-md-4">
          <!-- Thông tin cơ bản -->
          <app-basic-info
            [employeeList]="employeeList"
            [warehouseList]="warehouseList"
            [supplierList]="supplierList"
            (dataChange)="onBasicInfoChanged($event)"
            (warehouseChanged)="onWarehouseChanged($event)">
          </app-basic-info>

          <!-- Thông tin tài chính -->
          <app-financial-info
            [subTotal]="subTotal"
            [totalSupplierAdditionalCost]="totalSupplierAdditionalCost"
            [totalOtherAdditionalCost]="totalOtherAdditionalCost"
            [totalTax]="totalTax"
            [additionalCosts]="additionalCosts"
            [taxes]="taxes"
            (dataChange)="onFinancialInfoChanged($event)"
            (additionalCostsChange)="onAdditionalCostsChanged($event)"
            (taxesChange)="onTaxesChanged($event)"
            (saveDraft)="onSaveDraft($event)"
            (complete)="onComplete($event)">
          </app-financial-info>
        </div>
      </div>
    </div>
  </div>
</div>
