import { Injectable } from '@angular/core';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';
import { AdditionalCostModalComponent } from './additional-cost-modal.component';
import { AdditionalCostModalData } from '../../models/view/goods-receipt.view-model';

@Injectable({
  providedIn: 'root'
})
export class AdditionalCostModalService {
  constructor(private responsiveModalService: ResponsiveModalService) {}

  /**
   * Mở modal thêm/sửa chi phí bổ sung
   * @param data Dữ liệu đầu vào cho modal
   * @returns Promise<ImportAdditionalCost | undefined>
   */
  async open(data: AdditionalCostModalData): Promise<ImportAdditionalCost | undefined> {
    return this.responsiveModalService.open<
      AdditionalCostModalComponent,
      AdditionalCostModalData,
      ImportAdditionalCost
    >(AdditionalCostModalComponent, {
      data,
      width: '550px',
      maxWidth: '90vw',
      panelClass: 'additional-cost-modal'
    });
  }
}
