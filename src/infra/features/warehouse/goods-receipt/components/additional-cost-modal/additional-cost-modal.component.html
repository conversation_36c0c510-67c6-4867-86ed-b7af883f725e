<h2 mat-dialog-title>{{ modalTitle | translate }}</h2>

<form [formGroup]="form" (ngSubmit)="onSave()">
  <mat-dialog-content>
    <!-- Tên chi phí -->
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.ADDITIONAL_COST.NAME' | translate }}</mat-label>
      <input matInput formControlName="name" placeholder="{{ 'WAREHOUSE.ADDITIONAL_COST.NAME_PLACEHOLDER' | translate }}" autocomplete="off">
      <mat-error *ngIf="form.get('name')?.errors?.['required']">
        {{ 'VALIDATION.REQUIRED' | translate }}
      </mat-error>
      <mat-error *ngIf="form.get('name')?.errors?.['maxlength']">
        {{ 'VALIDATION.MAX_LENGTH' | translate: { max: 100 } }}
      </mat-error>
    </mat-form-field>

    <!-- Loại chi phí -->
    <div class="mb-4" formGroupName="costValue">
      <label class="form-label">{{ 'WAREHOUSE.ADDITIONAL_COST.COST_TYPE' | translate }}</label>
      <mat-radio-group formControlName="type" class="d-flex gap-4">
        <mat-radio-button value="fixed">{{ 'WAREHOUSE.ADDITIONAL_COST.FIXED' | translate }}</mat-radio-button>
        <mat-radio-button value="percentage">{{ 'WAREHOUSE.ADDITIONAL_COST.PERCENTAGE' | translate }}</mat-radio-button>
      </mat-radio-group>
    </div>

    <!-- Giá trị chi phí -->
    <div formGroupName="costValue" class="mb-4">
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'WAREHOUSE.ADDITIONAL_COST.VALUE' | translate }}</mat-label>
        <input matInput type="number" formControlName="value" min="0" step="1">
        <span matSuffix>{{ getCostValueUnit() }}</span>
        <mat-error *ngIf="form.get('costValue.value')?.errors?.['required']">
          {{ 'VALIDATION.REQUIRED' | translate }}
        </mat-error>
        <mat-error *ngIf="form.get('costValue.value')?.errors?.['min']">
          {{ 'VALIDATION.MIN_VALUE' | translate: { min: 0 } }}
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Trả cho nhà cung cấp -->
    <div class="mb-4">
      <mat-checkbox formControlName="paidToSupplier">
        {{ 'WAREHOUSE.ADDITIONAL_COST.PAID_TO_SUPPLIER' | translate }}
      </mat-checkbox>
    </div>

    <!-- Phân bổ vào giá vốn sản phẩm -->
    <div class="mb-4">
      <mat-checkbox formControlName="allocateToItems">
        {{ 'WAREHOUSE.ADDITIONAL_COST.ALLOCATE_TO_ITEMS' | translate }}
      </mat-checkbox>
    </div>

    <!-- Thông tin thuế -->
    <div formGroupName="tax" class="mb-4">
      <h3 class="text-gray-700 mb-2">{{ 'WAREHOUSE.ADDITIONAL_COST.TAX_INFO' | translate }}</h3>

      <!-- Tỷ lệ thuế -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'WAREHOUSE.ADDITIONAL_COST.TAX_RATE' | translate }}</mat-label>
        <input matInput type="number" formControlName="rate" min="0" max="100" step="0.1"
               matTooltip="{{ 'WAREHOUSE.ADDITIONAL_COST.TAX_RATE_TOOLTIP' | translate }}">
        <span matSuffix>%</span>
        <mat-error *ngIf="form.get('tax.rate')?.errors?.['min']">
          {{ 'VALIDATION.MIN_VALUE' | translate: { min: 0 } }}
        </mat-error>
        <mat-error *ngIf="form.get('tax.rate')?.errors?.['max']">
          {{ 'VALIDATION.MAX_VALUE' | translate: { max: 100 } }}
        </mat-error>
      </mat-form-field>

      <!-- Số tiền thuế -->
      <mat-form-field appearance="outline" class="w-100">
        <mat-label>{{ 'WAREHOUSE.ADDITIONAL_COST.TAX_AMOUNT' | translate }}</mat-label>
        <input matInput type="number" formControlName="amount" min="0" step="1">
        <span matSuffix>VND</span>
        <mat-error *ngIf="form.get('tax.amount')?.errors?.['min']">
          {{ 'VALIDATION.MIN_VALUE' | translate: { min: 0 } }}
        </mat-error>
      </mat-form-field>
    </div>

    <!-- Thông tin bổ sung -->
    <div *ngIf="data.subTotal" class="text-gray-600 text-sm mb-4">
      <p>{{ 'WAREHOUSE.ADDITIONAL_COST.SUB_TOTAL_INFO' | translate: { value: data.subTotal } }}</p>

      <ng-container *ngIf="!manualTaxAmount && form.get('tax.rate')?.value && form.get('costValue.value')?.value">
        <p>{{ 'WAREHOUSE.ADDITIONAL_COST.AUTO_TAX_INFO' | translate: { value: calculateTaxAmount() } }}</p>
      </ng-container>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid">
      {{ 'COMMON.SAVE' | translate }}
    </button>
  </mat-dialog-actions>
</form>
