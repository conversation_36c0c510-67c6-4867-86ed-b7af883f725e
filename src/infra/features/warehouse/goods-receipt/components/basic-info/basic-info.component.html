<!-- Thông tin nhân viên, ng<PERSON>y nhận hàng -->
<mat-card class="mb-3 basic-info-card">
  <mat-card-content [formGroup]="basicInfoForm">
    <h5 class="mb-3">{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.EMPLOYEE' | translate }}</h5>
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.SELECT_EMPLOYEE' | translate }}</mat-label>
      <mat-select formControlName="employeeId">
        <mat-option *ngFor="let employee of employeeList" [value]="employee._id">{{ employee.name }}</mat-option>
      </mat-select>
      <mat-error *ngIf="basicInfoForm.get('employeeId')?.hasError('required')">
        {{ 'VALIDATION.REQUIRED' | translate }}
      </mat-error>
    </mat-form-field>

    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.RECEIPT_DATE' | translate }}</mat-label>
      <input matInput [matDatepicker]="picker" formControlName="receiptDate">
      <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
      <mat-datepicker #picker></mat-datepicker>
      <mat-error *ngIf="basicInfoForm.get('receiptDate')?.hasError('required')">
        {{ 'VALIDATION.REQUIRED' | translate }}
      </mat-error>
    </mat-form-field>
  </mat-card-content>
</mat-card>

<!-- Thông tin kho hàng và nhà cung cấp -->
<mat-card class="mb-3 basic-info-card">
  <mat-card-content [formGroup]="basicInfoForm">
    <h5 class="mb-3">{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.WAREHOUSE' | translate }}</h5>
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.SELECT_WAREHOUSE' | translate }}</mat-label>
      <mat-select formControlName="warehouseId">
        <mat-option *ngFor="let warehouse of warehouseList" [value]="warehouse._id">{{ warehouse.name }}</mat-option>
      </mat-select>
      <mat-error *ngIf="basicInfoForm.get('warehouseId')?.hasError('required')">
        {{ 'VALIDATION.REQUIRED' | translate }}
      </mat-error>
    </mat-form-field>

    <h5 class="mb-3 mt-4">{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.SUPPLIER' | translate }}</h5>
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.SEARCH_SUPPLIER' | translate }}</mat-label>
      <input matInput
             [matAutocomplete]="supplierAuto"
             formControlName="supplierId">
      <mat-autocomplete #supplierAuto="matAutocomplete"
                        [displayWith]="displaySupplierFn"
                        (optionSelected)="onSupplierSelected($event)">
        <mat-option *ngFor="let supplier of filteredSuppliers | async" [value]="supplier">
          <div>{{ supplier.name }}</div>
          <small *ngIf="supplier.phoneNumber" class="text-muted">{{ supplier.phoneNumber }}</small>
        </mat-option>
      </mat-autocomplete>
      <mat-error *ngIf="basicInfoForm.get('supplierId')?.hasError('required')">
        {{ 'VALIDATION.REQUIRED' | translate }}
      </mat-error>
    </mat-form-field>

    <h5 class="mb-3 mt-4">{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.NOTES' | translate }}</h5>
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.NOTES' | translate }}</mat-label>
      <textarea matInput
                formControlName="notes"
                placeholder="{{ 'WAREHOUSE.GOODS_RECEIPT.RECEIPT_INFO.NOTES_PLACEHOLDER' | translate }}"
                maxlength="500"></textarea>
      <mat-hint align="end">{{ basicInfoForm.get('notes')?.value?.length || 0 }}/500</mat-hint>
    </mat-form-field>
  </mat-card-content>
</mat-card>
