import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { SupplierCostsComponent } from '../supplier-costs/supplier-costs.component';
import { OtherCostsComponent } from '../other-costs/other-costs.component';
import { TaxesComponent } from '../taxes/taxes.component';
import { PaymentComponent } from '../payment/payment.component';
import { ActionButtonsComponent } from '../action-buttons/action-buttons.component';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

import { FinancialInfoService } from './financial-info.service';
import { TaxInfo, ImportAdditionalCost, GoodsReceipt } from '../../models/api/goods-receipt.dto'
import { TaxFormModalService } from '@/shared/modals/common/tax-form-modal';
import { PaymentData } from '../payment/payment.component';

/**
 * Component thông tin tài chính
 */
@Component({
  selector: 'app-financial-info',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatButtonModule,
    MatIconModule,
    MatRadioModule,
    MatDialogModule,
    MatTooltipModule,
    TranslateModule,
    SupplierCostsComponent,
    OtherCostsComponent,
    TaxesComponent,
    PaymentComponent,
    ActionButtonsComponent
  ],
  templateUrl: './financial-info.component.html',
  styleUrls: ['./financial-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FinancialInfoComponent implements OnInit, OnChanges, OnDestroy {
  /**
   * Tổng tiền hàng
   */
  @Input() subTotal: number = 0;

  /**
   * Tổng chiết khấu
   */
  @Input() totalDiscount: number = 0;

  /**
   * Tổng chi phí trả cho nhà cung cấp
   */
  @Input() totalSupplierAdditionalCost: number = 0;

  /**
   * Tổng chi phí nhập khác
   */
  @Input() totalOtherAdditionalCost: number = 0;

  /**
   * Tổng thuế
   */
  @Input() totalTax: number = 0;

  /**
   * Danh sách chi phí bổ sung
   */
  @Input() additionalCosts: ImportAdditionalCost[] = [];

  /**
   * Danh sách thuế
   */
  @Input() taxes: TaxInfo[] = [];

  /**
   * Dữ liệu thông tin tài chính ban đầu
   */
  @Input() initialData?: Partial<GoodsReceipt>;

  /**
   * Sự kiện khi dữ liệu thay đổi
   */
  @Output() dataChange = new EventEmitter<Partial<GoodsReceipt>>();

  /**
   * Sự kiện khi chi phí bổ sung thay đổi
   */
  @Output() additionalCostsChange = new EventEmitter<ImportAdditionalCost[]>();

  /**
   * Sự kiện khi thuế thay đổi
   */
  @Output() taxesChange = new EventEmitter<TaxInfo[]>();

  /**
   * Sự kiện khi nhấn nút lưu nháp
   */
  @Output() saveDraft = new EventEmitter<GoodsReceipt>();

  /**
   * Sự kiện khi nhấn nút hoàn thành
   */
  @Output() complete = new EventEmitter<GoodsReceipt>();

  /**
   * Form thông tin tài chính
   */
  financialForm!: FormGroup;

  /**
   * Số tiền cần trả nhà cung cấp
   */
  supplierPayment: number = 0;

  /**
   * Công nợ
   */
  debt: number = 0;

  /**
   * Tổng chi phí bổ sung
   */
  totalAdditionalCost: number = 0;

  /**
   * Danh sách tài khoản ngân hàng
   */
  bankAccounts: any[] = [
    { id: 'bank1', name: 'Vietcombank - **********' },
    { id: 'bank2', name: 'Techcombank - **********' },
    { id: 'bank3', name: 'BIDV - **********' }
  ];

  /**
   * Subject để hủy các subscription khi component bị hủy
   */
  private destroy$ = new Subject<void>();

  constructor(
    private financialInfoService: FinancialInfoService,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private taxFormModalService: TaxFormModalService
  ) { }

  ngOnInit(): void {
    // Khởi tạo form
    this.initForm();

    // Theo dõi thay đổi form
    this.watchFormChanges();

    // Cập nhật form với dữ liệu ban đầu nếu có
    if (this.initialData) {
      this.financialInfoService.patchFormData(this.financialForm, this.initialData);
    }

    // Tính toán tổng tiền và công nợ
    this.calculateTotals();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Tính lại tổng tiền khi các giá trị đầu vào thay đổi
    if (changes['subTotal'] ||
        changes['totalSupplierAdditionalCost'] ||
        changes['totalOtherAdditionalCost'] ||
        changes['totalTax'] ||
        changes['additionalCosts'] ||
        changes['taxes']) {

      // Cập nhật tổng chi phí bổ sung
      this.totalAdditionalCost = this.totalSupplierAdditionalCost + this.totalOtherAdditionalCost;

      // Tính toán lại các giá trị
      this.calculateTotals();
    }
  }

  /**
   * Khởi tạo form
   */
  private initForm(): void {
    this.financialForm = this.financialInfoService.createFinancialForm();
  }

  /**
   * Theo dõi thay đổi form
   */
  private watchFormChanges(): void {
    this.financialForm.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(value => {
      // Lấy giá trị giảm giá tổng
      const totalDiscount = value.totalDiscount || 0;

      // Tính lại số tiền cần trả nhà cung cấp
      this.supplierPayment = this.financialInfoService.calculateSupplierPayment(
        this.subTotal,
        totalDiscount,
        this.totalSupplierAdditionalCost,
        this.totalTax
      );

      // Tính lại công nợ khi số tiền thanh toán thay đổi
      this.debt = this.financialInfoService.calculateDebt(
        this.supplierPayment,
        value.amountPaid
      );

      // Emit sự kiện thay đổi dữ liệu
      this.dataChange.emit(this.financialInfoService.getFormData(
        this.financialForm,
        this.debt,
        this.taxes,
        this.additionalCosts
      ));

      // Cập nhật UI
      this.cdr.markForCheck();
    });
  }

  /**
   * Tính toán tổng tiền và công nợ
   */
  private calculateTotals(): void {
    // Lấy giá trị giảm giá tổng từ form
    const totalDiscount = this.financialForm ? (this.financialForm.get('totalDiscount')?.value || 0) : 0;

    // Tính số tiền cần trả nhà cung cấp
    this.supplierPayment = this.financialInfoService.calculateSupplierPayment(
      this.subTotal,
      totalDiscount,
      this.totalSupplierAdditionalCost,
      this.totalTax
    );

    // Tính công nợ
    if (this.financialForm) {
      this.debt = this.financialInfoService.calculateDebt(
        this.supplierPayment,
        this.financialForm.value.amountPaid
      );
    }

    // Cập nhật UI
    this.cdr.markForCheck();
  }

  /**
   * Xử lý khi nhấn nút lưu nháp
   * @param data Dữ liệu đã chuẩn bị để lưu
   */
  onSaveDraft(data: GoodsReceipt): void {
    this.saveDraft.emit(data);
  }

  /**
   * Xử lý khi nhấn nút hoàn thành
   * @param data Dữ liệu đã chuẩn bị để hoàn thành
   */
  onComplete(data: GoodsReceipt): void {
    this.complete.emit(data);
  }

  /**
   * Lấy dữ liệu form
   */
  getFormData(): GoodsReceipt {
    return this.financialInfoService.getFormData(
      this.financialForm,
      this.debt,
      this.taxes,
      this.additionalCosts
    );
  }

  /**
   * Xử lý khi danh sách chi phí thay đổi từ các component con
   * @param costs Danh sách chi phí mới
   */
  onAdditionalCostsChange(costs: ImportAdditionalCost[]): void {
    // Cập nhật danh sách chi phí
    this.additionalCosts = costs;

    // Tính lại tổng chi phí trả cho nhà cung cấp
    this.totalSupplierAdditionalCost = this.financialInfoService.calculateSupplierAdditionalCosts(this.additionalCosts);

    // Tính lại tổng chi phí nhập khác
    this.totalOtherAdditionalCost = this.financialInfoService.calculateOtherAdditionalCosts(this.additionalCosts);

    // Tính lại tổng chi phí bổ sung
    this.totalAdditionalCost = this.totalSupplierAdditionalCost + this.totalOtherAdditionalCost;

    // Tính toán lại các giá trị
    this.calculateTotals();

    // Emit sự kiện thay đổi chi phí
    this.additionalCostsChange.emit(this.additionalCosts);

    // Emit sự kiện thay đổi dữ liệu
    this.dataChange.emit(this.getFormData());

    // Cập nhật UI
    this.cdr.markForCheck();
  }

  /**
   * Xử lý khi danh sách thuế thay đổi từ component con
   * @param taxes Danh sách thuế mới
   */
  onTaxesChange(taxes: TaxInfo[]): void {
    // Cập nhật danh sách thuế
    this.taxes = taxes;

    // Tính lại tổng thuế
    this.totalTax = this.financialInfoService.calculateTotalTax(this.taxes);

    // Tính toán lại các giá trị
    this.calculateTotals();

    // Emit sự kiện thay đổi thuế
    this.taxesChange.emit(this.taxes);

    // Emit sự kiện thay đổi dữ liệu
    this.dataChange.emit(this.getFormData());

    // Cập nhật UI
    this.cdr.markForCheck();
  }

  /**
   * Xử lý khi dữ liệu thanh toán thay đổi từ component con
   * @param paymentData Dữ liệu thanh toán mới
   */
  onPaymentChange(paymentData: PaymentData): void {
    // Cập nhật công nợ
    this.debt = paymentData.debt.debtAmount;

    // Emit sự kiện thay đổi dữ liệu
    this.dataChange.emit(this.getFormData());

    // Cập nhật UI
    this.cdr.markForCheck();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
