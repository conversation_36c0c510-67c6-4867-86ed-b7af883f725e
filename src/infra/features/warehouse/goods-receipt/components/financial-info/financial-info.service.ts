import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TaxInfo, ImportAdditionalCost, GoodsReceipt, InitialPayment } from '../../models/api/goods-receipt.dto'

/**
 * Service xử lý logic thông tin tài chính
 */
@Injectable({
  providedIn: 'root'
})
export class FinancialInfoService {
  constructor(private fb: FormBuilder) { }

  /**
   * Tạo form thông tin tài chính
   * @returns FormGroup cho thông tin tài chính
   */
  createFinancialForm(): FormGroup {
    return this.fb.group({
      receiptNumber: [{ value: '', disabled: true }],
      totalDiscount: [0, [Validators.required, Validators.min(0)]],
      paymentMethod: ['cash', Validators.required],
      amountPaid: [0, [Validators.required, Validators.min(0)]],
      dueDate: [null],
      bankAccountId: [''],
    });
  }

  /**
   * Tính toán công nợ
   * @param totalAmount Tổng tiền
   * @param amountPaid Số tiền đã thanh toán
   * @returns Số tiền công nợ
   */
  calculateDebt(totalAmount: number, amountPaid: number): number {
    return Math.max(0, totalAmount - amountPaid);
  }

  /**
   * Tính toán số tiền cần trả nhà cung cấp
   * @param subTotal Tổng tiền hàng
   * @param totalDiscount Giảm giá tổng
   * @param supplierAdditionalCosts Chi phí trả cho nhà cung cấp
   * @param totalTax Tổng thuế
   * @returns Số tiền cần trả nhà cung cấp
   */
  calculateSupplierPayment(subTotal: number, totalDiscount: number, supplierAdditionalCosts: number, totalTax: number): number {
    return Math.max(0, subTotal - totalDiscount + supplierAdditionalCosts + totalTax);
  }

  /**
   * Tính tổng chi phí trả cho nhà cung cấp
   * @param additionalCosts Danh sách chi phí bổ sung
   * @returns Tổng chi phí trả cho nhà cung cấp
   */
  calculateSupplierAdditionalCosts(additionalCosts: ImportAdditionalCost[] = []): number {
    return additionalCosts
      .filter(cost => cost.paidToSupplier)
      .reduce((total, cost) => total + (cost.costValue?.value || 0), 0);
  }

  /**
   * Tính tổng chi phí nhập khác
   * @param additionalCosts Danh sách chi phí bổ sung
   * @returns Tổng chi phí nhập khác
   */
  calculateOtherAdditionalCosts(additionalCosts: ImportAdditionalCost[] = []): number {
    return additionalCosts
      .filter(cost => !cost.paidToSupplier)
      .reduce((total, cost) => total + (cost.costValue?.value || 0), 0);
  }

  /**
   * Tính tổng thuế
   * @param taxes Danh sách thuế
   * @returns Tổng thuế
   */
  calculateTotalTax(taxes: TaxInfo[] = []): number {
    return taxes.reduce((total, tax) => total + tax.amount, 0);
  }

  /**
   * Điền đầy đủ số tiền thanh toán (100%)
   * @param supplierPayment Số tiền cần trả nhà cung cấp
   * @returns Số tiền thanh toán đầy đủ
   */
  fillFullPayment(supplierPayment: number): number {
    return supplierPayment;
  }

  /**
   * Lấy dữ liệu form để lưu
   * @param form Form thông tin tài chính
   * @param totalDebt Số tiền công nợ
   * @param taxes Danh sách thuế
   * @param additionalCosts Danh sách chi phí bổ sung
   * @returns Dữ liệu thông tin tài chính
   */
  getFormData(form: FormGroup, totalDebt: number, taxes: TaxInfo[] = [], additionalCosts: ImportAdditionalCost[] = []): GoodsReceipt {
    const formValue = form.getRawValue(); // Lấy cả giá trị của các trường disabled

    // Tạo các thanh toán ban đầu
    const initialPayments: InitialPayment[] = [];

    if (formValue.amountPaid > 0) {
      const payment: InitialPayment = {
        amount: formValue.amountPaid,
        method: formValue.paymentMethod === 'cash' ? 'cash' : 'bank_transfer',
        paidAt: new Date()
      };

      if (payment.method === 'bank_transfer' && formValue.bankAccountId) {
        payment.bankAccountId = formValue.bankAccountId;
      }

      initialPayments.push(payment);
    }

    // Tạo đối tượng GoodsReceipt mới
    const result = {
      _id: '',
      companyId: '',
      storeId: '',
      branchId: '',
      supplier: {
        _id: '',
        name: ''
      },
      items: [],
      summary: {
        subTotal: 0,
        totalDiscount: formValue.totalDiscount || 0,
        totalSupplierAdditionalCost: 0,
        totalNonSupplierAdditionalCost: 0,
        totalAdditionalCost: 0,
        totalTax: 0,
        total: 0,
        totalQuantity: 0,
        totalItems: 0
      },
      status: 'draft' as 'draft' | 'received' | 'checked' | 'completed' | 'cancelled',
      createdBy: {
        _id: '',
        name: ''
      },
      receivedAt: new Date(),
      taxes: taxes.length > 0 ? taxes : [],
      additionalCosts: additionalCosts.length > 0 ? additionalCosts : [],
      payment: {
        initialPayments: initialPayments,
        debt: {
          debtAmount: totalDebt,
          dueDate: formValue.dueDate
        }
      }
    } as GoodsReceipt;

    return result;
  }

  /**
   * Cập nhật form với dữ liệu có sẵn
   * @param form Form thông tin tài chính
   * @param data Dữ liệu thông tin tài chính
   */
  patchFormData(form: FormGroup, data: Partial<GoodsReceipt>): void {
    if (data) {
      // Cập nhật receiptNumber nếu có
      if (data._id) {
        form.get('receiptNumber')?.patchValue(data._id);
      }

      // Cập nhật totalDiscount nếu có
      if (data.summary?.totalDiscount !== undefined) {
        form.get('totalDiscount')?.patchValue(data.summary.totalDiscount);
      }

      // Cập nhật thông tin thanh toán nếu có
      if (data.payment && data.payment.initialPayments && data.payment.initialPayments.length > 0) {
        const payment = data.payment.initialPayments[0]; // Lấy thanh toán đầu tiên

        form.patchValue({
          paymentMethod: payment.method === 'cash' ? 'cash' : 'bankTransfer',
          amountPaid: payment.amount || 0,
          dueDate: data.payment.debt?.dueDate,
          bankAccountId: payment.bankAccountId
        });
      }
    }
  }
}
