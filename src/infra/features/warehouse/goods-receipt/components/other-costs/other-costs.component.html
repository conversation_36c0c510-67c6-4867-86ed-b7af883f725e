<div class="other-costs-container">
  <!-- Row chính -->
  <div class="d-flex justify-content-between align-items-center mb-2 clickable-row" (click)="toggleExpanded()">
    <div class="d-flex align-items-center">
      <span>{{ 'WAREHOUSE.GOODS_RECEIPT.OTHER_COSTS.TITLE' | translate }}</span>
      <span *ngIf="otherCosts.length > 0" class="ms-2 badge bg-info">{{ otherCosts.length }}</span>
    </div>
    <div class="d-flex align-items-center">
      <strong>{{ totalOtherCost | number }} VND</strong>
      <button mat-icon-button color="primary" class="ms-1" (click)="openSelectCostsDialog(); $event.stopPropagation()">
        <mat-icon>edit</mat-icon>
      </button>
      <mat-icon class="toggle-icon">{{ isExpanded ? 'expand_less' : 'expand_more' }}</mat-icon>
    </div>
  </div>

  <!-- Row phụ - hiển thị chi tiết -->
  <div class="other-costs-details" [class.expanded]="isExpanded">
    <div *ngIf="otherCosts.length === 0" class="text-muted py-2">
      {{ 'WAREHOUSE.GOODS_RECEIPT.OTHER_COSTS.NO_COSTS' | translate }}
    </div>

    <div *ngFor="let cost of otherCosts" class="cost-item">
      <div class="d-flex justify-content-between align-items-center py-1">
        <div>
          <div class="cost-name">{{ cost.name }}</div>
        </div>
        <div class="cost-actions">
          <div class="cost-value">{{ formatCostValue(cost) }}</div>
        </div>
      </div>

      <!-- Hiển thị thông tin thuế nếu có -->
      <div *ngIf="cost.tax && cost.tax.rate > 0" class="tax-info text-muted">
        <small>{{ 'WAREHOUSE.GOODS_RECEIPT.OTHER_COSTS.TAX_INFO' | translate: { rate: cost.tax.rate, amount: (cost.tax.amount | number) } }}</small>
      </div>
    </div>
  </div>
</div>
