import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { QualityCheck, QualityCheckRejectedItem, GoodsReceiptItem } from '../../models/api/goods-receipt.dto';

/**
 * Service xử lý logic kiểm tra chất lượng
 */
@Injectable({
  providedIn: 'root'
})
export class QualityCheckService {
  constructor(private fb: FormBuilder) { }

  /**
   * Tạo form kiểm tra chất lượng
   * @returns FormGroup cho kiểm tra chất lượng
   */
  createQualityCheckForm(): FormGroup {
    return this.fb.group({
      status: ['pending', Validators.required],
      checkedBy: ['', Validators.required],
      checkedAt: [new Date(), Validators.required],
      notes: ['']
    });
  }

  /**
   * Lấy dữ liệu form để lưu
   * @param form Form kiểm tra chất lượng
   * @param rejectedItems Danh sách sản phẩm bị từ chối
   * @returns Dữ liệu kiểm tra chất lượng
   */
  getFormData(form: FormGroup, rejectedItems: QualityCheckRejectedItem[] = []): Partial<QualityCheck> {
    const formValue = form.value;

    // Nếu trạng thái là pending và không có dữ liệu khác, trả về undefined
    if (formValue.status === 'pending' &&
        !formValue.checkedBy &&
        !formValue.notes &&
        rejectedItems.length === 0) {
      return {};
    }

    // Ánh xạ checkedBy từ ID sang object {_id, name}
    let checkedBy = undefined;
    if (formValue.checkedBy) {
      checkedBy = {
        _id: formValue.checkedBy,
        name: '' // Tên sẽ được điền bởi component
      };
    }

    return {
      status: formValue.status,
      checkedBy: checkedBy,
      checkedAt: formValue.checkedAt,
      notes: formValue.notes,
      rejectedItems: rejectedItems.length > 0 ? rejectedItems : undefined
    };
  }

  /**
   * Cập nhật form với dữ liệu có sẵn
   * @param form Form kiểm tra chất lượng
   * @param data Dữ liệu kiểm tra chất lượng
   */
  patchFormData(form: FormGroup, data: Partial<QualityCheck>): void {
    if (data) {
      // Ánh xạ checkedBy từ object {_id, name} sang ID
      const formData: any = { ...data };
      if (data.checkedBy && data.checkedBy._id) {
        formData.checkedBy = data.checkedBy._id;
      }

      form.patchValue(formData);
    }
  }

  /**
   * Tạo một sản phẩm bị từ chối mới
   * @param item Sản phẩm gốc
   * @param quantity Số lượng bị từ chối
   * @param reason Lý do từ chối
   * @returns Đối tượng QualityCheckRejectedItem
   */
  createRejectedItem(item: GoodsReceiptItem, quantity: number, reason: string): QualityCheckRejectedItem {
    return {
      _id: item._id,
      name: item.product.name,
      quantity: quantity,
      reason: reason
    };
  }

  /**
   * Cập nhật sản phẩm bị từ chối
   * @param rejectedItems Danh sách sản phẩm bị từ chối hiện tại
   * @param updatedItem Sản phẩm bị từ chối đã cập nhật
   * @returns Danh sách sản phẩm bị từ chối mới
   */
  updateRejectedItem(rejectedItems: QualityCheckRejectedItem[], updatedItem: QualityCheckRejectedItem): QualityCheckRejectedItem[] {
    const index = rejectedItems.findIndex(item => item._id === updatedItem._id);

    if (index >= 0) {
      // Cập nhật sản phẩm đã tồn tại
      const newItems = [...rejectedItems];
      newItems[index] = updatedItem;
      return newItems;
    } else {
      // Thêm sản phẩm mới
      return [...rejectedItems, updatedItem];
    }
  }

  /**
   * Xóa sản phẩm bị từ chối
   * @param rejectedItems Danh sách sản phẩm bị từ chối hiện tại
   * @param itemId ID của sản phẩm cần xóa
   * @returns Danh sách sản phẩm bị từ chối mới
   */
  removeRejectedItem(rejectedItems: QualityCheckRejectedItem[], itemId: string): QualityCheckRejectedItem[] {
    return rejectedItems.filter(item => item._id !== itemId);
  }
}
