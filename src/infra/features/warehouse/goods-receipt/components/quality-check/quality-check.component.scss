/* Styles cho component kiểm tra chất lượng */
:host {
  display: block;
  width: 100%;
}

/* <PERSON>ge trạng thái */
.status-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;

  &.status-pending {
    background-color: #e0e0e0;
    color: #616161;
  }

  &.status-passed {
    background-color: #c8e6c9;
    color: #2e7d32;
  }

  &.status-failed {
    background-color: #ffcdd2;
    color: #c62828;
  }

  &.status-partial {
    background-color: #fff9c4;
    color: #f57f17;
  }
}

/* B<PERSON>ng sản phẩm bị từ chối */
.rejected-items-section {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 16px;
  background-color: #fafafa;

  h5 {
    font-size: 1rem;
    font-weight: 500;
  }

  .mat-mdc-table {
    background-color: white;

    .mat-mdc-header-cell {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.87);
    }

    .mat-mdc-cell {
      color: rgba(0, 0, 0, 0.87);
    }
  }
}

/* Responsive styles */
@media (max-width: 768px) {
  .rejected-items-section {
    .d-flex {
      flex-direction: column;
      align-items: flex-start !important;

      button {
        margin-top: 8px;
        width: 100%;
      }
    }
  }
}
