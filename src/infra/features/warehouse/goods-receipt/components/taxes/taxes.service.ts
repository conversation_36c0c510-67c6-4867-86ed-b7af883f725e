import { Injectable } from '@angular/core';
import { TaxInfo } from '../../models/api/goods-receipt.dto';

@Injectable()
export class TaxesService {
  constructor() {}

  /**
   * T<PERSON>h tổng thuế
   * @param taxes Danh sách thuế
   * @returns Tổng thuế
   */
  calculateTotalTax(taxes: TaxInfo[] = []): number {
    return taxes.reduce((total, tax) => total + (tax.amount || 0), 0);
  }

  /**
   * Tính số tiền thuế dựa trên tỷ lệ và tổng tiền hàng
   * @param rate Tỷ lệ thuế
   * @param subTotal Tổng tiền hàng
   * @returns Số tiền thuế
   */
  calculateTaxAmount(rate: number, subTotal: number): number {
    return Math.round((subTotal * rate) / 100);
  }
}
