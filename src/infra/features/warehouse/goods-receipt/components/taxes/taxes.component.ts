import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { TaxInfo } from '../../models/api/goods-receipt.dto';
import { TaxFormModalService } from '@/shared/modals/common/tax-form-modal';
import { TaxesService } from './taxes.service';
import { ConfirmModalService } from '@/shared/modals/common/confirm-modal';

@Component({
  selector: 'app-taxes',
  templateUrl: './taxes.component.html',
  styleUrls: ['./taxes.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatExpansionModule,
    MatIconModule,
    MatBadgeModule,
    MatButtonModule,
    MatTooltipModule,
    MatDialogModule,
    TranslateModule
  ],
  providers: [TaxesService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TaxesComponent implements OnInit {
  /**
   * Danh sách thuế
   */
  @Input() taxes: TaxInfo[] = [];

  /**
   * Tổng tiền hàng để tính thuế tự động
   */
  @Input() subTotal: number = 0;

  /**
   * Sự kiện khi danh sách thuế thay đổi
   */
  @Output() taxesChange = new EventEmitter<TaxInfo[]>();

  /**
   * Tổng thuế
   */
  totalTax: number = 0;

  /**
   * Trạng thái mở rộng của panel
   */
  isExpanded: boolean = false;

  constructor(
    private dialog: MatDialog,
    private taxesService: TaxesService,
    private cdr: ChangeDetectorRef,
    private taxFormModalService: TaxFormModalService,
    private confirmModalService: ConfirmModalService
  ) {}

  ngOnInit(): void {
    this.updateTotalTax();
  }

  /**
   * Cập nhật khi input thay đổi
   */
  ngOnChanges(): void {
    this.updateTotalTax();
  }

  /**
   * Cập nhật tổng thuế
   */
  private updateTotalTax(): void {
    this.totalTax = this.taxesService.calculateTotalTax(this.taxes);
    this.cdr.markForCheck();
  }

  /**
   * Mở modal thêm thuế mới
   */
  async openAddTaxDialog(): Promise<void> {
    try {
      const result = await this.taxFormModalService.open({
        subTotal: this.subTotal
      });

      if (result) {
        // Thêm thuế mới vào danh sách
        const updatedTaxes = [...this.taxes, result];

        // Cập nhật danh sách thuế
        this.taxes = updatedTaxes;

        // Tính lại tổng thuế
        this.updateTotalTax();

        // Emit sự kiện thay đổi thuế
        this.taxesChange.emit(this.taxes);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thêm thuế:', error);
    }
  }

  /**
   * Mở modal chỉnh sửa thuế
   * @param tax Thuế cần chỉnh sửa
   * @param index Vị trí của thuế trong danh sách
   */
  async openEditTaxDialog(tax: TaxInfo, index: number): Promise<void> {
    try {
      const result = await this.taxFormModalService.open({
        tax: tax,
        subTotal: this.subTotal
      });

      if (result) {
        // Cập nhật thuế trong danh sách
        const updatedTaxes = [...this.taxes];
        updatedTaxes[index] = result;

        // Cập nhật danh sách thuế
        this.taxes = updatedTaxes;

        // Tính lại tổng thuế
        this.updateTotalTax();

        // Emit sự kiện thay đổi thuế
        this.taxesChange.emit(this.taxes);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chỉnh sửa thuế:', error);
    }
  }

  /**
   * Xóa thuế
   * @param index Vị trí của thuế trong danh sách
   */
  async deleteTax(index: number): Promise<void> {
    try {
      const confirmed = await this.confirmModalService.confirm({
        title: 'WAREHOUSE.GOODS_RECEIPT.TAXES.DELETE_CONFIRM_TITLE',
        message: 'WAREHOUSE.GOODS_RECEIPT.TAXES.DELETE_CONFIRM_MESSAGE',
        confirmText: 'COMMON.DELETE',
        cancelText: 'COMMON.CANCEL',
        confirmColor: 'warn'
      });

      if (confirmed) {
        // Xóa thuế khỏi danh sách
        const updatedTaxes = [...this.taxes];
        updatedTaxes.splice(index, 1);

        // Cập nhật danh sách thuế
        this.taxes = updatedTaxes;

        // Tính lại tổng thuế
        this.updateTotalTax();

        // Emit sự kiện thay đổi thuế
        this.taxesChange.emit(this.taxes);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal xác nhận xóa thuế:', error);
    }
  }

  /**
   * Toggle trạng thái mở rộng của panel
   */
  toggleExpanded(): void {
    this.isExpanded = !this.isExpanded;
  }

  /**
   * Format số hiển thị có dấu phân cách hàng nghìn
   */
  formatNumber(value: number): string {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  /**
   * Lấy tên loại thuế hiển thị
   */
  getTaxTypeName(type: string): string {
    switch (type) {
      case 'VAT':
        return 'WAREHOUSE.GOODS_RECEIPT.TAXES.TYPES.VAT';
      case 'import_tax':
        return 'WAREHOUSE.GOODS_RECEIPT.TAXES.TYPES.IMPORT_TAX';
      case 'other':
        return 'WAREHOUSE.GOODS_RECEIPT.TAXES.TYPES.OTHER';
      default:
        return type;
    }
  }

  /**
   * Ngăn chặn sự kiện click lan truyền khi nhấn vào nút
   */
  stopPropagation(event: Event): void {
    event.stopPropagation();
  }
}
