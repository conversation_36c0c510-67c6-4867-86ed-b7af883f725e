import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { ImportAdditionalCost } from '../../models/api/goods-receipt.dto';
import { SupplierCostsService } from './supplier-costs.service';
import { SelectAdditionalCostsModalService } from '@/features/warehouse/goods-receipt/components/select-additional-costs-modal';

@Component({
  selector: 'app-supplier-costs',
  templateUrl: './supplier-costs.component.html',
  styleUrls: ['./supplier-costs.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatExpansionModule,
    MatIconModule,
    MatBadgeModule,
    MatButtonModule,
    MatTooltipModule,
    TranslateModule
  ],
  providers: [SupplierCostsService],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SupplierCostsComponent implements OnInit {
  /**
   * Danh sách tất cả chi phí
   */
  @Input() additionalCosts: ImportAdditionalCost[] = [];

  /**
   * Tổng tiền hàng để tính thuế tự động cho chi phí phần trăm
   */
  @Input() subTotal: number = 0;

  /**
   * Sự kiện khi danh sách chi phí thay đổi
   */
  @Output() additionalCostsChange = new EventEmitter<ImportAdditionalCost[]>();

  /**
   * Danh sách chi phí trả cho nhà cung cấp
   */
  supplierCosts: ImportAdditionalCost[] = [];

  /**
   * Tổng chi phí trả cho nhà cung cấp
   */
  totalSupplierCost: number = 0;

  /**
   * Trạng thái mở rộng của panel
   */
  isExpanded: boolean = false;

  constructor(
    private selectAdditionalCostsModalService: SelectAdditionalCostsModalService,
    private supplierCostsService: SupplierCostsService
  ) {}

  ngOnInit(): void {
    this.updateSupplierCosts();
  }

  /**
   * Cập nhật khi input thay đổi
   */
  ngOnChanges(): void {
    this.updateSupplierCosts();
  }

  /**
   * Cập nhật danh sách chi phí trả cho nhà cung cấp
   */
  private updateSupplierCosts(): void {
    // Lọc ra các chi phí trả cho nhà cung cấp
    this.supplierCosts = this.additionalCosts.filter(cost => cost.paidToSupplier);

    // Tính tổng chi phí
    this.totalSupplierCost = this.supplierCostsService.calculateTotalCost(this.supplierCosts);
  }

  /**
   * Mở modal chọn chi phí trả cho nhà cung cấp
   */
  async openSelectCostsDialog(): Promise<void> {
    // Lấy danh sách tất cả chi phí có thể chọn từ service
    this.supplierCostsService.getAllCosts().subscribe(async allCosts => {
      const result = await this.selectAdditionalCostsModalService.open({
        items: allCosts.filter(cost => cost.paidToSupplier), // Chỉ lấy chi phí trả cho NCC
        current: this.supplierCosts,
        subTotal: this.subTotal
      });

      if (result) {
        // Lọc ra các chi phí không trả cho nhà cung cấp
        const otherCosts = this.additionalCosts.filter(cost => !cost.paidToSupplier);

        // Cập nhật danh sách chi phí
        const updatedCosts = [...otherCosts, ...result];

        // Emit sự kiện thay đổi
        this.additionalCostsChange.emit(updatedCosts);
      }
    });
  }

  /**
   * Format số hiển thị có dấu phân cách hàng nghìn
   */
  formatNumber(value: number): string {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  /**
   * Format giá trị chi phí hiển thị
   */
  formatCostValue(cost: ImportAdditionalCost): string {
    if (cost.costValue.type === 'fixed') {
      return `${this.formatNumber(cost.costValue.value)} VND`;
    } else {
      return `${cost.costValue.value}% (${this.formatNumber((cost.costValue.value * this.subTotal) / 100)} VND)`;
    }
  }

  /**
   * Toggle trạng thái mở rộng của panel
   */
  toggleExpanded(): void {
    this.isExpanded = !this.isExpanded;
  }
}
