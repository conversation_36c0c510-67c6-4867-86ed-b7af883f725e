import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ImportAdditionalCost } from'../../models/api/goods-receipt.dto';

@Injectable()
export class SupplierCostsService {
  constructor() {}

  /**
   * Tính tổng chi phí trả cho nhà cung cấp
   * @param costs Danh sách chi phí
   * @returns Tổng chi phí
   */
  calculateTotalCost(costs: ImportAdditionalCost[]): number {
    return costs.reduce((total, cost) => {
      if (cost.costValue.type === 'fixed') {
        return total + (cost.costValue?.value || 0);
      } else {
        // Nếu là phần trăm, giá trị thực tế sẽ được tính trong component
        return total + (cost.costValue?.value || 0);
      }
    }, 0);
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách tất cả chi phí có thể chọn
   * @returns Danh sách chi phí
   */
  getAllCosts(): Observable<ImportAdditionalCost[]> {
    // Mock data - trong thực tế sẽ gọi API
    const mockCosts: ImportAdditionalCost[] = [
      {
        _id: '1',
        name: 'Phí vận chuyển',
        costValue: {
          type: 'fixed',
          value: 100000
        },
        paidToSupplier: true,
        allocateToItems: true,
        isActive: true,
        autoAddToPurchaseOrder: true,
        refundOnReturn: false
      },
      {
        _id: '2',
        name: 'Phí đóng gói',
        costValue: {
          type: 'fixed',
          value: 50000
        },
        paidToSupplier: true,
        allocateToItems: true,
        isActive: true,
        autoAddToPurchaseOrder: false,
        refundOnReturn: false
      },
      {
        _id: '3',
        name: 'Phí bảo hiểm',
        costValue: {
          type: 'percentage',
          value: 1
        },
        paidToSupplier: true,
        allocateToItems: true,
        isActive: true,
        autoAddToPurchaseOrder: false,
        refundOnReturn: false,
        tax: {
          rate: 10,
          amount: 0
        }
      }
    ];

    return of(mockCosts);
  }
}
