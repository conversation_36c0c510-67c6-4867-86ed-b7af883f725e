import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatBottomSheet, MatBottomSheetModule } from '@angular/material/bottom-sheet';
import { MatDialog } from '@angular/material/dialog';
import { MatChipsModule } from '@angular/material/chips';
import { MatExpansionModule } from '@angular/material/expansion';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { OrderItemVariantUnitSelectionModalService } from '@/shared/modals/sales/order/order-item-variant-unit-selection-modal';
import { ResponsiveModalService } from '@core/services/responsive-modal.service';
import { BatchModalComponent, BatchData } from '@/shared/modals/warehouse/batch-modal';
import { WarehouseLocationPickerModalService } from '@/shared/modals/warehouse/warehouse-location-picker-modal';

import { ProductListService } from './product-list.service';
import { WarehouseLocation } from '@shared/models/api/warehouse-entities.dto';
import { GoodsReceiptItem, EmbeddedProduct, EmbeddedWarehouseLocation, ProductBatchItem, GoodsReceipt } from '../../models/api/goods-receipt.dto';

/**
 * Component danh sách sản phẩm
 */
@Component({
  selector: 'app-product-list',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatBottomSheetModule,
    MatChipsModule,
    MatExpansionModule,
    TranslateModule
  ],
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductListComponent implements OnInit, OnDestroy {
  /**
   * Danh sách vị trí trong kho
   */
  @Input() warehouseLocations: WarehouseLocation[] = [];

  /**
   * ID kho hàng đã chọn
   */
  @Input() set warehouseId(id: string) {
    this._warehouseId = id;
    this.filterLocationsByWarehouse();
  }
  get warehouseId(): string {
    return this._warehouseId;
  }
  private _warehouseId: string = '';

  /**
   * Sự kiện khi danh sách sản phẩm thay đổi
   */
  @Output() itemsChanged = new EventEmitter<GoodsReceiptItem[]>();

  /**
   * Sự kiện khi tổng tiền thay đổi
   */
  @Output() subtotalChanged = new EventEmitter<number>();

  /**
   * Các cột hiển thị trong bảng
   */
  displayedColumns: string[] = [
    'actions', 'product', 'unit', 'quantityOrdered', 'quantityReceived', 'quantityAccepted',
    'price', 'discount', 'total', 'location'
  ];

  /**
   * Danh sách các sản phẩm đang mở rộng để hiển thị lô hàng
   */
  expandedItems: Set<number> = new Set<number>();

  /**
   * Danh sách sản phẩm
   */
  items: GoodsReceiptItem[] = [];

  /**
   * Danh sách vị trí đã lọc theo kho
   */
  filteredLocations: any[] = [];

  /**
   * Subject để hủy các subscription khi component bị hủy
   */
  private destroy$ = new Subject<void>();

  constructor(
    private productListService: ProductListService,
    private bottomSheet: MatBottomSheet,
    private dialog: MatDialog,
    private responsiveModalService: ResponsiveModalService,
    private orderItemVariantUnitSelectionModalService: OrderItemVariantUnitSelectionModalService,
    private warehouseLocationPickerModalService: WarehouseLocationPickerModalService
  ) { }

  ngOnInit(): void {
    // Theo dõi thay đổi danh sách sản phẩm
    this.productListService.items$.pipe(
      takeUntil(this.destroy$)
    ).subscribe(items => {
      this.items = items;
      this.itemsChanged.emit(items);
      this.subtotalChanged.emit(this.productListService.getTotalAmount());
    });

    // Lọc vị trí theo kho
    this.filterLocationsByWarehouse();
  }

  /**
   * Thêm sản phẩm vào danh sách
   * @param product Sản phẩm cần thêm
   */
  addProduct(product: EmbeddedProduct): void {
    if (!product) return;

    const price = product.price || 0;
    const quantity = 1;

    this.productListService.addItem(product, price, quantity);
  }

  /**
   * Cập nhật số lượng nhận
   * @param index Vị trí sản phẩm cần cập nhật
   * @param quantity Số lượng mới
   */
  updateQuantityReceived(index: number, quantity: number): void {
    if (quantity < 0) return;

    this.productListService.updateItem(index, { quantityReceived: quantity });
  }

  /**
   * Cập nhật số lượng chấp nhận
   * @param index Vị trí sản phẩm cần cập nhật
   * @param quantity Số lượng mới
   */
  updateQuantityAccepted(index: number, quantity: number): void {
    if (quantity < 0) return;

    const item = this.items[index];
    if (quantity > item.quantityReceived) {
      // Số lượng chấp nhận không được vượt quá số lượng nhận
      quantity = item.quantityReceived;
    }

    this.productListService.updateItem(index, { quantityAccepted: quantity });
  }

  /**
   * Cập nhật giá sản phẩm
   * @param index Vị trí sản phẩm cần cập nhật
   * @param price Giá mới
   */
  updatePrice(index: number, price: number): void {
    if (price < 0) return;

    this.productListService.updateItem(index, { price: price });
  }

  /**
   * Cập nhật chiết khấu sản phẩm
   * @param index Vị trí sản phẩm cần cập nhật
   * @param value Giá trị chiết khấu
   * @param _type Loại chiết khấu (không sử dụng trong phiên bản hiện tại)
   */
  updateDiscount(index: number, value: number, _type: string): void {
    if (value < 0) return;

    // Trong phiên bản hiện tại, discount là số, không phải object
    this.productListService.updateItem(index, { discount: value });
  }

  /**
   * Mở modal chọn vị trí kho
   * @param index Vị trí sản phẩm cần cập nhật
   */
  async openLocationPickerModal(index: number): Promise<void> {
    if (!this.warehouseId) {
      console.error('Vui lòng chọn kho hàng trước');
      return;
    }

    try {
      const currentItem = this.items[index];
      const currentLocationId = currentItem.warehouseLocation?._id;

      const result = await this.warehouseLocationPickerModalService.open({
        warehouseId: this.warehouseId,
        locations: this.warehouseLocations,
        selectedLocationId: currentLocationId
      });

      if (result) {
        // Tìm đối tượng location từ ID
        const selectedLocation = this.warehouseLocations.find(loc => loc._id === result);
        if (selectedLocation) {
          this.updateLocation(index, selectedLocation);
        }
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chọn vị trí kho:', error);
    }
  }

  /**
   * Cập nhật vị trí sản phẩm
   * @param index Vị trí sản phẩm cần cập nhật
   * @param location Vị trí mới
   */
  updateLocation(index: number, location: any): void {
    if (location) {
      this.productListService.updateItem(index, {
        warehouseLocation: location as EmbeddedWarehouseLocation
      });
    }
  }

  /**
   * Mở modal để chọn đơn vị tính
   * @param index Vị trí sản phẩm cần cập nhật đơn vị tính
   */
  openUnitSelector(index: number): void {
    const currentItem = this.items[index];
    // Trong phiên bản hiện tại, chúng ta chưa có thông tin về units của sản phẩm
    // Sẽ cần cập nhật sau khi có API hoặc mock data

    // Tạo các đơn vị tính mẫu
    const mockUnits = [
      { unitName: 'cái', conversionRate: 1, isBaseUnit: true, price: 0 },
      { unitName: 'hộp', conversionRate: 10, isBaseUnit: false, price: 0 },
      { unitName: 'thùng', conversionRate: 100, isBaseUnit: false, price: 0 }
    ];

    // Tìm đơn vị tính hiện tại hoặc sử dụng đơn vị mặc định
    const currentUnitName = typeof currentItem.product.unit === 'string' ? currentItem.product.unit : 'cái';
    const currentUnit = mockUnits.find(u => u.unitName === currentUnitName) || mockUnits[0];

    // Mở modal chọn đơn vị tính
    this.orderItemVariantUnitSelectionModalService.open({
      variants: [],
      currentValue: {
        variant: { variantId: '', attributes: [] },
        unit: currentUnit
      },
      units: mockUnits
    }).then(result => {
      if (result && result.unit) {
        // Cập nhật đơn vị tính
        const updatedProduct = {
          ...currentItem.product,
          unit: result.unit // Sử dụng đối tượng ProductUnit thay vì chỉ lấy tên
        };
        this.productListService.updateItem(index, { product: updatedProduct });
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn đơn vị tính:', error);
    });
  }

  /**
   * Mở modal để chọn biến thể
   * @param index Vị trí sản phẩm cần cập nhật biến thể
   */
  openVariantSelector(index: number): void {
    const currentItem = this.items[index];
    // Trong phiên bản hiện tại, chúng ta chưa có thông tin về variants của sản phẩm
    // Sẽ cần cập nhật sau khi có API hoặc mock data

    // Tạo danh sách variants mẫu
    const mockVariants = [
      {
        variantId: 'var1',
        attributes: [
          { name: 'Kích thước', value: 'S' },
          { name: 'Màu sắc', value: 'Đỏ' }
        ]
      },
      {
        variantId: 'var2',
        attributes: [
          { name: 'Kích thước', value: 'M' },
          { name: 'Màu sắc', value: 'Xanh' }
        ]
      },
      {
        variantId: 'var3',
        attributes: [
          { name: 'Kích thước', value: 'L' },
          { name: 'Màu sắc', value: 'Đen' }
        ]
      }
    ];

    // Tìm đơn vị tính hiện tại hoặc sử dụng đơn vị mặc định
    const currentUnitName = typeof currentItem.product.unit === 'string' ? currentItem.product.unit : 'cái';
    const currentUnit = { unitName: currentUnitName, conversionRate: 1, isBaseUnit: true, price: 0 };

    // Mở modal chọn biến thể
    this.orderItemVariantUnitSelectionModalService.open({
      variants: mockVariants,
      currentValue: {
        variant: currentItem.product.variant || mockVariants[0],
        unit: currentUnit
      },
      units: []
    }).then(result => {
      if (result && result.variant) {
        // Cập nhật biến thể
        const updatedProduct = {
          ...currentItem.product,
          variant: result.variant,
          // Cập nhật tên sản phẩm để hiển thị biến thể
          name: `${currentItem.product.name} (${result.variant.attributes.map((attr: any) => attr.value).join(', ')})`
        };
        this.productListService.updateItem(index, { product: updatedProduct });
      }
    }).catch(error => {
      console.error('Lỗi khi mở modal chọn biến thể:', error);
    });
  }

  /**
   * Mở modal thêm lô hàng mới
   * @param index Vị trí sản phẩm cần thêm lô
   */
  async openAddBatchDialog(index: number): Promise<void> {
    try {
      const result = await this.responsiveModalService.open<
        BatchModalComponent,
        BatchData,
        BatchData
      >(
        BatchModalComponent,
        {
          data: {
            batchNumber: '',
            manufacturingDate: new Date(),
            expiryDate: new Date() as Date | null,
            quantity: 0 as number | null
          }
        }
      );

      if (result) {
        // Tạo ID tạm thời cho lô hàng
        const batch: ProductBatchItem = {
          _id: 'temp-batch-' + Date.now(),
          batchCode: result.batchNumber,
          manufactureDate: result.manufacturingDate,
          expiryDate: result.expiryDate ?? new Date(),
          quantity: result.quantity ?? 0
        };

        // Thêm lô hàng vào sản phẩm
        this.productListService.addBatch(index, batch);

        // Mở rộng hàng để hiển thị lô hàng vừa thêm
        this.expandedItems.add(index);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal thêm lô hàng:', error);
    }
  }

  /**
   * Mở modal chỉnh sửa lô hàng
   * @param itemIndex Vị trí sản phẩm
   * @param batchIndex Vị trí lô hàng
   */
  async openEditBatchDialog(itemIndex: number, batchIndex: number): Promise<void> {
    const item = this.items[itemIndex];
    if (!item.batches || batchIndex < 0 || batchIndex >= item.batches.length) return;

    const batch = item.batches[batchIndex];

    try {
      const result = await this.responsiveModalService.open<
        BatchModalComponent,
        BatchData,
        BatchData
      >(
        BatchModalComponent,
        {
          data: {
            batchNumber: batch.batchCode,
            manufacturingDate: batch.manufactureDate,
            expiryDate: batch.expiryDate,
            quantity: batch.quantity
          }
        }
      );

      if (result) {
        // Cập nhật thông tin lô hàng
        const updatedBatch: ProductBatchItem = {
          ...batch,
          batchCode: result.batchNumber,
          manufactureDate: result.manufacturingDate,
          expiryDate: result.expiryDate ?? new Date(),
          quantity: result.quantity ?? 0
        };

        // Cập nhật lô hàng
        this.productListService.updateBatch(itemIndex, batchIndex, updatedBatch);
      }
    } catch (error) {
      console.error('Lỗi khi mở modal chỉnh sửa lô hàng:', error);
    }
  }

  /**
   * Xóa lô hàng
   * @param itemIndex Vị trí sản phẩm
   * @param batchIndex Vị trí lô hàng
   */
  removeBatch(itemIndex: number, batchIndex: number): void {
    if (confirm('Bạn có chắc chắn muốn xóa lô hàng này?')) {
      this.productListService.removeBatch(itemIndex, batchIndex);
    }
  }

  /**
   * Chuyển đổi trạng thái mở rộng của hàng
   * @param index Vị trí hàng cần chuyển đổi trạng thái
   */
  toggleExpandRow(index: number): void {
    if (this.expandedItems.has(index)) {
      this.expandedItems.delete(index);
    } else {
      this.expandedItems.add(index);
    }
  }

  /**
   * Kiểm tra xem hàng có đang được mở rộng không
   * @param index Vị trí hàng cần kiểm tra
   * @returns true nếu hàng đang được mở rộng
   */
  isExpanded(index: number): boolean {
    return this.expandedItems.has(index);
  }

  /**
   * Định dạng ngày tháng
   * @param date Ngày cần định dạng
   * @returns Chuỗi ngày tháng đã định dạng
   */
  formatDate(date: Date): string {
    if (!date) return '';
    return new Date(date).toLocaleDateString();
  }

  /**
   * Kiểm tra xem sản phẩm có lô hàng không
   * @param item Sản phẩm cần kiểm tra
   * @returns true nếu sản phẩm có lô hàng
   */
  hasBatches(item: GoodsReceiptItem): boolean {
    return !!item.batches && item.batches.length > 0;
  }

  /**
   * Lấy số lượng lô hàng của sản phẩm
   * @param item Sản phẩm cần lấy số lượng lô hàng
   * @returns Số lượng lô hàng
   */
  getBatchCount(item: GoodsReceiptItem): number {
    return item.batches ? item.batches.length : 0;
  }

  /**
   * Xóa sản phẩm khỏi danh sách
   * @param index Vị trí sản phẩm cần xóa
   */
  removeItem(index: number): void {
    this.productListService.removeItem(index);
  }

  /**
   * Lọc vị trí theo kho hàng
   */
  private filterLocationsByWarehouse(): void {
    if (!this.warehouseId) {
      this.filteredLocations = [];
      return;
    }

    this.filteredLocations = this.warehouseLocations.filter(
      location => location.warehouse._id === this.warehouseId
    );
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
