<div class="product-list-table mb-3">
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th>{{ 'COMMON.ACTIONS.DELETE' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.PRODUCT' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.UNIT' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.ORDERED_QUANTITY' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.RECEIVED_QUANTITY' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.ACCEPTED_QUANTITY' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.PRICE' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.DISCOUNT' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.TOTAL' | translate }}</th>
          <th>{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.COLUMNS.LOCATION' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <!-- Hiển thị danh sách sản phẩm -->
        <ng-container *ngIf="items.length > 0; else emptyState">
          <ng-container *ngFor="let item of items; let i = index">
            <!-- Hàng chính -->
            <tr>
              <!-- Xóa -->
              <td>
                <button mat-icon-button color="warn" (click)="removeItem(i)">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>

              <!-- Sản phẩm -->
              <td>
                <div class="product-info">
                  <div class="product-name">{{ item.product.name }}</div>
                  <small *ngIf="item.product.sku" class="text-muted">{{ item.product.sku }}</small>
                  <!-- Nút chọn biến thể -->
                  <div class="mt-1">
                    <a href="javascript:void(0)" (click)="openVariantSelector(i)" class="variant-link">
                      <small>
                        <mat-icon class="small-icon">style</mat-icon>
                        {{ item.product.variant ? 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.CHANGE_VARIANT' : 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.SELECT_VARIANT' | translate }}
                      </small>
                    </a>
                  </div>
                </div>
              </td>

              <!-- Đơn vị tính -->
              <td>
                <a href="javascript:void(0)" (click)="openUnitSelector(i)" class="unit-link">
                  {{ item.product.unit || 'cái' }}
                </a>
              </td>

              <!-- Số lượng đặt hàng -->
              <td>
                <span>{{ item.quantityOrdered || 'N/A' }}</span>
              </td>

              <!-- Số lượng nhận -->
              <td>
                <input type="number" class="form-control form-control-sm"
                      [ngModel]="item.quantityReceived"
                      (ngModelChange)="updateQuantityReceived(i, $event)"
                      min="0"
                      [disabled]="hasBatches(item)">
                <!-- Hiển thị nút quản lý lô hàng -->
                <div class="mt-1">
                  <a href="javascript:void(0)" (click)="toggleExpandRow(i)" class="batch-link">
                    <small>
                      <mat-icon class="small-icon">{{ isExpanded(i) ? 'expand_less' : 'expand_more' }}</mat-icon>
                      {{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.MANAGE_BATCHES' | translate }}
                      <span *ngIf="hasBatches(item)" class="batch-count">({{ getBatchCount(item) }})</span>
                    </small>
                  </a>
                </div>
              </td>

              <!-- Số lượng chấp nhận -->
              <td>
                <input type="number" class="form-control form-control-sm"
                      [ngModel]="item.quantityAccepted"
                      (ngModelChange)="updateQuantityAccepted(i, $event)"
                      min="0"
                      [max]="item.quantityReceived"
                      [disabled]="hasBatches(item)">
              </td>

              <!-- Đơn giá -->
              <td>
                <input type="number" class="form-control form-control-sm"
                      [ngModel]="item.price"
                      (ngModelChange)="updatePrice(i, $event)"
                      min="0">
              </td>

              <!-- Giảm giá -->
              <td>
                <div class="input-group input-group-sm">
                  <input type="number" class="form-control form-control-sm"
                        [ngModel]="item.discount"
                        (ngModelChange)="updateDiscount(i, $event, 'fixed')"
                        min="0">
                  <span class="input-group-text">VND</span>
                </div>
              </td>

              <!-- Thành tiền -->
              <td>{{ item.total | number }} VND</td>

              <!-- Vị trí lưu trữ -->
              <td>
                <div class="location-picker">
                  <div *ngIf="item.warehouseLocation" class="selected-location">
                    {{ item.warehouseLocation.name }}
                    <button mat-icon-button color="primary" class="location-clear-btn"
                            (click)="updateLocation(i, null)">
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>
                  <button mat-stroked-button color="primary" class="location-select-btn"
                          (click)="openLocationPickerModal(i)">
                    <mat-icon>place</mat-icon>
                    {{ (item.warehouseLocation ? 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.CHANGE_LOCATION' : 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.SELECT_LOCATION') | translate }}
                  </button>
                </div>
              </td>
            </tr>

            <!-- Hàng mở rộng hiển thị lô hàng -->
            <tr *ngIf="isExpanded(i)" class="batch-row">
              <td colspan="10" class="batch-container">
                <div class="batch-list p-2">
                  <!-- Tiêu đề -->
                  <div class="d-flex justify-content-between align-items-center mb-2">
                    <h6 class="mb-0">{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.BATCH_LIST' | translate }}</h6>
                    <button mat-stroked-button color="primary" (click)="openAddBatchDialog(i)">
                      <mat-icon>add</mat-icon>
                      {{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.ADD_BATCH' | translate }}
                    </button>
                  </div>

                  <!-- Danh sách lô hàng -->
                  <div class="batch-chips" *ngIf="hasBatches(item); else noBatches">
                    <mat-chip-set>
                      <mat-chip *ngFor="let batch of item.batches; let batchIndex = index" class="batch-chip">
                        <div class="batch-info">
                          <span class="batch-number">{{ batch.batchCode }}</span>
                          <span class="batch-date">{{ formatDate(batch.expiryDate) }}</span>
                          <span class="batch-quantity">{{ batch.quantity }}</span>
                        </div>
                        <div class="batch-actions">
                          <button mat-icon-button (click)="openEditBatchDialog(i, batchIndex)" class="batch-edit-btn">
                            <mat-icon>edit</mat-icon>
                          </button>
                          <button mat-icon-button (click)="removeBatch(i, batchIndex)" class="batch-delete-btn">
                            <mat-icon>close</mat-icon>
                          </button>
                        </div>
                      </mat-chip>
                    </mat-chip-set>
                  </div>

                  <!-- Không có lô hàng -->
                  <ng-template #noBatches>
                    <div class="no-batches text-center py-2">
                      <p class="text-muted mb-0">{{ 'WAREHOUSE.GOODS_RECEIPT.PRODUCT_LIST.NO_BATCHES' | translate }}</p>
                    </div>
                  </ng-template>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>

        <!-- Trạng thái trống -->
        <ng-template #emptyState>
          <tr class="text-center">
            <td colspan="10" class="py-3">
              <p class="text-muted mb-0">{{ 'COMMON.NO_ITEMS' | translate }}</p>
            </td>
          </tr>
        </ng-template>
      </tbody>
    </table>
  </div>
</div>
