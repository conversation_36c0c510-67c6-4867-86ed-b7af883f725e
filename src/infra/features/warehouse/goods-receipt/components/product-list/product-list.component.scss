/* Styles cho component danh sách sản phẩm */
:host {
  display: block;
  width: 100%;
}

.product-list-table {
  .table {
    th {
      background-color: #f8f9fa;
      font-weight: 500;
      vertical-align: middle;
    }

    td {
      vertical-align: middle;
    }
  }
}

.product-info {
  .product-name {
    font-weight: 500;
    font-size: 0.95rem;
  }

  small {
    font-size: 0.8rem;
    display: block;
    margin-top: 2px;
  }
}

.form-control-sm, .form-select-sm {
  padding: 0.25rem 0.5rem;
  height: calc(1.5em + 0.5rem + 2px);
  font-size: 0.875rem;
}

.unit-link, .variant-link, .batch-link {
  color: #007bff;
  text-decoration: none;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
    color: #0056b3;
  }
}

.location-picker {
  .selected-location {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #e9f5ff;
    padding: 6px 10px;
    border-radius: 4px;
    margin-bottom: 8px;
    font-size: 0.9rem;

    .location-clear-btn {
      width: 24px;
      height: 24px;
      line-height: 24px;

      mat-icon {
        font-size: 16px;
        height: 16px;
        width: 16px;
      }
    }
  }

  .location-select-btn {
    font-size: 0.85rem;
    padding: 2px 8px;
    line-height: 24px;

    mat-icon {
      font-size: 16px;
      height: 16px;
      width: 16px;
      margin-right: 4px;
    }
  }
}

.small-icon {
  font-size: 16px;
  height: 16px;
  width: 16px;
  vertical-align: middle;
  margin-right: 4px;
}

.batch-count {
  color: #6c757d;
  margin-left: 4px;
}

.batch-row {
  background-color: #f8f9fa;

  .batch-container {
    padding: 0;
  }

  .batch-list {
    border-top: 1px solid #dee2e6;
  }
}

.batch-chip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  .batch-info {
    display: flex;
    align-items: center;

    .batch-number {
      font-weight: 500;
      margin-right: 8px;
    }

    .batch-date {
      color: #6c757d;
      margin-right: 8px;
      font-size: 0.85rem;
    }

    .batch-quantity {
      background-color: #e9ecef;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.85rem;
    }
  }

  .batch-actions {
    display: flex;
    align-items: center;

    button {
      width: 24px;
      height: 24px;
      line-height: 24px;

      mat-icon {
        font-size: 16px;
        height: 16px;
        width: 16px;
      }
    }
  }
}

// Responsive styles
@media (max-width: 992px) {
  .table-responsive {
    overflow-x: auto;
  }

  .batch-chip {
    flex-direction: column;
    align-items: flex-start;

    .batch-info {
      margin-bottom: 4px;
    }
  }
}

@media (max-width: 768px) {
  .product-list-table {
    .table {
      th, td {
        font-size: 0.85rem;
        padding: 0.5rem 0.25rem;
      }

      .product-info {
        .product-name {
          font-size: 0.85rem;
        }

        small {
          font-size: 0.75rem;
        }
      }
    }
  }

  .form-control-sm, .form-select-sm {
    font-size: 0.8rem;
    padding: 0.2rem 0.4rem;
  }

  .small-icon {
    font-size: 14px;
    height: 14px;
    width: 14px;
  }

  .batch-chip {
    .batch-info {
      .batch-number {
        font-size: 0.8rem;
      }

      .batch-date {
        font-size: 0.75rem;
      }

      .batch-quantity {
        font-size: 0.75rem;
      }
    }
  }
}
