import { Component, Inject, OnInit, Optional, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MAT_BOTTOM_SHEET_DATA, MatBottomSheetRef } from '@angular/material/bottom-sheet';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { MatSortModule } from '@angular/material/sort';
import { TranslateModule } from '@ngx-translate/core';
import { ImportAdditionalCost } from '../../models/api/goods-receipt.dto';
import { AdditionalCostModalService } from '@/features/warehouse/goods-receipt/components/additional-cost-modal/additional-cost-modal.service';

// Interface cho dữ liệu truyền vào modal
export interface SelectAdditionalCostsModalData {
  items: ImportAdditionalCost[]; // Danh sách tất cả chi phí có thể chọn
  current: ImportAdditionalCost[]; // Danh sách chi phí đã chọn trước đó
  subTotal?: number; // Tổng tiền hàng để tính thuế tự động cho chi phí phần trăm
}

// Interface để quản lý mỗi dòng trong bảng
interface CostTableRow {
  cost: ImportAdditionalCost;
  isSelected: boolean;
  customValue: number;
}

@Component({
  selector: 'app-select-additional-costs-modal',
  templateUrl: './select-additional-costs-modal.component.html',
  styleUrls: ['./select-additional-costs-modal.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatCheckboxModule,
    MatFormFieldModule,
    MatInputModule,
    MatTableModule,
    MatPaginatorModule,
    MatIconModule,
    MatSortModule,
    MatTooltipModule,
    TranslateModule
  ]
})
export class SelectAdditionalCostsModalComponent implements OnInit {
  displayedColumns: string[] = [
    'select',
    'name',
    'type',
    'defaultValue',
    'customValue',
    'paidToSupplier',
    'allocateToItems',
    'tax'
  ];

  dataSource = new MatTableDataSource<CostTableRow>();

  @ViewChild(MatPaginator) paginator!: MatPaginator;

  // FormGroup cho việc validate và quản lý dữ liệu form
  form: FormGroup;

  // Dữ liệu đầu vào cho modal
  data: SelectAdditionalCostsModalData;

  constructor(
    private fb: FormBuilder,
    private dialog: MatDialog,
    private additionalCostModalService: AdditionalCostModalService,
    @Optional() private dialogRef?: MatDialogRef<SelectAdditionalCostsModalComponent>,
    @Optional() private bottomSheetRef?: MatBottomSheetRef<SelectAdditionalCostsModalComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) private dialogData?: SelectAdditionalCostsModalData,
    @Optional() @Inject(MAT_BOTTOM_SHEET_DATA) private bottomSheetData?: SelectAdditionalCostsModalData
  ) {
    // Kết hợp dữ liệu từ dialog hoặc bottom sheet
    this.data = this.dialogData || this.bottomSheetData || { items: [], current: [] };

    // Khởi tạo form
    this.form = this.fb.group({
      costs: this.fb.array([])
    });

    // Khởi tạo dữ liệu cho bảng
    this.initializeTableData();
  }

  ngOnInit(): void {
    // Không cần khởi tạo thêm
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }

  /**
   * Khởi tạo dữ liệu cho bảng từ input data
   */
  private initializeTableData(): void {
    const costsFormArray = this.form.get('costs') as FormArray;
    const tableRows: CostTableRow[] = [];

    // Chuyển đổi từ danh sách input sang dữ liệu bảng
    if (this.data.items && this.data.items.length > 0) {
      this.data.items.forEach(item => {
        // Kiểm tra xem chi phí này đã được chọn trước đó chưa
        const existingCost = this.data.current ?
          this.data.current.find(c => c._id === item._id) :
          undefined;

        // Tạo một dòng mới cho bảng
        const row: CostTableRow = {
          cost: { ...item }, // Clone để không ảnh hưởng đến dữ liệu gốc
          isSelected: !!existingCost,
          customValue: existingCost ? existingCost.costValue.value : item.costValue.value
        };

        // Thêm vào mảng dữ liệu bảng
        tableRows.push(row);

        // Tạo FormGroup cho mỗi dòng và thêm vào FormArray
        costsFormArray.push(this.createCostFormGroup(row));
      });
    }

    // Gán dữ liệu cho bảng
    this.dataSource.data = tableRows;
  }

  /**
   * Tạo FormGroup cho mỗi dòng chi phí
   */
  private createCostFormGroup(row: CostTableRow): FormGroup {
    return this.fb.group({
      _id: [row.cost._id],
      isSelected: [row.isSelected],
      customValue: [row.customValue, [
        Validators.required,
        Validators.min(0)
      ]]
    });
  }

  /**
   * Lấy FormArray chứa tất cả các FormGroup của chi phí
   */
  get costsFormArray(): FormArray {
    return this.form.get('costs') as FormArray;
  }

  /**
   * Xử lý sự kiện khi checkbox được chọn/bỏ chọn
   */
  onSelectionChange(index: number, checked: boolean): void {
    // Cập nhật giá trị trong FormArray
    const formGroup = this.costsFormArray.at(index) as FormGroup;
    formGroup.get('isSelected')?.setValue(checked);

    // Cập nhật bộ validate dựa trên trạng thái chọn
    const customValueControl = formGroup.get('customValue');
    if (checked) {
      customValueControl?.setValidators([Validators.required, Validators.min(0)]);
    } else {
      customValueControl?.clearValidators();
    }
    customValueControl?.updateValueAndValidity();

    // Cập nhật dữ liệu dataSource
    this.dataSource.data[index].isSelected = checked;
  }

  /**
   * Xử lý khi giá trị tùy chỉnh thay đổi
   */
  onCustomValueChange(index: number, value: number): void {
    const row = this.dataSource.data[index];

    // Cập nhật giá trị trong dataSource
    row.customValue = value;

    // Cập nhật giá trị trong FormArray
    const formGroup = this.costsFormArray.at(index) as FormGroup;
    formGroup.get('customValue')?.setValue(value);

    // Tự động tính lại thuế nếu là chi phí cố định và có thuế
    if (row.cost.costValue.type === 'fixed' && row.cost.tax && row.cost.tax.rate) {
      row.cost.tax.amount = this.calculateTaxAmount(
        row.cost.costValue.type,
        value,
        row.cost.tax.rate
      );
    }
  }

  /**
   * Mở modal thêm mới chi phí
   */
  async openAdditionalCostDialog(): Promise<void> {
    const result = await this.additionalCostModalService.open({
      subTotal: this.data.subTotal
    });

    if (result) {
      // Thêm chi phí mới vào danh sách
      this.addNewCost(result);
    }
  }

  /**
   * Thêm chi phí mới vào bảng
   */
  private addNewCost(cost: ImportAdditionalCost): void {
    // Tạo row mới
    const newRow: CostTableRow = {
      cost: cost,
      isSelected: true,
      customValue: cost.costValue.value
    };

    // Thêm vào dataSource
    const newData = [...this.dataSource.data, newRow];
    this.dataSource.data = newData;

    // Thêm vào FormArray
    this.costsFormArray.push(this.createCostFormGroup(newRow));

    // Cuộn xuống dòng mới nếu có paginator
    if (this.paginator) {
      const lastPage = Math.ceil(newData.length / this.paginator.pageSize) - 1;
      this.paginator.pageIndex = lastPage;
      this.paginator.page.emit();
    }
  }

  /**
   * Tính toán số tiền thuế dựa trên loại chi phí, giá trị chi phí và tỷ lệ thuế
   */
  calculateTaxAmount(type: string, value: number, rate: number): number {
    if (type === 'fixed') {
      return Number(((value * rate) / 100).toFixed(0));
    } else if (type === 'percentage' && this.data.subTotal) {
      // Nếu là phần trăm, tính dựa trên tổng tiền hàng và tỷ lệ
      const costAmount = (value * this.data.subTotal) / 100;
      return Number(((costAmount * rate) / 100).toFixed(0));
    }

    return 0;
  }

  /**
   * Lấy tên loại chi phí hiển thị
   */
  getCostTypeName(type: string): string {
    return type === 'fixed' ? 'Cố định' : 'Phần trăm';
  }

  /**
   * Format giá trị chi phí hiển thị
   */
  formatCostValue(cost: ImportAdditionalCost): string {
    if (cost.costValue.type === 'fixed') {
      return `${this.formatNumber(cost.costValue.value)} VND`;
    } else {
      return `${cost.costValue.value}%`;
    }
  }

  /**
   * Format thông tin thuế hiển thị
   */
  formatTaxInfo(cost: ImportAdditionalCost): string {
    if (!cost.tax) return 'Không';

    return `${cost.tax.rate}% (${this.formatNumber(cost.tax.amount)} VND)`;
  }

  /**
   * Format số hiển thị có dấu phân cách hàng nghìn
   */
  formatNumber(value: number): string {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  /**
   * Kiểm tra xem form có hợp lệ không để enable nút Save
   */
  isFormValid(): boolean {
    // Form hợp lệ khi ít nhất có một chi phí được chọn và tất cả chi phí được chọn đều có giá trị hợp lệ
    let isValid = false;

    // Kiểm tra từng dòng trong dataSource
    this.dataSource.data.forEach(row => {
      if (row.isSelected) {
        // Đã có ít nhất một chi phí được chọn
        isValid = true;

        // Kiểm tra giá trị tùy chỉnh hợp lệ
        if (row.customValue === undefined || row.customValue === null || row.customValue < 0) {
          isValid = false;
          return false; // break forEach
        }
      }
    });

    return isValid;
  }

  /**
   * Hủy bỏ modal
   */
  onCancel(): void {
    this.close();
  }

  /**
   * Xử lý khi người dùng nhấn Lưu
   */
  onSave(): void {
    if (!this.isFormValid()) {
      return;
    }

    // Lọc và chỉ lấy các chi phí đã được chọn
    const selectedCosts: ImportAdditionalCost[] = [];

    this.dataSource.data.forEach((row, index) => {
      const formGroup = this.costsFormArray.at(index) as FormGroup;
      const isSelected = formGroup.get('isSelected')?.value;

      if (isSelected) {
        const customValue = formGroup.get('customValue')?.value;
        const cost = { ...row.cost }; // Clone để không ảnh hưởng dữ liệu gốc

        // Cập nhật giá trị tùy chỉnh
        cost.costValue.value = customValue;

        // Tự động cập nhật thuế nếu là chi phí cố định
        if (cost.costValue.type === 'fixed' && cost.tax && cost.tax.rate) {
          cost.tax.amount = this.calculateTaxAmount(
            cost.costValue.type,
            customValue,
            cost.tax.rate
          );
        }

        selectedCosts.push(cost);
      }
    });

    // Đóng modal và trả về danh sách chi phí đã chọn
    this.close(selectedCosts);
  }

  /**
   * Đóng modal và trả về kết quả
   */
  private close(result?: ImportAdditionalCost[]): void {
    if (this.dialogRef) {
      this.dialogRef.close(result);
    } else if (this.bottomSheetRef) {
      this.bottomSheetRef.dismiss(result);
    }
  }
}
