<h2 mat-dialog-title>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.TITLE' | translate }}</h2>

<div class="new-cost-button">
  <button mat-raised-button color="primary" (click)="openAdditionalCostDialog()">
    <mat-icon>add</mat-icon>
    {{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.CREATE_NEW' | translate }}
  </button>
</div>

<form [formGroup]="form">
  <mat-dialog-content>
    <div class="mat-elevation-z2 table-container">
      <table mat-table [dataSource]="dataSource">
        <!-- Cột chọn -->
        <ng-container matColumnDef="select">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.SELECT' | translate }}</th>
          <td mat-cell *matCellDef="let row; let i = index">
            <mat-checkbox
              [checked]="row.isSelected"
              (change)="onSelectionChange(i, $event.checked)">
            </mat-checkbox>
          </td>
        </ng-container>

        <!-- Cột tên -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.NAME' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{ row.cost.name }}</td>
        </ng-container>

        <!-- Cột loại -->
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.TYPE' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{ getCostTypeName(row.cost.costValue.type) }}</td>
        </ng-container>

        <!-- Cột giá trị mặc định -->
        <ng-container matColumnDef="defaultValue">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.DEFAULT_VALUE' | translate }}</th>
          <td mat-cell *matCellDef="let row">{{ formatCostValue(row.cost) }}</td>
        </ng-container>

        <!-- Cột giá trị tùy chỉnh -->
        <ng-container matColumnDef="customValue">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.CUSTOM_VALUE' | translate }}</th>
          <td mat-cell *matCellDef="let row; let i = index">
            <mat-form-field appearance="outline" class="custom-value-field">
              <input
                matInput
                type="number"
                [(ngModel)]="row.customValue"
                [ngModelOptions]="{standalone: true}"
                (ngModelChange)="onCustomValueChange(i, $event)"
                [required]="row.isSelected"
                min="0"
                step="1">
              <span matSuffix>{{ row.cost.costValue.type === 'fixed' ? 'VND' : '%' }}</span>
              <mat-error *ngIf="row.isSelected && !row.customValue && row.customValue !== 0">
                {{ 'VALIDATION.REQUIRED' | translate }}
              </mat-error>
              <mat-error *ngIf="row.customValue < 0">
                {{ 'VALIDATION.MIN_VALUE' | translate: { min: 0 } }}
              </mat-error>
            </mat-form-field>
          </td>
        </ng-container>

        <!-- Cột trả cho nhà cung cấp -->
        <ng-container matColumnDef="paidToSupplier">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.PAID_TO_SUPPLIER' | translate }}</th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox [checked]="row.cost.paidToSupplier" disabled></mat-checkbox>
          </td>
        </ng-container>

        <!-- Cột phân bổ vào giá vốn -->
        <ng-container matColumnDef="allocateToItems">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.ALLOCATE' | translate }}</th>
          <td mat-cell *matCellDef="let row">
            <mat-checkbox [checked]="row.cost.allocateToItems" disabled></mat-checkbox>
          </td>
        </ng-container>

        <!-- Cột thuế -->
        <ng-container matColumnDef="tax">
          <th mat-header-cell *matHeaderCellDef>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.TAX' | translate }}</th>
          <td mat-cell *matCellDef="let row">
            {{ formatTaxInfo(row.cost) }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <mat-paginator
        [pageSizeOptions]="[5, 10, 20]"
        showFirstLastButtons>
      </mat-paginator>
    </div>

    <!-- Thông tin bổ sung -->
    <div *ngIf="data.subTotal" class="text-gray-600 text-sm mt-3">
      <p>{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.SUB_TOTAL_INFO' | translate: { value: formatNumber(data.subTotal) } }}</p>
      <p class="notice">{{ 'WAREHOUSE.SELECT_ADDITIONAL_COSTS.TAX_UPDATE_NOTICE' | translate }}</p>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">
      {{ 'COMMON.CANCEL' | translate }}
    </button>
    <button
      mat-raised-button
      color="primary"
      [disabled]="!isFormValid()"
      (click)="onSave()">
      {{ 'COMMON.SAVE' | translate }}
    </button>
  </mat-dialog-actions>
</form>
