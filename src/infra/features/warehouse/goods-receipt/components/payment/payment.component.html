<div class="payment-container" [formGroup]="paymentForm">
  <!-- Đã thanh toán -->
  <div class="amount-paid-container mb-2">
    <mat-form-field appearance="outline" class="w-100">
      <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.PAYMENT.AMOUNT_PAID' | translate }}</mat-label>
      <input matInput type="number" min="0" formControlName="amountPaid">
      <span matSuffix>VND</span>
    </mat-form-field>
    <button mat-stroked-button color="primary" class="full-payment-btn" (click)="fillFullPayment()">
      100%
    </button>
  </div>

  <!-- <PERSON><PERSON>ơng thức thanh toán -->
  <div class="mb-3">
    <label class="form-label">{{ 'WAREHOUSE.GOODS_RECEIPT.PAYMENT.PAYMENT_METHOD' | translate }}:</label>
    <div class="payment-methods">
      <mat-radio-group formControlName="paymentMethod" class="d-flex flex-wrap">
        <mat-radio-button value="cash" class="me-3 mb-2">
          {{ 'WAREHOUSE.GOODS_RECEIPT.PAYMENT.PAYMENT_METHODS.CASH' | translate }}
        </mat-radio-button>
        <mat-radio-button value="bankTransfer" class="me-3 mb-2">
          {{ 'WAREHOUSE.GOODS_RECEIPT.PAYMENT.PAYMENT_METHODS.BANK_TRANSFER' | translate }}
        </mat-radio-button>
      </mat-radio-group>
    </div>
  </div>

  <!-- Tài khoản ngân hàng (hiển thị khi chọn chuyển khoản) -->
  <mat-form-field appearance="outline" class="w-100 mb-3" *ngIf="paymentForm.get('paymentMethod')?.value === 'bankTransfer'">
    <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.PAYMENT.SELECT_BANK' | translate }}</mat-label>
    <mat-select formControlName="bankAccountId">
      <mat-option *ngFor="let bank of bankAccounts" [value]="bank.id">{{ bank.name }}</mat-option>
    </mat-select>
  </mat-form-field>

  <!-- Ngày hẹn thanh toán -->
  <mat-form-field appearance="outline" class="w-100 mb-2">
    <mat-label>{{ 'WAREHOUSE.GOODS_RECEIPT.PAYMENT.DUE_DATE' | translate }}</mat-label>
    <input matInput [matDatepicker]="dueDatePicker" formControlName="dueDate">
    <mat-datepicker-toggle matSuffix [for]="dueDatePicker"></mat-datepicker-toggle>
    <mat-datepicker #dueDatePicker></mat-datepicker>
  </mat-form-field>

  <!-- Công nợ -->
  <div class="d-flex justify-content-between align-items-center mb-2 debt-row">
    <span>{{ 'WAREHOUSE.GOODS_RECEIPT.PAYMENT.DEBT' | translate }}:</span>
    <strong>{{ debt | number }} VND</strong>
  </div>
</div>
