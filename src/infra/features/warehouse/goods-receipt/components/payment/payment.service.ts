import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { InitialPayment } from '../../models/api/goods-receipt.dto';

@Injectable()
export class PaymentService {
  constructor() {}

  /**
   * Tính toán công nợ
   * @param totalAmount Tổng tiền
   * @param amountPaid Số tiền đã thanh toán
   * @returns Số tiền công nợ
   */
  calculateDebt(totalAmount: number, amountPaid: number): number {
    return Math.max(0, totalAmount - amountPaid);
  }

  /**
   * Điền đầy đủ số tiền thanh toán (100%)
   * @param supplierPayment Số tiền cần trả nhà cung cấp
   * @returns Số tiền thanh toán đầy đủ
   */
  fillFullPayment(supplierPayment: number): number {
    return supplierPayment;
  }

  /**
   * Kiểm tra xem phương thức thanh toán có nên bị disable không
   * @param amountPaid Số tiền đã thanh toán
   * @returns True nếu phương thức thanh toán nên bị disable
   */
  shouldDisablePaymentMethod(amountPaid: number): boolean {
    return amountPaid <= 0;
  }

  /**
   * Cập nhật form với dữ liệu có sẵn
   * @param form Form thanh toán
   * @param data Dữ liệu thanh toán
   */
  patchFormData(form: FormGroup, data: {
    initialPayments?: InitialPayment[];
    debt?: {
      debtAmount: number;
      dueDate?: Date;
    };
  }): void {
    if (data) {
      // Xử lý dữ liệu thanh toán ban đầu
      if (data.initialPayments && data.initialPayments.length > 0) {
        const payment = data.initialPayments[0]; // Lấy thanh toán đầu tiên

        form.patchValue({
          paymentMethod: payment.method === 'cash' ? 'cash' : 'bankTransfer',
          amountPaid: payment.amount || 0,
          bankAccountId: payment.bankAccountId,
          dueDate: data.debt?.dueDate
        });
      } else {
        form.patchValue({
          paymentMethod: 'cash',
          amountPaid: 0,
          dueDate: data.debt?.dueDate
        });
      }
    }
  }
}
