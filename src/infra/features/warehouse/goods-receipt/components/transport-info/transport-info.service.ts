import { Injectable } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TransportInfo } from '../../models/api/goods-receipt.dto';

/**
 * Service xử lý logic thông tin vận chuyển
 */
@Injectable({
  providedIn: 'root'
})
export class TransportInfoService {
  constructor(private fb: FormBuilder) { }

  /**
   * Tạo form thông tin vận chuyển
   * @returns FormGroup cho thông tin vận chuyển
   */
  createTransportForm(): FormGroup {
    return this.fb.group({
      transportMethod: ['road'],
      carrier: [''],
      trackingNumber: [''],
      estimatedDeliveryDate: [null],
      actualDeliveryDate: [null]
    });
  }

  /**
   * Lấy dữ liệu form để lưu
   * @param form Form thông tin vận chuyển
   * @returns Dữ liệu thông tin vận chuyển
   */
  getFormData(form: FormGroup): Partial<TransportInfo> {
    const formValue = form.value;

    // Kiểm tra xem có dữ liệu nào được nhập không
    const hasData = Object.values(formValue).some(value =>
      value !== null && value !== '' && value !== undefined
    );

    // Nếu không có dữ liệu, trả về đối tượng rỗng
    if (!hasData) {
      return {};
    }

    return formValue;
  }

  /**
   * Cập nhật form với dữ liệu có sẵn
   * @param form Form thông tin vận chuyển
   * @param data Dữ liệu thông tin vận chuyển
   */
  patchFormData(form: FormGroup, data: Partial<TransportInfo>): void {
    if (data) {
      form.patchValue(data);
    }
  }

  /**
   * Kiểm tra xem thông tin vận chuyển có dữ liệu hay không
   * @param data Dữ liệu thông tin vận chuyển
   * @returns true nếu có ít nhất một trường được điền
   */
  hasTransportInfo(data?: Partial<TransportInfo>): boolean {
    if (!data) return false;

    return Object.values(data).some(value =>
      value !== null && value !== '' && value !== undefined
    );
  }
}
