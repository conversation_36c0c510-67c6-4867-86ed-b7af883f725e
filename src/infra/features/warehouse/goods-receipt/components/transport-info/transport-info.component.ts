import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatBadgeModule } from '@angular/material/badge';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

import { TransportInfoService } from './transport-info.service';
import { TransportInfo } from '../../models/api/goods-receipt.dto';

/**
 * Component hiển thị và quản lý thông tin vận chuyển
 */
@Component({
  selector: 'app-transport-info',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatBadgeModule,
    TranslateModule
  ],
  templateUrl: './transport-info.component.html',
  styleUrls: ['./transport-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TransportInfoComponent implements OnInit, OnDestroy {
  /**
   * Dữ liệu thông tin vận chuyển ban đầu
   */
  @Input() initialData?: Partial<TransportInfo>;

  /**
   * Sự kiện khi dữ liệu thay đổi
   */
  @Output() dataChange = new EventEmitter<Partial<TransportInfo>>();

  /**
   * Form thông tin vận chuyển
   */
  transportForm!: FormGroup;

  /**
   * Trạng thái có thay đổi hay không
   */
  hasChanges = false;

  /**
   * Trạng thái panel có đang mở không
   */
  isExpanded = false;

  /**
   * Subject để hủy các subscription khi component bị hủy
   */
  private destroy$ = new Subject<void>();

  constructor(private transportInfoService: TransportInfoService) { }

  ngOnInit(): void {
    // Khởi tạo form
    this.initForm();

    // Theo dõi thay đổi form
    this.watchFormChanges();

    // Cập nhật form với dữ liệu ban đầu nếu có
    if (this.initialData) {
      this.transportInfoService.patchFormData(this.transportForm, this.initialData);
    }
  }

  /**
   * Khởi tạo form
   */
  private initForm(): void {
    this.transportForm = this.transportInfoService.createTransportForm();
  }

  /**
   * Theo dõi thay đổi form
   */
  private watchFormChanges(): void {
    this.transportForm.valueChanges.pipe(
      takeUntil(this.destroy$)
    ).subscribe(() => {
      // Đánh dấu có thay đổi
      this.hasChanges = true;
    });
  }

  /**
   * Lấy dữ liệu form
   */
  getFormData(): Partial<TransportInfo> {
    return this.transportInfoService.getFormData(this.transportForm);
  }

  /**
   * Kiểm tra xem thông tin vận chuyển có dữ liệu hay không
   */
  hasTransportInfo(): boolean {
    return this.transportInfoService.hasTransportInfo(this.initialData);
  }

  /**
   * Xử lý khi trạng thái panel thay đổi
   * @param isExpanded Trạng thái mở của panel
   */
  onPanelToggle(isExpanded: boolean): void {
    this.isExpanded = isExpanded;
  }

  /**
   * Lưu thông tin vận chuyển
   */
  saveTransportInfo(): void {
    if (this.transportForm.valid) {
      // Emit sự kiện thay đổi
      this.dataChange.emit(this.getFormData());

      // Đánh dấu không có thay đổi
      this.hasChanges = false;
    } else {
      // Đánh dấu tất cả các trường là đã touched để hiển thị lỗi
      this.transportForm.markAllAsTouched();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
