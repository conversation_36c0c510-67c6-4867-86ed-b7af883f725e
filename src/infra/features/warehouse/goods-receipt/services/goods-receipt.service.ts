import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GoodsReceipt, GoodsReceiptItem } from '../models/api/goods-receipt.dto';
import { EmbeddedSupplier as Supplier } from '@shared/models/api/entity-embedded.dto';
import { GoodsReceiptUseCase } from '@application/use-cases/warehouse/goods-receipt/goods-receipt.usecase';
import { GoodsReceiptRepositoryImpl } from './goods-receipt-repository.impl';

// Mock data
import { mockEmployeeList, mockWarehouseList, mockSuppliers, mockWarehouseLocations } from '@mock/shared/list.mock';

/**
 * Service xử lý tương tác giữa component và use case
 * Tuân thủ nguyên tắc Clean Architecture
 */
@Injectable()
export class GoodsReceiptService {
  // Mock data
  employeeList = mockEmployeeList;
  warehouseList = mockWarehouseList;
  supplierList = mockSuppliers;
  warehouseLocations = mockWarehouseLocations;


  constructor(
    private fb: FormBuilder,
    private goodsReceiptUseCase: GoodsReceiptUseCase
  ) { }

  /**
   * Lấy thông tin phiếu nhập kho theo ID
   * @param id ID của phiếu nhập kho
   */
  getGoodsReceipt(id: string): Observable<GoodsReceipt | null> {
    return this.goodsReceiptUseCase.getGoodsReceipt(id);
  }

  /**
   * Tạo mới phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho
   */
  createGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt> {
    return this.goodsReceiptUseCase.createGoodsReceipt(goodsReceipt);
  }

  /**
   * Cập nhật phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho cần cập nhật
   */
  updateGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt> {
    return this.goodsReceiptUseCase.updateGoodsReceipt(goodsReceipt);
  }

  /**
   * Tính toán tổng tiền hàng
   * @param items Danh sách sản phẩm
   */
  calculateSubTotal(items: GoodsReceiptItem[]): number {
    return items.reduce((total, item) => total + item.total, 0);
  }

  /**
   * Tính toán tổng số tiền cần thanh toán
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalPayment(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptUseCase.calculateTotalPayment(goodsReceipt);
  }

  /**
   * Tính toán công nợ
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateDebt(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptUseCase.calculateDebt(goodsReceipt);
  }

  /**
   * Tính toán tổng chi phí
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalAdditionalCost(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptUseCase.calculateTotalAdditionalCost(goodsReceipt);
  }

  /**
   * Tính toán tổng thuế
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalTax(goodsReceipt: GoodsReceipt): number {
    return this.goodsReceiptUseCase.calculateTotalTax(goodsReceipt);
  }

  /**
   * Tạo form thông tin cơ bản
   */
  createBasicInfoForm(): FormGroup {
    return this.fb.group({
      employeeId: [null, Validators.required],
      receiptDate: [new Date(), Validators.required],
      warehouseId: [null, Validators.required],
      supplierId: [null, Validators.required],
      notes: ['', Validators.maxLength(500)]
    });
  }

  /**
   * Thiết lập autocomplete cho nhà cung cấp
   */
  setupSupplierAutocomplete(form: FormGroup): Observable<typeof this.supplierList> {
    return form.get('supplierId')!.valueChanges.pipe(
      startWith(''),
      map(value => {
        const name = typeof value === 'string' ? value : value?.name;
        return name ? this._filterSuppliers(name) : this.supplierList.slice();
      })
    );
  }

  /**
   * Lọc danh sách nhà cung cấp theo từ khóa
   */
  private _filterSuppliers(value: string): typeof this.supplierList {
    const filterValue = value.toLowerCase();
    return this.supplierList.filter(supplier =>
      supplier.name.toLowerCase().includes(filterValue) ||
      supplier.phoneNumber?.toLowerCase().includes(filterValue) ||
      supplier.email?.toLowerCase().includes(filterValue)
    );
  }

  /**
   * Hiển thị tên nhà cung cấp trong autocomplete
   */
  displaySupplierFn(supplier: Supplier | null): string {
    return supplier && supplier.name ? supplier.name : '';
  }

  /**
   * Xử lý khi chọn nhà cung cấp từ autocomplete
   */
  handleSupplierSelection(form: FormGroup, supplier: Supplier): void {
    form.get('supplierId')?.setValue(supplier._id);
  }

  /**
   * Lấy dữ liệu form để lưu
   */
  getFormData(form: FormGroup): Partial<GoodsReceipt> {
    return form.value;
  }


  /**
   * Lọc vị trí trong kho dựa trên kho hàng đã chọn
   * @param warehouseId ID của kho hàng
   */
  filterLocationsByWarehouse(warehouseId: string) {
    if (!warehouseId) return [];
    return this.warehouseLocations.filter(location => location.warehouse._id === warehouseId);
  }
}
