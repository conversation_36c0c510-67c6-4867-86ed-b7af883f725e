import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { GoodsReceiptRepository } from '@domain/repositories/warehouse/goods-receipt.repository';
import { GoodsReceipt, GoodsReceiptItem } from '@domain/entities/goods-receipt.entity';

// Mock data
import { mockEmployeeList, mockWarehouseList, mockSuppliers, mockWarehouseLocations } from '@mock/shared/list.mock';

/**
 * Implementation của GoodsReceiptRepository
 * Xử lý việc gọi API và tương tác với backend
 */
@Injectable()
export class GoodsReceiptRepositoryImpl implements GoodsReceiptRepository {
  private goodsReceiptSubject = new BehaviorSubject<GoodsReceipt | null>(null);
  public goodsReceipt$ = this.goodsReceiptSubject.asObservable();

  // Mock data
  employeeList = mockEmployeeList;
  warehouseList = mockWarehouseList;
  supplierList = mockSuppliers;
  warehouseLocations = mockWarehouseLocations;

  constructor(private http: HttpClient) {}

  /**
   * Lấy thông tin phiếu nhập kho theo ID
   * @param id ID của phiếu nhập kho
   */
  getGoodsReceipt(id: string): Observable<GoodsReceipt | null> {
    // TODO: Implement api call
    // return this.http.get<GoodsReceipt>(/api/warehouse/goods-receipts/);
    return of(null);
  }

  /**
   * Tạo mới phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho
   */
  createGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt> {
    // TODO: Implement api call
    // return this.http.post<GoodsReceipt>('/api/warehouse/goods-receipts', goodsReceipt);
    console.log('Creating goods receipt:', goodsReceipt);
    return of({...goodsReceipt, _id: 'mock-id-' + Date.now()}).pipe(
      tap(receipt => this.goodsReceiptSubject.next(receipt))
    );
  }

  /**
   * Cập nhật phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho cần cập nhật
   */
  updateGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt> {
    // TODO: Implement api call
    // return this.http.put<GoodsReceipt>(/api/warehouse/goods-receipts/, goodsReceipt);
    console.log('Updating goods receipt:', goodsReceipt);
    return of(goodsReceipt).pipe(
      tap(receipt => this.goodsReceiptSubject.next(receipt))
    );
  }

  /**
   * Tính toán tổng tiền hàng
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateSubTotal(goodsReceipt: GoodsReceipt): number {
    return goodsReceipt.items.reduce((total, item) => total + item.total, 0);
  }

  /**
   * Tính toán tổng số tiền cần thanh toán
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalPayment(goodsReceipt: GoodsReceipt): number {
    const { summary } = goodsReceipt;
    return summary.total;
  }

  /**
   * Tính toán công nợ
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateDebt(goodsReceipt: GoodsReceipt): number {
    const total = this.calculateTotalPayment(goodsReceipt);
    // Tính tổng số tiền đã thanh toán từ initialPayments
    const totalPaid = goodsReceipt.payment.initialPayments ?
      goodsReceipt.payment.initialPayments.reduce((sum, payment) => sum + payment.amount, 0) : 0;
    return total - totalPaid;
  }

  /**
   * Tính toán tổng chi phí
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalAdditionalCost(goodsReceipt: GoodsReceipt): number {
    if (!goodsReceipt.additionalCosts || goodsReceipt.additionalCosts.length === 0) {
      return 0;
    }

    return goodsReceipt.additionalCosts.reduce((total, cost) => {
      let amount = 0;
      if (cost.costValue.type === 'fixed') {
        amount = cost.costValue.value;
      } else {
        // Percentage
        amount = (cost.costValue.value * goodsReceipt.summary.subTotal) / 100;
      }
      // Add tax if exists
      if (cost.tax) {
        amount += cost.tax.amount;
      }
      return total + amount;
    }, 0);
  }

  /**
   * Tính toán tổng thuế
   * @param goodsReceipt Phiếu nhập kho
   */
  calculateTotalTax(goodsReceipt: GoodsReceipt): number {
    if (!goodsReceipt.taxes || goodsReceipt.taxes.length === 0) {
      return 0;
    }

    return goodsReceipt.taxes.reduce((total, tax) => total + tax.amount, 0);
  }
}
