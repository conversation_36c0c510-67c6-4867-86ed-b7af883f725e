<div class="app-main-box">
  <!-- begin: grid -->
  <div class="flex items-center justify-between p-4 customer-overview-block">
    <div class="flex items-center gap-2.5">
      <img alt="Profile" class="rounded-full size-12 shrink-0" src="https://keenthemes.com/static/metronic/tailwind/dist/assets/media/avatars/300-2.png">
      <div class="flex flex-col">
        <a class="text-base font-semibold text-gray-900 hover:text-primary-active" href="#">
          <PERSON><PERSON><PERSON><PERSON>
          <span class="badge badge-sm badge-success badge-outline">
            Ti<PERSON><PERSON> cận
          </span>
        </a>
        <div class="flex items-center gap-1.5 mt-0.5">
          <span class="text-sm text-gray-900">hiengmail.com</span> -
          <span class="text-sm text-gray-900">(84) 0953232323</span>
        </div>

        <div class="flex items-center">
          <button class="dropdown-toggle btn btn-sm btn-primary">
          <i class="ki-filled ki-users">
          </i>
          Connect
          </button>
          <button class="btn btn-sm btn-icon btn-light">
          <i class="ki-filled ki-messages">
          </i>
          </button>
          <div class="dropdown" data-dropdown="true" data-dropdown-placement="bottom-end" data-dropdown-placement-rtl="bottom-start" data-dropdown-trigger="click">
            <button class="dropdown-toggle btn btn-sm btn-icon btn-light">
            <i class="ki-filled ki-dots-vertical">
            </i>
            </button>
            <div class="dropdown-content menu-default w-full max-w-[220px]">
              <div class="menu-item" data-dropdown-dismiss="true">
                <button class="menu-link" data-modal-toggle="#share_profile_modal">
                <span class="menu-icon">
                <i class="ki-filled ki-coffee">
                </i>
                </span>
                <span class="menu-title">
                Share Profile
                </span>
                </button>
              </div>
              <div class="menu-item" data-dropdown-dismiss="true">
                <a class="menu-link" data-modal-toggle="#give_award_modal" href="#">
                <span class="menu-icon">
                <i class="ki-filled ki-award">
                </i>
                </span>
                <span class="menu-title">
                Give Award
                </span>
                </a>
              </div>
              <div class="menu-item" data-dropdown-dismiss="true">
                <button class="menu-link">
                <span class="menu-icon">
                <i class="ki-filled ki-chart-line">
                </i>
                </span>
                <span class="menu-title">
                Stay Updated
                </span>
                <label class="switch switch-sm">
                <input name="check" type="checkbox" value="1">
                </label>
                </button>
              </div>
              <div class="menu-item" data-dropdown-dismiss="true">
                <button class="menu-link" data-modal-toggle="#report_user_modal">
                <span class="menu-icon">
                <i class="ki-filled ki-information-2">
                </i>
                </span>
                <span class="menu-title">
                Report User
                </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="flex flex-col items-end gap-2">
      <div class="flex gap-4">
        <div class="card flex-col justify-between gap-6 h-full bg-cover rtl:bg-[left_top_-1.7rem] bg-[right_top_-1.7rem] bg-no-repeat channel-stats-bg">
          <div class="flex flex-col gap-1 py-4 px-5">
            <span class="text-3xl font-semibold text-gray-900">
            24k
            </span>
            <span class="text-2sm font-normal text-gray-700">
            Lessons Views
            </span>
          </div>
        </div>
        <div class="card flex-col justify-between gap-6 h-full bg-cover rtl:bg-[left_top_-1.7rem] bg-[right_top_-1.7rem] bg-no-repeat channel-stats-bg">
          <div class="flex flex-col gap-1 py-4 px-5">
            <span class="text-3xl font-semibold text-gray-900">
            24k
            </span>
            <span class="text-2sm font-normal text-gray-700">
            Lessons Views
            </span>
          </div>
        </div>
        <div class="card flex-col justify-between gap-6 h-full bg-cover rtl:bg-[left_top_-1.7rem] bg-[right_top_-1.7rem] bg-no-repeat channel-stats-bg">
          <div class="flex flex-col gap-1 py-4 px-5">
            <span class="text-3xl font-semibold text-gray-900">
            24k
            </span>
            <span class="text-2sm font-normal text-gray-700">
            Lessons Views
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- end: grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5">
    <div class="col-span-1 table-border-r">
      <div class="grid gap-5 lg:gap-7.5">
        <div class="card border-0 rounded-0 shadow-none">
          <div class="card-header">
            <h3 class="card-title">
              General Info
            </h3>
          </div>
          <div class="card-body pt-3.5 pb-3.5">
            <table class="table-auto">
              <tbody>
                <tr>
                  <td class="text-sm text-gray-600 pb-3 pe-4 lg:pe-8">
                    Phone:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    +31 6 12345678
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3 pe-4 lg:pe-8">
                    Email:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    jennystudio.com
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3 pe-4 lg:pe-8">
                    Status:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    <span class="badge badge-sm badge-success badge-outline">
                    Subscribed
                    </span>
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3 pe-4 lg:pe-8">
                    Type:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    Wholesale
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3 pe-4 lg:pe-8">
                    Encryption:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    Strong
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3 pe-4 lg:pe-8">
                    Last Order:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    Today at 13:06
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3 pe-4 lg:pe-8">
                    Signed Up:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    2 months ago
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="card border-0 rounded-0 shadow-none">
          <div class="card-header">
            <h3 class="card-title">
              Attributes
            </h3>
          </div>
          <div class="card-body pt-3.5 pb-1">
            <table class="table-auto">
              <tbody>
                <tr>
                  <td class="text-sm text-gray-600 pb-3.5 pe-4 lg:pe-6">
                    customer_id:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    CUST567
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3.5 pe-4 lg:pe-6">
                    c_name:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    jenny
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3.5 pe-4 lg:pe-6">
                    license_id:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    LIC123
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3.5 pe-4 lg:pe-6">
                    log_id:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    CUST567
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3.5 pe-4 lg:pe-6">
                    resv_code:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    CS345
                  </td>
                </tr>
                <tr>
                  <td class="text-sm text-gray-600 pb-3.5 pe-4 lg:pe-6">
                    orders_io:
                  </td>
                  <td class="text-sm text-gray-900 pb-3">
                    JENNYTIME
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="card-footer justify-center">
            <a class="btn btn-link" href="/metronic/tailwind/demo1/network/user-table/store-clients">
            All Attributes
            </a>
          </div>
        </div>
        <div class="card border-0 rounded-0 shadow-none">
          <div class="card-header">
            <h3 class="card-title">
              Skills
            </h3>
          </div>
          <div class="card-body">
            <div class="flex flex-wrap gap-2.5 mb-2">
              <span class="badge badge-sm badge-gray-200">
              Web Design
              </span>
              <span class="badge badge-sm badge-gray-200">
              Code Review
              </span>
              <span class="badge badge-sm badge-gray-200">
              Figma
              </span>
              <span class="badge badge-sm badge-gray-200">
              Product Development
              </span>
              <span class="badge badge-sm badge-gray-200">
              Webflow
              </span>
              <span class="badge badge-sm badge-gray-200">
              AI
              </span>
              <span class="badge badge-sm badge-gray-200">
              noCode
              </span>
              <span class="badge badge-sm badge-gray-200">
              Management
              </span>
            </div>
          </div>
        </div>
        <div class="card border-0 rounded-0 shadow-none">
          <div class="card-header gap-2">
            <h3 class="card-title">
              Contributors
            </h3>
            <div class="menu" data-menu="true">
              <div class="menu-item menu-item-dropdown" data-menu-item-offset="0, 10px" data-menu-item-placement="bottom-end" data-menu-item-placement-rtl="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:click">
                <button class="menu-toggle btn btn-sm btn-icon btn-light btn-clear">
                <i class="ki-filled ki-dots-vertical">
                </i>
                </button>
                <div class="menu-dropdown menu-default w-full max-w-[200px]" data-menu-dismiss="true">
                  <div class="menu-item">
                    <a class="menu-link" href="/metronic/tailwind/demo1/account/activity">
                    <span class="menu-icon">
                    <i class="ki-filled ki-cloud-change">
                    </i>
                    </span>
                    <span class="menu-title">
                    Activity
                    </span>
                    </a>
                  </div>
                  <div class="menu-item">
                    <a class="menu-link" data-modal-toggle="#share_profile_modal" href="#">
                    <span class="menu-icon">
                    <i class="ki-filled ki-share">
                    </i>
                    </span>
                    <span class="menu-title">
                    Share
                    </span>
                    </a>
                  </div>
                  <div class="menu-item menu-item-dropdown" data-menu-item-offset="-15px, 0" data-menu-item-placement="right-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:hover">
                    <div class="menu-link">
                      <span class="menu-icon">
                      <i class="ki-filled ki-notification-status">
                      </i>
                      </span>
                      <span class="menu-title">
                      Notifications
                      </span>
                      <span class="menu-arrow">
                      <i class="ki-filled ki-right text-3xs rtl:transform rtl:rotate-180">
                      </i>
                      </span>
                    </div>
                    <div class="menu-dropdown menu-default w-full max-w-[175px]">
                      <div class="menu-item">
                        <a class="menu-link" href="/metronic/tailwind/demo1/account/home/<USER>">
                        <span class="menu-icon">
                        <i class="ki-filled ki-sms">
                        </i>
                        </span>
                        <span class="menu-title">
                        Email
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" href="/metronic/tailwind/demo1/account/home/<USER>">
                        <span class="menu-icon">
                        <i class="ki-filled ki-message-notify">
                        </i>
                        </span>
                        <span class="menu-title">
                        SMS
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" href="/metronic/tailwind/demo1/account/home/<USER>">
                        <span class="menu-icon">
                        <i class="ki-filled ki-notification-status">
                        </i>
                        </span>
                        <span class="menu-title">
                        Push
                        </span>
                        </a>
                      </div>
                    </div>
                  </div>
                  <div class="menu-item">
                    <a class="menu-link" data-modal-toggle="#report_user_modal" href="#">
                    <span class="menu-icon">
                    <i class="ki-filled ki-dislike">
                    </i>
                    </span>
                    <span class="menu-title">
                    Report
                    </span>
                    </a>
                  </div>
                  <div class="menu-separator">
                  </div>
                  <div class="menu-item">
                    <a class="menu-link" href="/metronic/tailwind/demo1/account/home/<USER>">
                    <span class="menu-icon">
                    <i class="ki-filled ki-setting-3">
                    </i>
                    </span>
                    <span class="menu-title">
                    Settings
                    </span>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card-body">
            <div class="flex flex-col gap-2 lg:gap-5">
              <div class="flex items-center gap-2">
                <div class="flex items-center grow gap-2.5">
                  <img alt="" class="rounded-full size-9 shrink-0" src="/static/metronic/tailwind/dist/assets/media/avatars/300-3.png">
                  <div class="flex flex-col">
                    <a class="text-sm font-semibold text-gray-900 hover:text-primary-active mb-px" href="#">
                    Tyler Hero
                    </a>
                    <span class="text-xs font-semibold text-gray-600">
                    6 contributors
                    </span>
                  </div>
                </div>
                <div class="menu" data-menu="true">
                  <div class="menu-item menu-item-dropdown" data-menu-item-offset="0, 10px" data-menu-item-placement="bottom-end" data-menu-item-placement-rtl="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:click">
                    <button class="menu-toggle btn btn-sm btn-icon btn-light btn-clear">
                    <i class="ki-filled ki-dots-vertical">
                    </i>
                    </button>
                    <div class="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-document">
                        </i>
                        </span>
                        <span class="menu-title">
                        Details
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" data-modal-toggle="#share_profile_modal" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-share">
                        </i>
                        </span>
                        <span class="menu-title">
                        Share
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-file-up">
                        </i>
                        </span>
                        <span class="menu-title">
                        Export
                        </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <div class="flex items-center grow gap-2.5">
                  <img alt="" class="rounded-full size-9 shrink-0" src="/static/metronic/tailwind/dist/assets/media/avatars/300-1.png">
                  <div class="flex flex-col">
                    <a class="text-sm font-semibold text-gray-900 hover:text-primary-active mb-px" href="#">
                    Esther Howard
                    </a>
                    <span class="text-xs font-semibold text-gray-600">
                    29 contributors
                    </span>
                  </div>
                </div>
                <div class="menu" data-menu="true">
                  <div class="menu-item menu-item-dropdown" data-menu-item-offset="0, 10px" data-menu-item-placement="bottom-end" data-menu-item-placement-rtl="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:click">
                    <button class="menu-toggle btn btn-sm btn-icon btn-light btn-clear">
                    <i class="ki-filled ki-dots-vertical">
                    </i>
                    </button>
                    <div class="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-document">
                        </i>
                        </span>
                        <span class="menu-title">
                        Details
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" data-modal-toggle="#share_profile_modal" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-share">
                        </i>
                        </span>
                        <span class="menu-title">
                        Share
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-file-up">
                        </i>
                        </span>
                        <span class="menu-title">
                        Export
                        </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <div class="flex items-center grow gap-2.5">
                  <img alt="" class="rounded-full size-9 shrink-0" src="/static/metronic/tailwind/dist/assets/media/avatars/300-14.png">
                  <div class="flex flex-col">
                    <a class="text-sm font-semibold text-gray-900 hover:text-primary-active mb-px" href="#">
                    Cody Fisher
                    </a>
                    <span class="text-xs font-semibold text-gray-600">
                    34 contributors
                    </span>
                  </div>
                </div>
                <div class="menu" data-menu="true">
                  <div class="menu-item menu-item-dropdown" data-menu-item-offset="0, 10px" data-menu-item-placement="bottom-end" data-menu-item-placement-rtl="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:click">
                    <button class="menu-toggle btn btn-sm btn-icon btn-light btn-clear">
                    <i class="ki-filled ki-dots-vertical">
                    </i>
                    </button>
                    <div class="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-document">
                        </i>
                        </span>
                        <span class="menu-title">
                        Details
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" data-modal-toggle="#share_profile_modal" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-share">
                        </i>
                        </span>
                        <span class="menu-title">
                        Share
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-file-up">
                        </i>
                        </span>
                        <span class="menu-title">
                        Export
                        </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <div class="flex items-center grow gap-2.5">
                  <img alt="" class="rounded-full size-9 shrink-0" src="/static/metronic/tailwind/dist/assets/media/avatars/300-7.png">
                  <div class="flex flex-col">
                    <a class="text-sm font-semibold text-gray-900 hover:text-primary-active mb-px" href="#">
                    Arlene McCoy
                    </a>
                    <span class="text-xs font-semibold text-gray-600">
                    1 contributors
                    </span>
                  </div>
                </div>
                <div class="menu" data-menu="true">
                  <div class="menu-item menu-item-dropdown" data-menu-item-offset="0, 10px" data-menu-item-placement="bottom-end" data-menu-item-placement-rtl="bottom-start" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:click">
                    <button class="menu-toggle btn btn-sm btn-icon btn-light btn-clear">
                    <i class="ki-filled ki-dots-vertical">
                    </i>
                    </button>
                    <div class="menu-dropdown menu-default w-full max-w-[175px]" data-menu-dismiss="true">
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-document">
                        </i>
                        </span>
                        <span class="menu-title">
                        Details
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" data-modal-toggle="#share_profile_modal" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-share">
                        </i>
                        </span>
                        <span class="menu-title">
                        Share
                        </span>
                        </a>
                      </div>
                      <div class="menu-item">
                        <a class="menu-link" href="#">
                        <span class="menu-icon">
                        <i class="ki-filled ki-file-up">
                        </i>
                        </span>
                        <span class="menu-title">
                        Export
                        </span>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card-footer justify-center">
            <a class="btn btn-link" href="/metronic/tailwind/demo1/public-profile/network">
            All Contributors
            </a>
          </div>
        </div>
      </div>
    </div>
    <div class="col-span-2">
      <div class="flex flex-col gap-5 lg:gap-7.5 mt-4">
        <div class="flex flex-col gap-5 lg:gap-7.5">
          <div class="card">
            <div class="card-header pb-0" style="min-height: 0;">
              <div class="menu gap-3" data-menu="true">
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary here" data-menu-item-overflow="true" data-menu-item-placement="bottom-start" data-menu-item-placement-rtl="bottom-end" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:hover">
                  <div class="menu-link gap-1.5 pb-2 lg:pb-4 px-2">
                    <span class="menu-title text-nowrap text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-medium menu-item-here:text-primary menu-item-here:font-medium menu-item-show:text-primary menu-link-hover:text-primary">
                    Nhật ký
                    </span>
                    <span class="menu-arrow">
                    <i class="ki-filled ki-down text-2xs text-gray-500 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary">
                    </i>
                    </span>
                  </div>
                </div>
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary" data-menu-item-overflow="true" data-menu-item-placement="bottom-start" data-menu-item-placement-rtl="bottom-end" data-menu-item-toggle="dropdown" data-menu-item-trigger="click|lg:hover">
                  <div class="menu-link gap-1.5 pb-2 lg:pb-4 px-2">
                    <span class="menu-title text-nowrap text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-medium menu-item-here:text-primary menu-item-here:font-medium menu-item-show:text-primary menu-link-hover:text-primary">
                    Giao dịch
                    </span>
                    <span class="menu-arrow">
                    <i class="ki-filled ki-down text-2xs text-gray-500 menu-item-active:text-primary menu-item-here:text-primary menu-item-show:text-primary menu-link-hover:text-primary">
                    </i>
                    </span>
                  </div>
                  <div class="menu-dropdown menu-default py-2 min-w-[200px]" style="">
                    <div class="menu-item">
                      <a class="menu-link" href="/metronic/tailwind/demo1/public-profile/projects/3-columns" tabindex="0">
                      <span class="menu-title">
                      3 Columns
                      </span>
                      </a>
                    </div>
                    <div class="menu-item">
                      <a class="menu-link" href="/metronic/tailwind/demo1/public-profile/projects/2-columns" tabindex="0">
                      <span class="menu-title">
                      2 Columns
                      </span>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary">
                  <a class="menu-link gap-1.5 pb-2 lg:pb-4 px-2" href="/metronic/tailwind/demo1/public-profile/works">
                  <span class="menu-title text-nowrap font-medium text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-semibold menu-item-here:text-primary menu-item-here:font-semibold menu-item-show:text-primary menu-link-hover:text-primary">
                  Phản hồi
                  </span>
                  </a>
                </div>
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary">
                  <a class="menu-link gap-1.5 pb-2 lg:pb-4 px-2" href="/metronic/tailwind/demo1/public-profile/teams">
                  <span class="menu-title text-nowrap font-medium text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-semibold menu-item-here:text-primary menu-item-here:font-semibold menu-item-show:text-primary menu-link-hover:text-primary">
                  Lịch hẹn
                  </span>
                  </a>
                </div>
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary">
                  <a class="menu-link gap-1.5 pb-2 lg:pb-4 px-2" href="/metronic/tailwind/demo1/public-profile/network">
                  <span class="menu-title text-nowrap font-medium text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-semibold menu-item-here:text-primary menu-item-here:font-semibold menu-item-show:text-primary menu-link-hover:text-primary">
                  Cơ hội
                  </span>
                  </a>
                </div>
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary">
                  <a class="menu-link gap-1.5 pb-2 lg:pb-4 px-2" href="/metronic/tailwind/demo1/public-profile/activity">
                  <span class="menu-title text-nowrap font-medium text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-semibold menu-item-here:text-primary menu-item-here:font-semibold menu-item-show:text-primary menu-link-hover:text-primary">
                  Lịch đi tuyến
                  </span>
                  </a>
                </div>
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary">
                  <a class="menu-link gap-1.5 pb-2 lg:pb-4 px-2" href="/metronic/tailwind/demo1/public-profile/activity">
                  <span class="menu-title text-nowrap font-medium text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-semibold menu-item-here:text-primary menu-item-here:font-semibold menu-item-show:text-primary menu-link-hover:text-primary">
                  Automation
                  </span>
                  </a>
                </div>
                <div class="menu-item border-b-2 border-b-transparent menu-item-active:border-b-primary menu-item-here:border-b-primary">
                  <a class="menu-link gap-1.5 pb-2 lg:pb-4 px-2" href="/metronic/tailwind/demo1/public-profile/activity">
                  <span class="menu-title text-nowrap font-medium text-sm text-gray-700 menu-item-active:text-primary menu-item-active:font-semibold menu-item-here:text-primary menu-item-here:font-semibold menu-item-show:text-primary menu-link-hover:text-primary">
                  Giới thiệu
                  </span>
                  </a>
                </div>
              </div>
            </div>

            <div class="card-body">
              <div class="flex flex-col">
                <div class="flex items-start relative">
                  <div class="w-9 start-0 top-9 absolute bottom-0 rtl:-translate-x-1/2 translate-x-1/2 border-s border-s-gray-300">
                  </div>
                  <div class="flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-300 size-9 text-gray-600">
                    <i class="ki-filled ki-people text-base">
                    </i>
                  </div>
                  <div class="ps-2.5 mb-7 text-md grow">
                    <div class="flex flex-col">
                      <div class="text-sm text-gray-800">
                        Jenny sent an
                        <a class="text-sm link" href="#">
                        inquiry
                        </a>
                        about a
                        <a class="text-sm link" href="#">
                        new product
                        </a>
                        .
                      </div>
                      <span class="text-xs text-gray-600">
                      Today, 9:00 AM
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex items-start relative">
                  <div class="w-9 start-0 top-9 absolute bottom-0 rtl:-translate-x-1/2 translate-x-1/2 border-s border-s-gray-300">
                  </div>
                  <div class="flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-300 size-9 text-gray-600">
                    <i class="ki-filled ki-calendar-tick text-base">
                    </i>
                  </div>
                  <div class="ps-2.5 mb-7 text-md grow">
                    <div class="flex flex-col pb-2.5">
                      <span class="text-sm text-gray-800">
                      Jenny attended a webinar on new product features.
                      </span>
                      <span class="text-xs text-gray-600">
                      3 days ago, 11:45 AM
                      </span>
                    </div>
                    <div class="card shadow-none p-4">
                      <div class="flex flex-wrap gap-2.5">
                        <i class="ki-filled ki-code text-lg text-info">
                        </i>
                        <div class="flex flex-col gap-5 grow">
                          <div class="flex flex-wrap items-center justify-between">
                            <div class="flex flex-col gap-0.5">
                              <span class="text-md font-medium text-gray-900 cursor-pointer hover:text-primary mb-px">
                              Leadership Development Series: Part 1
                              </span>
                              <span class="text-xs text-gray-600">
                              The first installment of a leadership development series.
                              </span>
                            </div>
                            <a class="btn btn-link" href="/metronic/tailwind/demo1/account/members/teams">
                            View
                            </a>
                          </div>
                          <div class="flex flex-wrap gap-7.5">
                            <div class="flex items-center gap-1.5">
                              <span class="text-2sm font-medium text-gray-600">
                              Code:
                              </span>
                              <a class="text-2sm text-primary" href="#">
                              #leaderdev-1
                              </a>
                            </div>
                            <div class="flex items-center gap-1.5">
                              <span class="text-2sm text-gray-600">
                              Progress:
                              </span>
                              <div class="progress progress-success min-w-[120px]">
                                <div class="progress-bar" style="width: 80%">
                                </div>
                              </div>
                            </div>
                            <div class="flex items-center gap-1.5 lg:min-w-24 shrink-0 max-w-auto">
                              <span class="text-2sm text-gray-600">
                              Guests:
                              </span>
                              <div class="flex -space-x-2">
                                <div class="flex">
                                  <img class="hover:z-5 relative shrink-0 rounded-full ring-1 ring-light-light size-7" src="/static/metronic/tailwind/dist/assets/media/avatars/300-4.png">
                                </div>
                                <div class="flex">
                                  <img class="hover:z-5 relative shrink-0 rounded-full ring-1 ring-light-light size-7" src="/static/metronic/tailwind/dist/assets/media/avatars/300-1.png">
                                </div>
                                <div class="flex">
                                  <img class="hover:z-5 relative shrink-0 rounded-full ring-1 ring-light-light size-7" src="/static/metronic/tailwind/dist/assets/media/avatars/300-2.png">
                                </div>
                                <div class="flex">
                                  <span class="hover:z-5 relative inline-flex items-center justify-center shrink-0 rounded-full ring-1 font-semibold leading-none text-3xs size-7 text-primary-inverse ring-primary-light bg-primary">
                                  +24
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-start relative">
                  <div class="w-9 start-0 top-9 absolute bottom-0 rtl:-translate-x-1/2 translate-x-1/2 border-s border-s-gray-300">
                  </div>
                  <div class="flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-300 size-9 text-gray-600">
                    <i class="ki-filled ki-entrance-left text-base">
                    </i>
                  </div>
                  <div class="ps-2.5 mb-7 text-md grow">
                    <div class="flex flex-col">
                      <div class="text-sm text-gray-800">
                        Jenny's last login to the
                        <a class="text-sm link" href="#">
                        Customer Portal
                        </a>
                        .
                      </div>
                      <span class="text-xs text-gray-600">
                      5 days ago, 4:07 PM
                      </span>
                    </div>
                  </div>
                </div>
                <div class="flex items-start relative">
                  <div class="w-9 start-0 top-9 absolute bottom-0 rtl:-translate-x-1/2 translate-x-1/2 border-s border-s-gray-300">
                  </div>
                  <div class="flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-300 size-9 text-gray-600">
                    <i class="ki-filled ki-directbox-default text-base">
                    </i>
                  </div>
                  <div class="ps-2.5 mb-7 text-md grow">
                    <div class="flex flex-col pb-2.5">
                      <span class="text-sm text-gray-800">
                      Email campaign sent to Jenny for a special promotion.
                      </span>
                      <span class="text-xs text-gray-600">
                      1 week ago, 11:45 AM
                      </span>
                    </div>
                    <div class="card shadow-none">
                      <div class="card-body lg:py-4">
                        <div class="flex justify-center">
                        </div>
                        <div class="flex flex-col gap-1">
                          <div class="text-md font-medium text-gray-900 text-center">
                            First Campaign Created
                          </div>
                          <div class="flex items-center justify-center gap-1">
                            <a class="text-2sm font-semibold link" href="/metronic/tailwind/demo1/public-profile/profiles/company">
                            Axio new release
                            </a>
                            <span class="text-2sm text-gray-700 me-2">
                            email campaign
                            </span>
                            <span class="badge badge-sm badge-success badge-outline">
                            Public
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="flex items-start relative">
                  <div class="flex items-center justify-center shrink-0 rounded-full bg-gray-100 border border-gray-300 size-9 text-gray-600">
                    <i class="ki-filled ki-rocket text-base">
                    </i>
                  </div>
                  <div class="ps-2.5 text-md grow">
                    <div class="flex flex-col">
                      <div class="text-sm text-gray-800">
                        Explored niche demo ideas for product-specific solutions.
                      </div>
                      <span class="text-xs text-gray-600">
                      3 weeks ago, 4:07 PM
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-footer justify-center">
              <a class="btn btn-link" href="/metronic/tailwind/demo1/public-profile/activity">
              All-time Activities
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- end: grid -->
</div>