import { Routes } from '@angular/router';
import { RouterResolverService } from '@core/services/router.resolver.service';
import { canActivateCashier } from '@core/guards/auth.guard';
import { ROUTER_LINKS } from 'salehub_shared_contracts';

export const ProductRoutes: Routes = [
  {
    path: ROUTER_LINKS.product.create,
    loadComponent: () => import('./product-form/components/product-form.component')
          .then((m) => m.ProductFormComponent)
  },
  {
    path: ROUTER_LINKS.product.edit,
    loadComponent: () => import('./product-form/components/product-form.component')
          .then((m) => m.ProductFormComponent)
  },
  {
    path: ROUTER_LINKS.product.list,
    loadComponent: () => import('./product-list/product-list.component')
      .then((m) => m.ProductListComponent),
  },
  {
    path: ROUTER_LINKS.product.layouts,
    loadComponent: () => import('./product-layout/product-layout.component')
          .then((m) => m.ProductLayoutComponent),
  },
  {
    path: ROUTER_LINKS.product.layouts_edit,
    loadComponent: () => import('./product-layout-edit/product-layout-edit.component')
          .then((m) => m.ProductLayoutEditComponent),
  }
];
