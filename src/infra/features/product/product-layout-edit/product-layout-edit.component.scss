// Product Layout Edit Component Styles
// Full height layout với toolbar và content area





.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .text-center {
    max-width: 300px;
  }

  mat-spinner {
    margin: 0 auto;
  }
}

.error-container {
  flex: 1;
  display: flex;
  align-items: center;

  .alert {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.15);

    mat-icon.display-4 {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
    }
  }
}


.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .display-1 {
    font-size: 4rem;
    opacity: 0.3;
  }
}
