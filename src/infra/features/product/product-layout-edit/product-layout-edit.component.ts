import { Component, OnInit, OnDestroy, signal, computed, inject, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

// Material imports
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

// Shared components
import { DynamicLayoutBuilderComponent } from '@shared/components/dynamic-layout-builder/dynamic-layout-builder.component';
import { DynamicLayoutBuilderService } from '@shared/components/dynamic-layout-builder/dynamic-layout-builder.service';

// Models và Services
import { Template, Section, LayoutField } from '@shared/models/view/dynamic-layout-builder.model';
import { ProductLayoutService } from '../product-layout/product-layout.service';

/**
 * ProductLayoutEditComponent - Component chỉnh sửa layout sản phẩm
 *
 * Tính năng:
 * - Load template từ route parameter (index)
 * - Sử dụng DynamicLayoutBuilderComponent để chỉnh sửa
 * - Navigation back đến danh sách
 * - Error handling và loading states
 * - Sử dụng Angular 19 Signals và OnPush change detection
 */
@Component({
  selector: 'app-product-layout-edit',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatIconModule,
    MatToolbarModule,
    MatProgressSpinnerModule,
    DynamicLayoutBuilderComponent
  ],
  templateUrl: './product-layout-edit.component.html',
  styleUrls: ['./product-layout-edit.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProductLayoutEditComponent implements OnInit, OnDestroy {
  // Inject services
  private route = inject(ActivatedRoute);
  private router = inject(Router);
  private productLayoutService = inject(ProductLayoutService);
  private dynamicLayoutBuilderService = inject(DynamicLayoutBuilderService);
  private subscriptions = new Subscription();

  // Signals cho state management
  currentTemplate = signal<Template | null>(null);
  templateIndex = signal<number | null>(null);
  isLoading = signal<boolean>(false);
  error = signal<string | null>(null);

  // Computed signals
  hasTemplate = computed(() => !!this.currentTemplate());
  templateName = computed(() => this.currentTemplate()?.name || '');
  sections = computed(() => this.currentTemplate()?.sections || []);

  ngOnInit(): void {
    this.loadTemplateFromRoute();
    this.subscribeToLayoutChanges();
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }

  /**
   * Subscribe vào layout changes từ DynamicLayoutBuilderService
   */
  private subscribeToLayoutChanges(): void {
    const layoutSub = this.dynamicLayoutBuilderService.layoutChanged$.subscribe(
      (updatedSections: Section[]) => {
        const currentTemplate = this.currentTemplate();
        if (currentTemplate) {
          const updatedTemplate: Template = {
            ...currentTemplate,
            sections: updatedSections
          };
          this.currentTemplate.set(updatedTemplate);
          console.log('Layout updated from service:', updatedTemplate);
        }
      }
    );
    this.subscriptions.add(layoutSub);
  }

  /**
   * Load template dựa trên route parameter
   */
  private loadTemplateFromRoute(): void {
    const routeSub = this.route.params.subscribe(params => {
      const indexParam = params['index'];

      if (indexParam !== undefined) {
        const index = parseInt(indexParam, 10);

        if (!isNaN(index)) {
          this.loadTemplate(index);
        } else {
          this.handleInvalidIndex();
        }
      } else {
        // Nếu không có index, load template đầu tiên
        this.loadTemplate(0);
      }
    });

    this.subscriptions.add(routeSub);
  }

  /**
   * Load template theo index
   * @param index Vị trí template trong mảng
   */
  private loadTemplate(index: number): void {
    try {
      this.isLoading.set(true);
      this.error.set(null);
      this.templateIndex.set(index);

      // Simulate loading delay
      setTimeout(() => {
        const template = this.productLayoutService.getProductLayoutByIndex(index);

        if (template) {
          this.currentTemplate.set(template);
          // Khởi tạo DynamicLayoutBuilderService với sections từ template
          this.dynamicLayoutBuilderService.updateSections(template.sections);
          this.isLoading.set(false);
        } else {
          this.handleTemplateNotFound(index);
        }
      }, 300);

    } catch (err) {
      this.handleLoadError(err);
    }
  }

  /**
   * Xử lý khi index không hợp lệ
   */
  private handleInvalidIndex(): void {
    this.error.set('Chỉ số template không hợp lệ.');
    this.isLoading.set(false);
    console.error('Invalid template index in route');
  }

  /**
   * Xử lý khi không tìm thấy template
   * @param index Index đã tìm
   */
  private handleTemplateNotFound(index: number): void {
    this.error.set(`Không tìm thấy template tại vị trí ${index}.`);
    this.isLoading.set(false);
    console.error(`Template not found at index: ${index}`);
  }

  /**
   * Xử lý lỗi khi load template
   * @param err Error object
   */
  private handleLoadError(err: unknown): void {
    this.error.set('Không thể tải template. Vui lòng thử lại.');
    this.isLoading.set(false);
    console.error('Error loading template:', err);
  }

  /**
   * Điều hướng về trang danh sách layout
   */
  onBackToList(): void {
    try {
      this.router.navigate(['/product/layouts']);
    } catch (err) {
      console.error('Navigation error:', err);
    }
  }

  /**
   * Xử lý retry khi có lỗi
   */
  onRetry(): void {
    const index = this.templateIndex();
    if (index !== null) {
      this.loadTemplate(index);
    } else {
      this.loadTemplate(0);
    }
  }



  /**
   * Lấy thống kê template hiện tại
   */
  getCurrentTemplateStats() {
    const template = this.currentTemplate();
    if (template) {
      return this.productLayoutService.getTemplateStats(template);
    }
    return {
      sectionCount: 0,
      fieldCount: 0,
      requiredFieldCount: 0,
      optionalFieldCount: 0
    };
  }

  /**
   * Kiểm tra template có thay đổi không
   * @returns true nếu có thay đổi
   */
  hasUnsavedChanges(): boolean {
    // TODO: Implement change detection logic
    return false;
  }

  /**
   * Lưu template (placeholder cho tương lai)
   */
  onSaveTemplate(): void {
    const template = this.currentTemplate();
    if (template) {
      console.log('Saving template:', template);
      // TODO: Implement save API call
    }
  }
}
