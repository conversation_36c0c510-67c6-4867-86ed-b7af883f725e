<!-- Header Section -->
<div class="container-fluid py-4">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h1 class="h3 mb-1">{{ 'PRODUCT_LAYOUT.TITLE' | translate }}</h1>
          <p class="text-muted mb-0">
            {{ 'PRODUCT_LAYOUT.SUBTITLE' | translate }}
            <span class="badge bg-primary ms-2">{{ templateCount() }}</span>
          </p>
        </div>
        
        <!-- Action buttons -->
        <div class="d-flex gap-2">
          <button 
            mat-raised-button 
            color="primary"
            (click)="onRetry()"
            [disabled]="isLoading()"
            [attr.aria-label]="'PRODUCT_LAYOUT.REFRESH' | translate">
            <mat-icon>refresh</mat-icon>
            {{ 'PRODUCT_LAYOUT.REFRESH' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading()" class="row">
    <div class="col-12">
      <div class="d-flex justify-content-center align-items-center py-5">
        <div class="text-center">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="visually-hidden">{{ 'COMMON.LOADING' | translate }}</span>
          </div>
          <p class="text-muted">{{ 'PRODUCT_LAYOUT.LOADING' | translate }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error() && !isLoading()" class="row">
    <div class="col-12">
      <div class="alert alert-danger d-flex align-items-center" role="alert">
        <mat-icon class="me-2">error</mat-icon>
        <div class="flex-grow-1">
          {{ error() }}
        </div>
        <button 
          mat-button 
          color="primary" 
          (click)="onRetry()"
          class="ms-2">
          {{ 'COMMON.RETRY' | translate }}
        </button>
      </div>
    </div>
  </div>

  <!-- Templates Grid -->
  <div *ngIf="hasTemplates() && !isLoading() && !error()" class="row">
    <div class="col-12">
      <!-- Grid Container với CSS Grid và Bootstrap responsive -->
      <div class="template-grid">
        <mat-card 
          *ngFor="let template of templates(); trackBy: trackByTemplate; let i = index"
          class="template-card h-100 cursor-pointer"
          (click)="onTemplateClick(i)"
          [attr.aria-label]="'PRODUCT_LAYOUT.CARD_ARIA_LABEL' | translate: {name: template.name}"
          tabindex="0"
          (keydown.enter)="onTemplateClick(i)"
          (keydown.space)="onTemplateClick(i)">
          
          <mat-card-header class="pb-2">
            <div mat-card-avatar class="template-avatar">
              <mat-icon [class]="'text-' + getTemplateChipColor(template.name)">
                {{ getTemplateIcon(template.name) }}
              </mat-icon>
            </div>
            <mat-card-title class="template-title">
              {{ template.name }}
            </mat-card-title>
            <mat-card-subtitle>
              {{ 'PRODUCT_LAYOUT.TEMPLATE_SUBTITLE' | translate }}
            </mat-card-subtitle>
          </mat-card-header>

          <mat-card-content class="flex-grow-1">
            <!-- Statistics -->
            <div class="stats-container mb-3">
              <div class="row g-2">
                <div class="col-6">
                  <div class="stat-item text-center p-2 bg-light rounded">
                    <div class="stat-number text-primary fw-bold">
                      {{ getSectionCount(template) }}
                    </div>
                    <div class="stat-label small text-muted">
                      {{ 'PRODUCT_LAYOUT.SECTIONS' | translate }}
                    </div>
                  </div>
                </div>
                <div class="col-6">
                  <div class="stat-item text-center p-2 bg-light rounded">
                    <div class="stat-number text-success fw-bold">
                      {{ getFieldCount(template) }}
                    </div>
                    <div class="stat-label small text-muted">
                      {{ 'PRODUCT_LAYOUT.FIELDS' | translate }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Template Details -->
            <div class="template-details">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="small text-muted">{{ 'PRODUCT_LAYOUT.REQUIRED_FIELDS' | translate }}:</span>
                <mat-chip [color]="getTemplateChipColor(template.name)" selected>
                  {{ getTemplateStats(template).requiredFieldCount }}
                </mat-chip>
              </div>
              <div class="d-flex justify-content-between align-items-center">
                <span class="small text-muted">{{ 'PRODUCT_LAYOUT.OPTIONAL_FIELDS' | translate }}:</span>
                <mat-chip color="primary">
                  {{ getTemplateStats(template).optionalFieldCount }}
                </mat-chip>
              </div>
            </div>
          </mat-card-content>

          <mat-card-actions class="d-flex justify-content-between align-items-center">
            <div class="template-category">
              <mat-chip 
                [color]="getTemplateChipColor(template.name)" 
                selected
                class="small">
                {{ template.name.split(' ')[0] }}
              </mat-chip>
            </div>
            <button 
              mat-icon-button 
              color="primary"
              [attr.aria-label]="'PRODUCT_LAYOUT.EDIT_TEMPLATE' | translate: {name: template.name}"
              (click)="$event.stopPropagation(); onTemplateClick(i)">
              <mat-icon>edit</mat-icon>
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!hasTemplates() && !isLoading() && !error()" class="row">
    <div class="col-12">
      <div class="text-center py-5">
        <mat-icon class="display-1 text-muted mb-3">view_module</mat-icon>
        <h3 class="text-muted">{{ 'PRODUCT_LAYOUT.NO_TEMPLATES' | translate }}</h3>
        <p class="text-muted">{{ 'PRODUCT_LAYOUT.NO_TEMPLATES_DESC' | translate }}</p>
        <button 
          mat-raised-button 
          color="primary" 
          (click)="onRetry()">
          <mat-icon>refresh</mat-icon>
          {{ 'PRODUCT_LAYOUT.REFRESH' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
