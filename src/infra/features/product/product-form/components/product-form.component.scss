.container-fluid {
  padding: 20px;
}

.product-form-card {
  max-width: 1200px;
  margin: 0 auto;
}

.section {
  margin-bottom: 24px;
}

.w-100 {
  width: 100%;
}

.w-50 {
  width: 50%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

mat-checkbox {
  margin-top: 10px;
  display: block;
}

.add-btn {
  margin-top: -20px;
  margin-left: 8px;
}

.variant-row, .unit-row, .ingredient-row {
  padding: 10px 0;
}

.variant-products-list {
  overflow-x: auto;
  margin-bottom: 20px;

  table {
    width: 100%;

    th {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    td {
      padding: 4px;
      vertical-align: top;

      mat-form-field {
        margin-bottom: 0;
      }
    }
  }

  // Styling cho column tồn kho
  .quantity-column {
    display: flex;
    align-items: center;

    mat-form-field {
      margin-right: 5px;
    }

    button {
      margin-left: 5px;
    }
  }

  // Styling cho warehouse button
  .warehouse-btn {
    color: #3f51b5;
    transition: color 0.3s ease;

    &:hover {
      color: #303f9f;
    }

    &.custom-warehouse {
      color: #4caf50;
    }
  }
}

// CSS để điều chỉnh mat-form-field trong bảng
.variant-products-list .mat-mdc-form-field-subscript-wrapper {
  display: none;
}

// CSS cho mat-expansion-panel
mat-expansion-panel {
  margin-bottom: 16px;
}

mat-expansion-panel-header {
  height: 48px !important;
}

mat-expansion-panel-header mat-panel-title {
  font-weight: 500;
  font-size: 16px;
}

// Styling cho dialog chọn vị trí
.location-path {
  cursor: pointer;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #f5f5f5;
  margin-bottom: 16px;
  display: flex;
  align-items: center;

  mat-icon {
    margin-right: 8px;
    color: #757575;
  }
}

// Responsive
@media (max-width: 768px) {
  .variant-products-list {
    overflow-x: auto;
  }
}
