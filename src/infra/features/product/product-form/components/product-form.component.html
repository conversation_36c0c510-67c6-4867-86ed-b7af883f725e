<div class="container-fluid">
  <div class="product-form-card">
    <h3>{{ editId ? ('PRODUCT.PRODUCT_FORM.EDIT_PRODUCT' | translate) : ('PRODUCT.PRODUCT_FORM.CREATE_PRODUCT' | translate) }}</h3>

    <mat-card-content>
      <form [formGroup]="productForm" (ngSubmit)="saveProduct()">
        <!-- Block thông tin cơ bản -->
        <div class="section">
          <h4>{{ 'PRODUCT.PRODUCT_FORM.BASIC_INFO' | translate }}</h4>

          <div class="row">
            <!-- Tên sản phẩm -->
            <div class="col-md-6 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.NAME' | translate }}</mat-label>
                <input matInput formControlName="name" required>
                <mat-error *ngIf="productForm.get('name')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Mã sản phẩm -->
            <div class="col-md-6 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.SKU' | translate }}</mat-label>
                <input matInput formControlName="sku">
              </mat-form-field>
            </div>
          </div>

          <div class="row">
            <!-- Mô tả -->
            <div class="col-md-12 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.DESCRIPTION' | translate }}</mat-label>
                <textarea matInput formControlName="description" rows="3"></textarea>
              </mat-form-field>
            </div>
          </div>

          <div class="row">
            <!-- Trọng lượng -->
            <div class="col-md-3 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.WEIGHT' | translate }}</mat-label>
                <input matInput type="number" formControlName="weight" min="0">
              </mat-form-field>
            </div>

            <!-- Giá bán -->
            <div class="col-md-3 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.PRICE' | translate }}</mat-label>
                <input matInput type="number" formControlName="price" min="0" required>
                <mat-error *ngIf="productForm.get('price')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="productForm.get('price')?.hasError('min')">
                  {{ 'VALIDATION.MIN' | translate: { value: 0 } }}
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Giá vốn -->
            <div class="col-md-3 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.COST' | translate }}</mat-label>
                <input matInput type="number" formControlName="cost" min="0" required>
                <mat-error *ngIf="productForm.get('cost')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
                <mat-error *ngIf="productForm.get('cost')?.hasError('min')">
                  {{ 'VALIDATION.MIN' | translate: { value: 0 } }}
                </mat-error>
              </mat-form-field>
            </div>

            <!-- Giá bán buôn -->
            <div class="col-md-3 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.WHOLESALE_PRICE' | translate }}</mat-label>
                <input matInput type="number" formControlName="wholesale_price" min="0">
                <mat-error *ngIf="productForm.get('wholesale_price')?.hasError('min')">
                  {{ 'VALIDATION.MIN' | translate: { value: 0 } }}
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <div class="row">
            <!-- Danh mục -->
            <div class="col-md-6 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.CATEGORY' | translate }}</mat-label>
                <mat-select formControlName="categoryId" required>
                  <mat-option *ngFor="let category of categories" [value]="category._id">
                    {{ category.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="productForm.get('categoryId')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
              <button type="button" class="add-btn" mat-icon-button color="primary"
                      matTooltip="{{ 'PRODUCT.PRODUCT_FORM.ADD_CATEGORY' | translate }}"
                      (click)="openCategoryDialog()">
                <mat-icon fontIcon="add_circle"></mat-icon>
              </button>
            </div>

            <!-- Thương hiệu -->
            <div class="col-md-6 mb-3">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.BRAND' | translate }}</mat-label>
                <mat-select formControlName="brandId">
                  <mat-option *ngFor="let brand of brands" [value]="brand._id">
                    {{ brand.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <button type="button" class="add-btn" mat-icon-button color="primary"
                      matTooltip="{{ 'PRODUCT.PRODUCT_FORM.ADD_BRAND' | translate }}"
                      (click)="openBrandDialog()">
                <mat-icon>add_circle</mat-icon>
              </button>
            </div>
          </div>

          <div class="row">
            <!-- Tags -->
            <div class="col-md-12 mb-3">
              <app-chip-form-field
                [label]="'PRODUCT.PRODUCT_FORM.TAGS' | translate"
                [placeholder]="'PRODUCT.PRODUCT_FORM.ENTER_TAG' | translate"
                [suggestions]="tagSuggestions"
                formControlName="tags">
              </app-chip-form-field>
            </div>
          </div>

          <!-- Trạng thái -->
          <div class="row">
            <div class="col-md-12 mb-3">
              <mat-checkbox formControlName="isActive" color="primary">
                {{ 'PRODUCT.PRODUCT_FORM.ACTIVE' | translate }}
              </mat-checkbox>
            </div>
          </div>
        </div>

        <!-- Block quản lý kho ban đầu -->
        <mat-expansion-panel class="mat-elevation-z0 mb-3" expanded>
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'PRODUCT.PRODUCT_FORM.INITIAL_INVENTORY' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <ng-container formArrayName="warehouses">
            <div *ngFor="let warehouseForm of warehousesFormArray.controls; let i = index" class="mb-3">
              <div class="d-flex align-items-center">
                <div class="flex-grow-1">
                  <app-warehouse-and-location-picker
                    [formGroupName]="i"
                    [warehouses]="warehouses"
                    [suppliers]="suppliers"
                    [locations]="locations"
                    (addSupplier)="openSupplierDialog()">
                  </app-warehouse-and-location-picker>
                </div>
                <button type="button" mat-icon-button color="warn" (click)="removeWarehouse(i)">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
              <mat-divider *ngIf="i < warehousesFormArray.controls.length - 1"></mat-divider>
            </div>

            <button type="button" mat-stroked-button color="primary" (click)="addWarehouse()" [disabled]="hasVariants">
              <mat-icon>add</mat-icon>
              {{ 'PRODUCT.PRODUCT_FORM.ADD_WAREHOUSE' | translate }}
            </button>
          </ng-container>
        </mat-expansion-panel>

        <!-- Block thuộc tính sản phẩm -->
        <mat-expansion-panel class="mat-elevation-z0 mb-3">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'PRODUCT.PRODUCT_FORM.VARIANTS' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <ng-container formArrayName="variants">
            <div *ngFor="let variantForm of variantsFormArray.controls; let i = index" class="mb-3">
              <div [formGroupName]="i" class="variant-row">
                <div class="row">
                  <!-- Chọn thuộc tính -->
                  <div class="col-md-5 mb-3">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.VARIANT_NAME' | translate }}</mat-label>
                      <mat-select formControlName="variantId" required>
                        <mat-option *ngFor="let variant of variants" [value]="variant._id">
                          {{ variant.name }}
                        </mat-option>
                      </mat-select>
                      <mat-error *ngIf="variantForm.get('variantId')?.hasError('required')">
                        {{ 'VALIDATION.REQUIRED' | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Chọn giá trị thuộc tính -->
                  <div class="col-md-6 mb-3">
                    <app-chip-form-field
                      [label]="'PRODUCT.PRODUCT_FORM.VARIANT_VALUES' | translate"
                      [placeholder]="'PRODUCT.PRODUCT_FORM.SELECT_VALUES' | translate"
                      [suggestions]="getVariantValues(variantForm)"
                      formControlName="values"
                      [errorMsg]="variantForm.get('values')?.hasError('required') ? ('VALIDATION.REQUIRED' | translate) : ''">
                    </app-chip-form-field>
                  </div>

                  <!-- Nút xóa -->
                  <div class="col-md-1 d-flex align-items-center justify-content-center">
                    <button type="button" mat-icon-button color="warn" (click)="removeVariant(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
              <mat-divider *ngIf="i < variantsFormArray.controls.length - 1"></mat-divider>
            </div>

            <div class="d-flex justify-content-between mt-3">
              <button type="button" mat-stroked-button color="primary" (click)="addVariant()">
                <mat-icon>add</mat-icon>
                {{ 'PRODUCT.PRODUCT_FORM.ADD_VARIANT' | translate }}
              </button>

              <button type="button" mat-stroked-button color="accent" (click)="openVariantDialog()">
                <mat-icon>add_circle</mat-icon>
                {{ 'PRODUCT.PRODUCT_FORM.CREATE_NEW_VARIANT' | translate }}
              </button>
            </div>
          </ng-container>
        </mat-expansion-panel>

        <!-- Block danh sách sản phẩm con -->
        <mat-expansion-panel class="mat-elevation-z0 mb-3" *ngIf="hasVariants" [expanded]="hasVariants">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'PRODUCT.PRODUCT_FORM.VARIANT_PRODUCTS' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <!-- Chọn kho hàng mặc định -->
          <div class="row mb-3">
            <div class="col-md-4">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.DEFAULT_WAREHOUSE' | translate }}</mat-label>
                <mat-select formControlName="defaultWarehouseId" required>
                  <mat-option *ngFor="let warehouse of warehouses" [value]="warehouse._id">
                    {{ warehouse.name }}
                  </mat-option>
                </mat-select>
                <mat-error *ngIf="productForm.get('defaultWarehouseId')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
            </div>

            <div class="col-md-4">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.DEFAULT_SUPPLIER' | translate }}</mat-label>
                <mat-select formControlName="defaultSupplierId">
                  <mat-option *ngFor="let supplier of suppliers" [value]="supplier._id">
                    {{ supplier.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
              <button type="button" class="add-btn" mat-icon-button color="primary"
                      matTooltip="{{ 'PRODUCT.PRODUCT_FORM.ADD_SUPPLIER' | translate }}"
                      (click)="openSupplierDialog()">
                <mat-icon>add_circle</mat-icon>
              </button>
            </div>

            <div class="col-md-4">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.DEFAULT_LOCATION' | translate }}</mat-label>
                <input matInput
                       readonly
                       placeholder="{{ 'WAREHOUSE.SELECT_LOCATION' | translate }}"
                       [value]="getDefaultLocationPath()"
                       (click)="openLocationDialog()">
                <mat-icon matSuffix>location_on</mat-icon>
              </mat-form-field>
            </div>
          </div>

          <!-- Danh sách sản phẩm con -->
          <div class="variant-products-list">
            <ng-container formArrayName="variantProducts">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>{{ 'PRODUCT.PRODUCT_FORM.VARIANTS' | translate }}</th>
                    <th>{{ 'PRODUCT.PRODUCT_FORM.SKU' | translate }}</th>
                    <th>{{ 'PRODUCT.PRODUCT_FORM.PRICE' | translate }}</th>
                    <th>{{ 'PRODUCT.PRODUCT_FORM.COST' | translate }}</th>
                    <th>{{ 'PRODUCT.PRODUCT_FORM.WHOLESALE_PRICE' | translate }}</th>
                    <th>{{ 'PRODUCT.PRODUCT_FORM.QUANTITY' | translate }}</th>
                    <th>{{ 'PRODUCT.PRODUCT_FORM.ACTIONS' | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let variantProduct of variantProductsFormArray.controls; let i = index" [formGroupName]="i">
                    <td>
                      <div *ngFor="let option of variantProduct.get('variant_options')?.value">
                        <strong>{{ option.variantName }}:</strong> {{ option.value }}
                      </div>
                    </td>
                    <td>
                      <mat-form-field class="w-100" appearance="outline">
                        <input matInput formControlName="sku">
                      </mat-form-field>
                    </td>
                    <td>
                      <mat-form-field class="w-100" appearance="outline">
                        <input matInput type="number" formControlName="price" min="0" required>
                      </mat-form-field>
                    </td>
                    <td>
                      <mat-form-field class="w-100" appearance="outline">
                        <input matInput type="number" formControlName="cost" min="0" required>
                      </mat-form-field>
                    </td>
                    <td>
                      <mat-form-field class="w-100" appearance="outline">
                        <input matInput type="number" formControlName="wholesale_price" min="0">
                      </mat-form-field>
                    </td>
                    <td class="quantity-column">
                      <mat-form-field class="w-50" appearance="outline">
                        <input matInput type="number" formControlName="quantity" min="0" required>
                      </mat-form-field>
                      <button type="button" mat-icon-button
                              matTooltip="{{ 'PRODUCT.PRODUCT_FORM.CUSTOM_WAREHOUSE' | translate }}"
                              [class.custom-warehouse]="customWarehouseMap.has(i)"
                              class="warehouse-btn"
                              (click)="openWarehouseForVariant(i)">
                        <mat-icon>warehouse</mat-icon>
                      </button>
                    </td>
                    <td>
                      <button type="button" mat-icon-button color="primary"
                              matTooltip="{{ 'PRODUCT.PRODUCT_FORM.VARIANT_DETAILS' | translate }}"
                              (click)="openVariantDetails(i)">
                        <mat-icon>edit</mat-icon>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </ng-container>
          </div>
        </mat-expansion-panel>

        <!-- Block đơn vị tính -->
        <mat-expansion-panel class="mat-elevation-z0 mb-3">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'PRODUCT.PRODUCT_FORM.UNITS_OF_MEASURE' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <div class="row mb-3">
            <div class="col-md-6">
              <mat-form-field class="w-100" appearance="outline">
                <mat-label>{{ 'PRODUCT.PRODUCT_FORM.BASE_UNIT' | translate }}</mat-label>
                <input matInput formControlName="baseUnit" required>
                <mat-error *ngIf="productForm.get('baseUnit')?.hasError('required')">
                  {{ 'VALIDATION.REQUIRED' | translate }}
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <h5>{{ 'PRODUCT.PRODUCT_FORM.CONVERSION_UNITS' | translate }}</h5>
          <ng-container formArrayName="units">
            <div *ngFor="let unitForm of unitsFormArray.controls; let i = index" class="mb-3">
              <div [formGroupName]="i" class="unit-row">
                <div class="row">
                  <!-- Tên đơn vị -->
                  <div class="col-md-2">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.UNIT_NAME' | translate }}</mat-label>
                      <input matInput formControlName="name" required>
                      <mat-error *ngIf="unitForm.get('name')?.hasError('required')">
                        {{ 'VALIDATION.REQUIRED' | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Giá trị quy đổi -->
                  <div class="col-md-2">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.CONVERSION_VALUE' | translate }}</mat-label>
                      <input matInput type="number" formControlName="conversionValue" min="0.01" required>
                      <mat-error *ngIf="unitForm.get('conversionValue')?.hasError('required')">
                        {{ 'VALIDATION.REQUIRED' | translate }}
                      </mat-error>
                      <mat-error *ngIf="unitForm.get('conversionValue')?.hasError('min')">
                        {{ 'VALIDATION.MIN' | translate: { value: 0.01 } }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Giá bán lẻ -->
                  <div class="col-md-2">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.RETAIL_PRICE' | translate }}</mat-label>
                      <input matInput type="number" formControlName="retailPrice" min="0">
                      <mat-error *ngIf="unitForm.get('retailPrice')?.hasError('min')">
                        {{ 'VALIDATION.MIN' | translate: { value: 0 } }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Giá bán buôn -->
                  <div class="col-md-2">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.WHOLESALE_PRICE' | translate }}</mat-label>
                      <input matInput type="number" formControlName="wholesalePrice" min="0">
                      <mat-error *ngIf="unitForm.get('wholesalePrice')?.hasError('min')">
                        {{ 'VALIDATION.MIN' | translate: { value: 0 } }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Giá vốn -->
                  <div class="col-md-2">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.COST' | translate }}</mat-label>
                      <input matInput type="number" formControlName="cost" min="0">
                      <mat-error *ngIf="unitForm.get('cost')?.hasError('min')">
                        {{ 'VALIDATION.MIN' | translate: { value: 0 } }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Mã hàng -->
                  <div class="col-md-1">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.SKU' | translate }}</mat-label>
                      <input matInput formControlName="sku">
                    </mat-form-field>
                  </div>

                  <!-- Nút xóa -->
                  <div class="col-md-1 d-flex align-items-center justify-content-center">
                    <button type="button" mat-icon-button color="warn" (click)="removeUnit(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
              <mat-divider *ngIf="i < unitsFormArray.controls.length - 1"></mat-divider>
            </div>

            <button type="button" mat-stroked-button color="primary" (click)="addUnit()">
              <mat-icon>add</mat-icon>
              {{ 'PRODUCT.PRODUCT_FORM.ADD_UNIT' | translate }}
            </button>
          </ng-container>
        </mat-expansion-panel>

        <!-- Block thành phần nguyên liệu -->
        <mat-expansion-panel class="mat-elevation-z0 mb-3">
          <mat-expansion-panel-header>
            <mat-panel-title>
              {{ 'PRODUCT.PRODUCT_FORM.INGREDIENTS' | translate }}
            </mat-panel-title>
          </mat-expansion-panel-header>

          <ng-container formArrayName="ingredients">
            <div *ngFor="let ingredientForm of ingredientsFormArray.controls; let i = index" class="mb-3">
              <div [formGroupName]="i" class="ingredient-row">
                <div class="row">
                  <!-- Chọn sản phẩm -->
                  <div class="col-md-4">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.INGREDIENT' | translate }}</mat-label>
                      <input matInput
                             type="text"
                             formControlName="productName"
                             [matAutocomplete]="auto"
                             required>
                      <mat-autocomplete #auto="matAutocomplete"
                                      [displayWith]="displayProductFn"
                                      (optionSelected)="onProductSelected($event, i)">
                        <mat-option *ngFor="let product of filteredProducts(ingredientForm.get('productName')?.value)"
                                   [value]="product">
                          {{ product.name }} ({{ product.units?.baseUnit }})
                        </mat-option>
                      </mat-autocomplete>
                      <mat-error *ngIf="ingredientForm.get('productName')?.hasError('required')">
                        {{ 'VALIDATION.REQUIRED' | translate }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Số lượng -->
                  <div class="col-md-2">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.QUANTITY' | translate }}</mat-label>
                      <input matInput
                             type="number"
                             formControlName="quantity"
                             min="0.01"
                             required
                             (ngModelChange)="updateTotal(i)">
                      <span matSuffix *ngIf="getSelectedProductUnit(i)">{{ getSelectedProductUnit(i) }}</span>
                      <mat-error *ngIf="ingredientForm.get('quantity')?.hasError('required')">
                        {{ 'VALIDATION.REQUIRED' | translate }}
                      </mat-error>
                      <mat-error *ngIf="ingredientForm.get('quantity')?.hasError('min')">
                        {{ 'VALIDATION.MIN' | translate: { value: 0.01 } }}
                      </mat-error>
                    </mat-form-field>
                  </div>

                  <!-- Giá vốn đơn vị -->
                  <div class="col-md-2">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.UNIT_COST' | translate }}</mat-label>
                      <input matInput
                             type="number"
                             formControlName="cost"
                             readonly>
                      <span matSuffix>đ</span>
                    </mat-form-field>
                  </div>

                  <!-- Thành tiền -->
                  <div class="col-md-3">
                    <mat-form-field class="w-100" appearance="outline">
                      <mat-label>{{ 'PRODUCT.PRODUCT_FORM.TOTAL' | translate }}</mat-label>
                      <input matInput
                             type="number"
                             formControlName="total"
                             readonly>
                      <span matSuffix>đ</span>
                    </mat-form-field>
                  </div>

                  <!-- Nút xóa -->
                  <div class="col-md-1 d-flex align-items-center justify-content-center">
                    <button type="button" mat-icon-button color="warn" (click)="removeIngredient(i)">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
              <mat-divider *ngIf="i < ingredientsFormArray.controls.length - 1"></mat-divider>
            </div>

            <!-- Tổng giá vốn -->
            <div class="row mb-3" *ngIf="ingredientsFormArray.controls.length > 0">
              <div class="col-md-8 text-end">
                <strong>{{ 'PRODUCT.PRODUCT_FORM.TOTAL_COSTS' | translate }}:</strong>
              </div>
              <div class="col-md-3">
                <mat-form-field class="w-100 mdc-text-field--no-label" appearance="outline">
                  <input matInput [value]="getTotalCost() | number:'1.0-0'" readonly>
                  <span matSuffix>đ</span>
                </mat-form-field>
              </div>
            </div>

            <!-- Tổng thành phần -->
            <div class="row mb-3" *ngIf="ingredientsFormArray.controls.length > 0">
              <div class="col-md-8 text-end">
                <strong>{{ 'PRODUCT.PRODUCT_FORM.TOTAL_INGREDIENTS' | translate }}:</strong>
              </div>
              <div class="col-md-3">
                <mat-form-field class="w-100 mdc-text-field--no-label" appearance="outline">
                  <input matInput [value]="getTotalIngredients() | number:'1.2-2'" readonly>
                  <span matSuffix>{{ getBaseUnit() }}</span>
                </mat-form-field>
              </div>
            </div>

            <button type="button" mat-stroked-button color="primary" (click)="addIngredient()">
              <mat-icon>add</mat-icon>
              {{ 'PRODUCT.PRODUCT_FORM.ADD_INGREDIENT' | translate }}
            </button>
          </ng-container>
        </mat-expansion-panel>

        <!-- Nút bấm -->
        <div class="form-actions mt-4">
          <button type="button" mat-button>
            {{ 'COMMON.CANCEL' | translate }}
          </button>
          <button type="submit" mat-raised-button color="primary"
          >
            {{ 'COMMON.SAVE' | translate }}
          </button>
        </div>
      </form>
    </mat-card-content>
  </div>
</div>
