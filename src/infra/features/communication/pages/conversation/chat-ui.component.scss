.chat-container {
  height: calc(100vh - 65px);
  overflow: hidden;
}

.chat-sidebar {
  .count-unread {
    top: -7px;
    right: -4px;
    padding: 3px 5px;
  }

  .trimmed-text {
    max-width: 250px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  
  @media (max-width: 1023px) {
    height: 300px;
    border-bottom: 1px solid var(--tw-gray-200);
  }
}

.chat-area {
  min-width: 0; // Prevent flex items from overflowing
  flex: 1 1 auto; // Allow chat area to grow and shrink
}

.chat-input {
  width: 100%;
  
  textarea.form-control {
    width: 100% !important;
    display: block;
  }
}

.chat-messages {
  height: 100%;
  display: flex;
  flex-direction: column-reverse; // Reverse the direction to make it Facebook-style
  
  cdk-virtual-scroll-viewport {
    height: 100%;
    width: 100%;
    
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: var(--tw-gray-300);
      border-radius: 4px;
    }
  }
}

.loading-indicator {
  padding: 10px 0;
  
  .spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--kt-primary);
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
}

.contacts-list {
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--tw-gray-300);
    border-radius: 4px;
  }
}

.message-wrapper {
  &:last-child {
    margin-bottom: 0;
  }
}

.audio-waveform {
  overflow: hidden;
}

.media {
  scrollbar-width: thin;
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: var(--tw-gray-300);
    border-radius: 4px;
  }
}

// Responsive adjustments
@media (max-width: 1023px) {
  .chat-container {
    flex-direction: column;
  }
  
  .detail-panel {
    display: none;
  }
}
