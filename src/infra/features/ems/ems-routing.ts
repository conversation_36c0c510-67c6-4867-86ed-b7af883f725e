import { Routes } from '@angular/router';
import { RouterResolverService } from '@core/services/router.resolver.service';
import { canActivateCashier } from '@core/guards/auth.guard';

export const EMSRoutes: Routes = [
  {
    path: 'organization/departments',
    // data: {
    //   resolverNavigationBlock: 'cashier',
    //   resolverPermission: 'cashier',
    //   resolverRequestQuery: ['cashier']
    // },
    // resolve: RouterResolverService,
    loadComponent: () => import('./pages/manage-departments/manage-departments.component')
      .then((m) => m.ManageDepartmentsComponent),
    // canActivate: [canActivateCashier]
  }
];

// Route riêng cho organization/info
export const OrganizationRoutes: Routes = [
  {
    path: 'organization/info-form',
    // data: {
    //   resolverNavigationBlock: 'cashier',
    //   resolverPermission: 'cashier',
    //   resolverRequestQuery: ['cashier']
    // },
    // resolve: RouterResolverService,
    loadComponent: () => import('./components/organization-info-form/organization-info-form.component')
      .then((m) => m.OrganizationInfoFormComponent),
    // canActivate: [canActivateCashier]
  }
];
