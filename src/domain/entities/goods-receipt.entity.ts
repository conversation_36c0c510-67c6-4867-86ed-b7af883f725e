import type {
  GoodsReceipt,
  GoodsReceiptItem,
  TaxInfo,
  TransportInfo,
  QualityCheck,
  QualityCheckRejectedItem,
  ProductBatchItem,
  InitialPayment
} from 'salehub_shared_contracts/entities/ims/inventory/goods_receipt';
import type { ImportAdditionalCost } from 'salehub_shared_contracts/entities/scm/import_additional_cost';
import type { EmbeddedProduct } from 'salehub_shared_contracts/entities/ims/product/embedded_product';
import type { EmbeddedWarehouseLocation } from 'salehub_shared_contracts/entities/wms/warehouse_location';

// Re-export các types từ contracts để sử dụng trong domain
export {
  GoodsReceipt,
  GoodsReceiptItem,
  TaxInfo,
  TransportInfo,
  QualityCheck,
  QualityCheckRejectedItem,
  ProductBatchItem,
  InitialPayment,
  ImportAdditionalCost,
  EmbeddedProduct,
  EmbeddedWarehouseLocation
};
