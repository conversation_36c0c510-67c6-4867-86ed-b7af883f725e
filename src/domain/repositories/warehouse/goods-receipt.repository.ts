import { Observable } from 'rxjs';
import { GoodsReceipt } from '@domain/entities/goods-receipt.entity';

/**
 * Repository abstraction cho Goods Receipt
 * Tuân thủ nguyên tắc Dependency Inversion của Clean Architecture
 */
export abstract class GoodsReceiptRepository {
  /**
   * Lấy thông tin phiếu nhập kho theo ID
   * @param id ID của phiếu nhập kho
   */
  abstract getGoodsReceipt(id: string): Observable<GoodsReceipt | null>;

  /**
   * Tạo mới phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho
   */
  abstract createGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt>;

  /**
   * Cập nhật phiếu nhập kho
   * @param goodsReceipt Thông tin phiếu nhập kho cần cập nhật
   */
  abstract updateGoodsReceipt(goodsReceipt: GoodsReceipt): Observable<GoodsReceipt>;

  /**
   * Tính toán tổng tiền hàng
   * @param goodsReceipt Phiếu nhập kho
   */
  abstract calculateSubTotal(goodsReceipt: GoodsReceipt): number;

  /**
   * Tính toán tổng số tiền cần thanh toán
   * @param goodsReceipt Phiếu nhập kho
   */
  abstract calculateTotalPayment(goodsReceipt: GoodsReceipt): number;

  /**
   * Tính toán công nợ
   * @param goodsReceipt Phiếu nhập kho
   */
  abstract calculateDebt(goodsReceipt: GoodsReceipt): number;

  /**
   * Tính toán tổng chi phí
   * @param goodsReceipt Phiếu nhập kho
   */
  abstract calculateTotalAdditionalCost(goodsReceipt: GoodsReceipt): number;

  /**
   * Tính toán tổng thuế
   * @param goodsReceipt Phiếu nhập kho
   */
  abstract calculateTotalTax(goodsReceipt: GoodsReceipt): number;
}
