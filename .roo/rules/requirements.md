---
description:
globs:
alwaysApply: true
---
<PERSON><PERSON><PERSON> cầu chung:

- Bạn phải đọc toàn bộ rules trong .roo/rules/*
- Bạn phải đọc toàn bộ docs trong docs/*
- Bạn phải đọc [FOLDER_STRUCTURE.md](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/FOLDER_STRUCTURE.md) để hiểu rõ cấu trúc folder. Cấu trúc folder chi tiết và cách tổ chức các file/folder cho dự án ERP, sử dụng **Angular 19** với **standalone components** và **Clean Architecture**
- Kết nối với các MCP server hiện tại để tự động debug lỗi và xem preview trên chrome.
- <PERSON>hi không còn lỗi nào, xem trên giao diện bằng MCP, xem có lỗi gì không và sửa. Tôi đã dựng sẵn server và watch changes rồi, bạn chỉ cần vào thông qua cổng 4200 và kiểm tra.
- File translate đã được cấu hình nằm ở src/infra/i18n. KHÔNG ĐƯỢC PHÉP TẠO TRONG src/assets/i18n/*. Không được phép ghi đè lên các field có sẵn trong file i18n, chỉ được phép append thêm field vào.
- Bạn luôn tự thêm i18n vào file json, không phải tôi thêm bằng tay.
- Không viết thêm các function để xử lý i18n, đọc  [ngx-translate-integration.md](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/docs/ngx-translate-integration.md) để tích hợp vào. Luôn luôn phải tuân thủ rule [i18n.md](mdc:frontend/frontend/.roo/rules/i18n.md)
- Các key trong TRANSLATION luôn phải đặt theo dạng SCREAMING_SNAKE_CASE (UPPER_SNAKE_CASE)
- Luôn thêm chi tiết comment trong code để giải thích logic bằng tiếng việt.
- Giao diện nên được viết bằng bootstrap và bắt buộc phải responsive
- Khi import vào angular, dùng các paths ngắn được định nghĩa trong [tsconfig.json](mdc:frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/frontend/tsconfig.json) như @core, @shared, @mock...
- KHÔNG ĐƯỢC PHÉP sửa các file quan trọng như app.component, layout/*... Tất cả logic của component con phải được định nghĩa trong folder của component con. Như  product thì chỉ gói gọn trong features/product. Tất cả router phải được export vào router con như product-routing.ts nằm trong features/product/product-routing.ts. Không export vào app.routing.ts. KHÔNG ĐƯỢC PHÉP đụng đến logic và giao diện của toàn ứng dụng.
- Từng bước step phải lưu lại vào .roo/context/[taskname].md để tôi theo dõi và để bạn nhớ rõ những task đã làm, phục vụ cho bạn khi làm 1 task quá dài, vượt quá context dẫn đến việc bạn bị quên context. Các lần tiếp theo bạn phải đọc lại context xem đã làm những task nào, đang làm đến đâu để nạp lại context vào bộ nhớ.
- Không được viết 1 component quá dài, nên phân định rõ cái nào xử lý trong component và cái nào xử lý trong service. Nếu 1 component bao gồm nhiều thành phần, chia nó thành các component con và gọi đến các component con từ component cha. Component chỉ dùng để tương tác với UI, logic nghiệp vụ phải được đẩy xuống service. Nên chia service ngay trong folder component, ví dụ product/product.component.ts, product/product.service.ts
- Khi muốn test 1 chức năng, 1 dialog nào đó, viết thẳng vào @test-theme.component.ts, không được phép tạo component mới. Path để test là http://localhost:4200/#/test. Với các pages có path thì test bằng path đó.
- QUAN TRỌNG: Sau khi viết xong, Chạy ng build xem còn lỗi gì không và sửa.
