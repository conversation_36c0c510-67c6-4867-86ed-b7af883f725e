import { promises as fs } from 'node:fs';
import path from 'node:path';
import chokidar from 'chokidar';
import { fileURLToPath } from 'node:url';

// T<PERSON>i tạo __dirname trong ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const i18nPath = path.join(__dirname, '../src/infra/i18n');
const outputPath = path.join(__dirname, '../public/assets/i18n');

async function mergeLanguage(lang, i18nPath, outputPath) {
  const translations = {};

  // Hàm quét đệ quy để đọc tất cả file JSON theo ngôn ngữ
  async function collectTranslations(currentPath, prefix = '') {
    const entries = await fs.readdir(currentPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentPath, entry.name);
      const keyPrefix = prefix ? `${prefix}.${entry.name}` : entry.name;

      if (entry.isDirectory()) {
        await collectTranslations(fullPath, keyPrefix);
      } else if (entry.name === `${lang}.json`) {
        try {
          const content = await fs.readFile(fullPath, 'utf-8');
          const parsed = JSON.parse(content);
          mergeDeep(translations, parsed);
        } catch (error) {
          console.error(`Error reading file ${fullPath}:`, error);
          throw error;
        }
      }
    }
  }

  try {
    await collectTranslations(i18nPath);
    await fs.mkdir(outputPath, { recursive: true });
    await fs.writeFile(
      path.join(outputPath, `${lang}.json`),
      JSON.stringify(translations, null, 2)
    );
    console.log(`Merged ${lang} translations to ${path.join(outputPath, `${lang}.json`)}`);
  } catch (error) {
    console.log(error);
    console.error(`Error merging ${lang} translations:`, error);
  }
}

async function mergeTranslations() {
  const languages = ['en', 'vi'];

  for (const lang of languages) {
    await mergeLanguage(lang, i18nPath, outputPath);
  }
}

async function watchTranslations() {
  const languages = ['en', 'vi'];

  // Gộp lần đầu
  await mergeTranslations();


  // Theo dõi thay đổi
  const watcher = chokidar.watch(i18nPath, {
    ignored: (path, stats) => stats?.isFile() && !path.endsWith('.json'),
    persistent: true,
  });


  watcher.on('change', async (filePath) => {
    console.log(`File changed: ${filePath}`);
    const lang = path.basename(filePath, '.json');
    if (languages.includes(lang)) {
      await mergeLanguage(lang, i18nPath, outputPath);
    }
  });

  console.log('Watching for translation file changes in infra/i18n...');
}

function isObject(item) {
  return (item && typeof item === 'object' && !Array.isArray(item));
}

function mergeDeep(target, ...sources) {
  if (!sources.length) return target;
  const source = sources.shift();

  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!target[key]) {
          Object.assign(target, {
            [key]: {}
          });
        }
        mergeDeep(target[key], source[key]);
      } else {
        Object.assign(target, {
          [key]: source[key]
        });
      }
    });
  }

  return mergeDeep(target, ...sources);
}

// Kiểm tra tham số dòng lệnh
if (process.argv.includes('--watch')) {
  watchTranslations();
} else {
  mergeTranslations();
}
