#!/usr/bin/env node
import { readFile, writeFile } from 'fs/promises';

// Từ điển chuẩn để đề xuất bản dịch
const translationDictionary = {
  Save: "<PERSON><PERSON><PERSON>",
  Cancel: "<PERSON>ủ<PERSON>",
  Submit: "<PERSON><PERSON>c N<PERSON>",
  Create: "<PERSON>ạ<PERSON>",
  Order: "Đơn H<PERSON>",
  Title: "Ti<PERSON>u <PERSON>",
  Confirm: "Xác Nhận",
  Panel: "Bảng Điều Khiển",
  Dialog: "Hộp Thoại"
};

// Hàm chuyển JSON phân cấp thành danh sách key phẳng
function flattenKeys(obj, prefix = '') {
  const keys = [];
  for (const [key, value] of Object.entries(obj)) {
    const newKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...flattenKeys(value, newKey));
    } else {
      keys.push(newKey);
    }
  }
  return keys;
}

// Hàm đọc file JSON
async function readJsonFile(filePath) {
  try {
    const content = await readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Lỗi khi đọc file ${filePath}: ${error.message}`);
    return null;
  }
}

// Hàm đọc file html_translation_keys.js
async function readHtmlTranslationKeys(filePath) {
  try {
    const content = await readFile(filePath, 'utf-8');
    // Giả sử file là module CommonJS
    const module = { exports: [] };
    eval(content); // Không an toàn trong môi trường thực tế, nên dùng require trong Node
    return module.exports;
  } catch (error) {
    console.error(`Lỗi khi đọc file ${filePath}: ${error.message}`);
    return null;
  }
}

// Hàm đề xuất vị trí thêm key
function suggestTranslationFile(key) {
  const parts = key.split('.');
  const namespace = parts[0];

  if (namespace === 'COMMON') {
    return 'src/infra/i18n/global';
  } else if (parts.length >= 2) {
    const feature = namespace.toLowerCase();
    if (parts.length === 2) {
      return `src/infra/i18n/${feature}`;
    } else {
      const subFeature = parts[1].toLowerCase().replace(/_/g, '-');
      return `src/infra/i18n/${feature}/${subFeature}`;
    }
  }
  return 'src/infra/i18n/global'; // Mặc định nếu không xác định được
}

// Hàm so sánh key
async function compareTranslationKeys(htmlKeysFile, mergedFile, outputFile) {
  // Đọc danh sách key từ html_translation_keys.js
  const htmlKeysData = await readHtmlTranslationKeys(htmlKeysFile);
  if (!htmlKeysData) {
    console.error('Lỗi: Không thể đọc danh sách key từ html_translation_keys.js');
    process.exit(1);
  }
  const htmlKeys = htmlKeysData.map(item => item.key);

  const mergedJson = await readJsonFile(mergedFile);
  if (!mergedJson) {
    console.error(`Lỗi: Không thể đọc ${mergedFile}`);
    process.exit(1);
  }
  const mergedKeys = flattenKeys(mergedJson);

  // Tìm key thiếu
  const missingKeys = htmlKeys
    .filter(key => !mergedKeys.includes(key))
    .map(key => {
      const htmlKeyData = htmlKeysData.find(item => item.key === key);
      const suggestedFolder = suggestTranslationFile(key);
      const enValue = key.split('.').pop().replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
      const viValue = translationDictionary[enValue] || 'Chưa có bản dịch';
      return {
        key,
        htmlFile: htmlKeyData.filePath,
        lineNumber: htmlKeyData.lineNumber,
        suggestion: `Thêm key "${key}" vào ${suggestedFolder}/en.json và vi.json:\n` +
                    `  en.json: "${enValue}"\n` +
                    `  vi.json: "${viValue}"`
      };
    })
    .sort((a, b) => a.key.localeCompare(b.key)); // Sắp xếp theo key

  // Báo cáo kết quả
  if (missingKeys.length === 0) {
    console.log(`Thành công: Không có key nào trong html_translation_keys.js thiếu trong ${mergedFile}`);
  } else {
    console.error(`Lỗi: Tìm thấy ${missingKeys.length} key thiếu trong ${mergedFile}`);
    // for (const { key, htmlFile, lineNumber, suggestion } of missingKeys) {
    //   console.error(`  - Key: "${key}"`);
    //   console.error(`    HTML File: ${htmlFile} (Dòng: ${lineNumber})`);
    //   console.error(`    Đề xuất: ${suggestion}`);
    // }
  }

  // Xuất danh sách key thiếu ra file (chỉ chứa key, đã sắp xếp)
  const outputKeys = missingKeys.map(item => item.key);
  const outputContent = `module.exports = ${JSON.stringify(outputKeys, null, 2)};`;
  try {
    await writeFile(outputFile, outputContent, 'utf-8');
    console.log(`Đã xuất danh sách key thiếu ra: ${outputFile}`);
  } catch (error) {
    console.error(`Lỗi khi ghi file ${outputFile}: ${error.message}`);
  }

  return missingKeys.length === 0;
}

export { compareTranslationKeys };