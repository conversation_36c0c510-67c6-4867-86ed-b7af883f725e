#!/usr/bin/env node
import { readFile, readdir, stat, writeFile } from 'fs/promises';
import { join, resolve } from 'path';
import glob from 'glob-promise';

// Hàm chuyển JSON phân cấp thành danh sách key phẳng
function flattenKeys(obj, prefix = '') {
  const keys = [];
  for (const [key, value] of Object.entries(obj)) {
    const newKey = prefix ? `${prefix}.${key}` : key;
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...flattenKeys(value, newKey));
    } else {
      keys.push(newKey);
    }
  }
  return keys;
}

// Hàm đọc và phân tích file JSON
async function readJsonFile(filePath) {
  try {
    const content = await readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    if (error.code === 'ENOENT') {
      console.error(`Lỗi: File không tồn tại: ${filePath}`);
    } else {
      console.error(`Lỗi khi đọc file ${filePath}: ${error.message}`);
    }
    return null;
  }
}

// Hàm ghi file JSON với định dạng đẹp
async function writeJsonFile(filePath, data) {
  try {
    await writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
    console.log(`Đã cập nhật file: ${filePath}`);
  } catch (error) {
    console.error(`Lỗi khi ghi file ${filePath}: ${error.message}`);
  }
}

// Hàm xóa key khỏi object JSON
function removeKeyFromJson(json, key) {
  const parts = key.split('.');
  let current = json;
  for (let i = 0; i < parts.length - 1; i++) {
    current = current[parts[i]];
    if (!current) return false;
  }
  const lastKey = parts[parts.length - 1];
  if (current[lastKey] !== undefined) {
    delete current[lastKey];
    return true;
  }
  return false;
}

// Hàm thu thập tất cả key translation từ src/infra/i18n/
async function collectTranslationKeys(i18nPath) {
  const keyMap = new Map(); // Lưu key và file chứa key

  async function scanDirectory(dir) {
    try {
      const entries = await readdir(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = join(dir, entry.name);
        if (entry.isDirectory()) {
          await scanDirectory(fullPath);
        } else if (entry.isFile() && entry.name === 'en.json') {
          const json = await readJsonFile(fullPath);
          if (json) {
            const keys = flattenKeys(json);
            for (const key of keys) {
              if (!keyMap.has(key)) {
                keyMap.set(key, fullPath);
              } else {
                console.warn(`Cảnh báo: Key "${key}" trùng lặp trong ${fullPath} và ${keyMap.get(key)}.`);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error(`Lỗi khi duyệt thư mục ${dir}: ${error.message}`);
    }
  }

  try {
    const stats = await stat(i18nPath);
    if (!stats.isDirectory()) {
      console.error(`Lỗi: Thư mục i18n không tồn tại: ${i18nPath}`);
      return keyMap;
    }
    await scanDirectory(i18nPath);
  } catch (error) {
    console.error(`Lỗi: Không thể truy cập thư mục i18n: ${i18nPath}`);
  }

  return keyMap;
}

// Hàm tìm kiếm key trong mã nguồn
async function findKeyInSource(key, projectPath) {
  const pattern = `**/*.{ts,html}`;
  const files = await glob(pattern, {
    cwd: projectPath,
    ignore: [
      'node_modules/**',
      'dist/**',
      'public/assets/i18n/**',
      'src/infra/i18n/**', // Bỏ qua file translation
      '**/*.spec.ts', // Bỏ qua file test
    ],
    absolute: true,
  });


  for (const file of files) {
    try {
      const content = await readFile(file, 'utf-8');
      // Tìm key trong file (hỗ trợ cả 'COMMON.SAVE' và "COMMON.SAVE")
      if (content.includes(`'${key}'`) || content.includes(`"${key}"`) || content.includes('`'+key+'`')) {
        return file;
      }
    } catch (error) {
      console.warn(`Cảnh báo: Không thể đọc file ${file}: ${error.message}`);
    }
  }
  return null;
}

// Hàm tìm và xóa key translation không được sử dụng
async function findUnusedTranslations(projectPath, i18nPath, outputUnunsedTranslationKeyFile, remove = false) {
  // Thu thập key translation
  const keyMap = await collectTranslationKeys(i18nPath);
  console.log(`Tổng số key translation: ${keyMap.size}`);

  const unusedKeys = [];
  let checkedKeys = 0;

  // Kiểm tra từng key
  for (const [key, filePath] of keyMap) {
    checkedKeys++;
    if (checkedKeys % 100 === 0) {
      console.log(`Đã kiểm tra ${checkedKeys}/${keyMap.size} key...`);
    }

    const foundFile = await findKeyInSource(key, projectPath);
    if (!foundFile) {
      unusedKeys.push({ key, filePath, suggestion: `Xóa key "${key}" khỏi ${filePath} và file vi.json tương ứng.` });
    }
  }

  // Báo cáo kết quả
  if (unusedKeys.length === 0) {
    console.log('Thành công: Không tìm thấy key translation nào không được sử dụng.');
  } else {
    console.error(`Lỗi: Tìm thấy ${unusedKeys.length} key translation không được sử dụng:`);
    for (const { key, filePath, suggestion } of unusedKeys) {
      console.error(`  - Key: "${key}"`);
      console.error(`    File: ${filePath}`);
      console.error(`    Đề xuất: ${suggestion}`);
    }
  }

  // Xuất danh sách key thừa ra file unused_translation_keys.js
  const outputFile = outputUnunsedTranslationKeyFile;
  const outputContent = `module.exports = ${JSON.stringify(unusedKeys, null, 2)};`;
  try {
    await writeFile(outputFile, outputContent, 'utf-8');
    console.log(`Đã xuất danh sách key thừa ra: ${outputFile}`);
  } catch (error) {
    console.error(`Lỗi khi ghi file ${outputFile}: ${error.message}`);
  }

  // Xóa key thừa nếu có tùy chọn --remove
  if (remove && unusedKeys.length > 0) {
    console.log('Bắt đầu xóa key translation không được sử dụng...');
    for (const { key, filePath } of unusedKeys) {
      // Cập nhật en.json
      const enJson = await readJsonFile(filePath);
      if (enJson && removeKeyFromJson(enJson, key)) {
        await writeJsonFile(filePath, enJson);
      }

      // Cập nhật vi.json
      const viFilePath = filePath.replace('en.json', 'vi.json');
      const viJson = await readJsonFile(viFilePath);
      if (viJson && removeKeyFromJson(viJson, key)) {
        await writeJsonFile(viFilePath, viJson);
      }
    }
    console.log('Đã xóa tất cả key translation không được sử dụng.');
  }

  return unusedKeys.length === 0;
}

export { findUnusedTranslations };