#!/usr/bin/env node
import { join, resolve, dirname } from 'path';
import { compareTranslations } from '../compare-translations.js';

// <PERSON><PERSON>m <PERSON>
async function main() {
  const folderPath = process.argv[2] ? resolve(process.argv[2]) : process.cwd();

  console.log(`So s<PERSON>h bản dịch trong thư mục: ${folderPath}`);

  const success = await compareTranslations(folderPath);

  process.exit(success ? 0 : 1);
}

// Chạy script
main().catch(error => {
  console.error(`Lỗi không xác định: ${error.message}`);
  process.exit(1);
});