#!/usr/bin/env node
import { join, resolve } from 'path';
import { collectHtmlTranslationKeys, exportTranslationKeys } from '../export-component-html-translation-keys.js';
import { stat } from 'fs/promises';

// Hàm chính
async function main() {
  const args = process.argv.slice(2);
  const folderPath = args[0] && !args[0].startsWith('--') ? resolve(args[0]) : process.cwd();
  const outputArgIndex = args.findIndex(arg => arg.startsWith('--output='));
  const outputFile = outputArgIndex !== -1
    ? resolve(args[outputArgIndex].split('=')[1])
    : join(process.cwd(), 'scripts/exports/html_translation_keys.js');

  console.log(`Quét translation key trong các file .html trong: ${folderPath}`);
  console.log(`File đầu ra: ${outputFile}`);

  // Kiểm tra folder tồn tại
  try {
    const stats = await stat(folderPath);
    if (!stats.isDirectory()) {
      console.error(`Lỗi: Đường dẫn không phải thư mục: ${folderPath}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`Lỗi: Thư mục không tồn tại: ${folderPath}`);
    process.exit(1);
  }

  // Thu thập key
  const keys = await collectHtmlTranslationKeys(folderPath);

  // Báo cáo kết quả
  if (keys.length === 0) {
    console.log('Không tìm thấy translation key nào trong các file .html.');
  } else {
    console.log(`Tìm thấy ${keys.length} translation key:`);
    for (const { key, filePath, lineNumber, line } of keys) {
      console.log(`  - Key: "${key}"`);
      console.log(`    File: ${filePath}`);
      console.log(`    Dòng: ${lineNumber} (${line})`);
    }
  }

  // Xuất key ra file
  await exportTranslationKeys(keys, outputFile);

  process.exit(0);
}

// Chạy script
main().catch(error => {
  console.error(`Lỗi không xác định: ${error.message}`);
  process.exit(1);
});