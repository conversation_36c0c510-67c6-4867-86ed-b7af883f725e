#!/usr/bin/env node

import { join, resolve } from 'path';
import { compareTranslationKeys } from '../compare-component-translation-keys.js';
import { stat } from 'fs/promises';


// Hàm chính
async function main() {
  const args = process.argv.slice(2);
  const projectPath = process.cwd();
  const htmlKeysFile = args[0] ? resolve(args[0]) : join(projectPath, 'scripts/exports/html_translation_keys.js');
  const mergedFile = args[1] ? resolve(args[1]) : join(projectPath, 'public/assets/i18n/en.json');
  const outputArgIndex = args.findIndex(arg => arg.startsWith('--output='));
  const outputFile = outputArgIndex !== -1
    ? resolve(args[outputArgIndex].split('=')[1])
    : join(projectPath, 'scripts/exports/missing_translation_keys.js');

  console.log(`So sánh translation key:`);
  console.log(`  File HTML keys: ${htmlKeysFile}`);
  console.log(`  File merged translations: ${mergedFile}`);
  console.log(`  File đầu ra: ${outputFile}`);

  // Kiểm tra file tồn tại
  try {
    await stat(htmlKeysFile);
    await stat(mergedFile);
  } catch (error) {
    console.error(`Lỗi: Một trong hai file không tồn tại: ${htmlKeysFile} hoặc ${mergedFile}`);
    process.exit(1);
  }

  const success = await compareTranslationKeys(htmlKeysFile, mergedFile, outputFile);

  process.exit(success ? 0 : 1);
}

// Chạy script
main().catch(error => {
  console.error(`Lỗi không xác định: ${error.message}`);
  process.exit(1);
});