{"COMMON": {"SELECT": "Select", "WAREHOUSE": "Warehouse", "SUPPLIER": "Supplier", "REQUIRED": "is required", "ACTIONS": {"EDIT": "Edit", "DELETE": "Delete", "SAVE": "Save"}, "SAVE": "Save", "CANCEL": "Cancel", "CONFIRM": "Confirm", "EMPLOYEE": "Sales Employee", "SALE_CHANNEL": "Sales Channel", "CREATED_AT": "Order Creation Time", "ADD_TAG": "Add Tag", "NOTE": "Note", "DISCOUNT": "Discount", "PUBLIC_NOTE": "Public Note", "INTERNAL_NOTE": "Internal Note", "DISCOUNT_INFO": "Discount Information", "CUSTOMER_NEEDS_TO_PAY": "Customer Needs to Pay", "ENTER_AMOUNT": "Enter Amount", "PAYMENT": "Payment", "ERROR_LOADING_DATA": "Error loading data", "NO_ITEMS": "No data available", "CLOSE": "Close", "LOADING": "Loading...", "RETRY": "Retry", "REFRESH": "Refresh", "ADD": "Add", "EDIT": "Edit", "DELETE": "Delete"}, "ROUTER_TEXT": {"CLASSIFICATION": "Classification", "CATEGORY": "Category", "BRAND": "Brand", "ATTRIBUTES": "Attributes", "PRODUCT_BATCH": "Product Batch", "SUPPLIERS": "Suppliers", "PURCHASE_ORDERS": "Purchase Orders", "SUPPLIER_CONTRACTS": "Supplier Contracts", "DEMAND_FORECAST": "De<PERSON>", "INVENTORY": "Inventory", "TRANSFER": "Transfer", "HISTORY_XNK": "Export/Import History", "INVENTORY_CHECK": "Inventory Check", "DRAFT": "Draft", "PRODUCT_PACKAGE": "Product Package", "INVENTORY_LIMIT": "Inventory Limit", "PRODUCT_LOCATION": "Product Location", "LOCATION_LIST": "Location List", "LOCATION_INVOICE": "Location Invoice", "LOCATION_PRODUCT": "Location Product", "STORAGE_AREA_REPORT": "Storage Area Report", "IMPORT_FORECAST": "Import Forecast", "INTERNAL_TRANSPORT": "Internal Transport Management", "INVOICE_SEARCH": "Invoice Search", "INVOICE_DRAFT": "Invoice Draft", "RETAIL": "Retail", "WHOLESALE": "Wholesale", "SHIPPING": "Shipping", "RETURNS": "Returns", "DEBT_GIFT": "Gift Debt", "OLD_DEVICE_IMPORT": "Old Device Import", "PRICING_POLICY": "Pricing Policy", "PROMOTIONS": "Promotions", "DISCOUNTS": "Discounts", "POINTS": "Points", "COUPONS": "Coupons", "GIFTS": "Gifts", "DUPLICATE": "Duplicate Orders", "ECOMMERCE": "E-commerce", "PACKING": "Packing", "HANDOVER": "Handover Document", "COMPLAINTS": "<PERSON><PERSON><PERSON><PERSON>", "DELETED": "Deleted Orders", "RECONCILIATION": "Reconciliation", "COD_PAYMENT": "COD Payment", "SELF_SHIPPING": "Self-Shipping Reconciliation", "ORDER_SOURCE": "Order Source", "POTENTIAL": "Potential", "OFFER": "Offer", "CONTACTS": "Contacts", "OPPORTUNITIES": "Opportunities", "QUOTATIONS": "Quotations", "OPPORTUNITY_POOL": "Opportunity Pool", "CAMPAIGNS": "Campaigns", "CUSTOMERS": "Customers", "CUSTOMER_CARD": "Customer Card", "CUSTOMER_CARE": "Customer Care", "LEVELS": "Levels", "GROUPS": "Customer Groups", "CARE_METHODS": "Care Methods", "CARE_REASONS": "Care Reasons", "CONVERSATIONS": "Conversations", "COMMENTS": "Comments", "REVIEWS": "Reviews", "POST_MANAGEMENT": "Post Management", "CHATBOT_INTEGRATION": "Chatbot Integration", "CASH": "Cash Transactions", "BANK": "Bank Transactions", "CUSTOMER_DEBT": "Customer Debt", "EMPLOYEE": "Employee", "SUMMARY": "Transaction Summary", "DEBT_PAYMENT": "Debt & Payment", "SUPPLIER_DEBT": "Supplier Debt", "TRANSPORT_SERVICE_DEBT": "Transport Service Debt", "ENTRIES": "Entries", "COUNTERPART": "Counterpart Transactions", "INSTALLMENT": "Installment Collection", "HISTORY": "History", "ACCOUNTS": "Accounting Accounts", "INSTALLMENT_SERVICE": "Installment Service", "FINANCIAL_REPORTS": "Financial Reports", "BUDGET_MANAGEMENT": "Budget Management", "EMPLOYEES": "Employees", "ATTENDANCE": "Attendance", "SHIFTS": "Shifts", "PAYROLL": "Payroll Management", "KPI": "KPI", "RECRUITMENT": "Recruitment", "TRAINING": "Training", "DEPARTMENTS": "Department Setup", "PERMISSION_GROUPS": "Permission Group Setup", "COMPANY_INFO": "Company Information", "OVERVIEW": "Overview", "REPORTS": "Reports", "REVENUE": "Revenue", "RETAIL_REPORT": "Retail", "WHOLESALE_REPORT": "Wholesale", "INVENTORY_REPORT": "Inventory", "PRODUCTS_REPORT": "Products", "CUSTOMERS_REPORT": "Customers", "PROMOTIONS_REPORT": "Promotions", "ACCOUNTING": "Accounting", "ANALYTICS": "Data Analytics", "DASHBOARD": "Dashboard", "REVENUE_FORECAST": "Revenue Forecast", "CUSTOMER_BEHAVIOR": "Customer Behavior Analysis", "COMMISSIONS": "Sales Commissions", "AFFILIATE": "Affiliate", "CAMPAIGN_MANAGEMENT": "Campaign Management", "ROI_ANALYSIS": "ROI Analysis", "ADVERTISING": "Advertising", "GENERAL": "General Settings", "SALES_INVENTORY": "Sales & Inventory", "ORDER_SETTINGS": "Orders", "SHIPPING_SETTINGS": "Shipping", "PRINT_TEMPLATE": "Print Template", "EMAIL": "Send Email", "SMS": "Send SMS", "BRANCHES": "Branches", "AGENTS": "Agents", "EXPIRATION": "Expiration", "ECOMMERCE_SYNC": "E-commerce Sync", "MORE": "More", "PRODUCTS": "Products", "ORDERS": "Orders", "GOODS_RECEIPT": "Goods Receipt", "PRODUCT_LAYOUT": "Product Layout"}, "NAVIGATION_MODULE": {"PRODUCTS": "Products", "SUPPLY_CHAIN": "Supply Chain", "WAREHOUSE": "Warehouse", "SALES": "Sales", "ORDERS": "Orders", "CRM": "Customer Relationship", "COMMUNICATION": "Multi-Channel Communication", "FINANCE": "Finance", "HR": "Human Resources", "ORGANIZATION": "Enterprise Organization", "BUSINESS_INTELLIGENCE": "Business Intelligence", "MARKETING": "Marketing", "SETTINGS": "Settings"}, "NAVIGATION_MODULE_TOOLTIP": {"PRODUCTS": "Module Product Management", "SUPPLY_CHAIN": "Module Supply Chain Management", "WAREHOUSE": "Module Warehouse Management", "SALES": "Module Sales", "ORDERS": "Module Order Management", "CRM": "Module Customer Relationship Management", "COMMUNICATION": "Module Multi-Channel Communication", "FINANCE": "Module Finance Management", "HR": "Module Human Resource Management", "ORGANIZATION": "Module Enterprise Organization", "BUSINESS_INTELLIGENCE": "Module Business Intelligence", "MARKETING": "Module Marketing", "SETTINGS": "<PERSON><PERSON><PERSON>"}, "VALIDATION": {"REQUIRED": "This field is required", "MIN": "Minimum value is {{value}}", "COMPLETE_ORDER_INFO": "Please complete order information", "MAX_LENGTH": "Maximum {{max}} characters", "MIN_VALUE": "Minimum value: {{min}}", "MAX_VALUE": "Maximum value: {{max}}"}, "ORGANIZATION": {"ORGANIZATION_FORM": {"TITLE": "Organization Information", "BUSINESS_NAME": "Business Name", "BUSINESS_NAME_PLACEHOLDER": "Enter business name", "BUSINESS_NAME_REQUIRED": "Please enter business name", "TAX_CODE": "Tax Code", "TAX_CODE_PLACEHOLDER": "Enter tax code", "TAX_CODE_REQUIRED": "Please enter tax code", "TAX_AUTHORITY": "Tax Authority", "TAX_AUTHORITY_PLACEHOLDER": "Enter tax authority", "ESTABLISHMENT_DATE": "Establishment Date", "PHONES": {"TITLE": "Work Phones", "PHONE_NUMBER": "Phone Number", "PHONE_NUMBER_PLACEHOLDER": "Enter phone number", "PHONE_NUMBER_REQUIRED": "Please enter phone number", "PHONE_NUMBER_PATTERN": "Phone number must contain 10-11 digits", "ADD_PHONE": "Add phone number"}, "WORK_EMAIL": "Work Email", "WORK_EMAIL_PLACEHOLDER": "Enter work email", "WORK_EMAIL_INVALID": "Please enter a valid email format", "DESCRIPTION": "Description", "DESCRIPTION_PLACEHOLDER": "Enter description about the business", "OWNER": {"TITLE": "Owner Information", "NAME": "Owner Name", "NAME_PLACEHOLDER": "Enter owner name", "NAME_REQUIRED": "Please enter owner name"}, "ACTIONS": {"CANCEL": "Cancel", "SAVE": "Save Information"}}}, "PRODUCT": {"PRODUCT_FORM": {"CREATE_PRODUCT": "Create New Product", "EDIT_PRODUCT": "Edit Product", "BASIC_INFO": "Basic Information", "NAME": "Product Name", "SKU": "SKU", "DESCRIPTION": "Description", "WEIGHT": "Weight", "PRICE": "Retail Price", "COST": "Cost", "WHOLESALE_PRICE": "Wholesale Price", "CATEGORY": "Category", "BRAND": "Brand", "TAGS": "Tags", "ENTER_TAG": "Enter tag and press Enter", "ACTIVE": "Active", "INITIAL_INVENTORY": "Initial Inventory Management", "LOCATION": "Location", "QUANTITY": "Quantity", "VARIANTS": "Product Variants", "VARIANT_NAME": "Variant Name", "VARIANT_VALUES": "Variant Values", "SELECT_VALUES": "Select variant values", "ADD_VARIANT": "<PERSON><PERSON>", "CREATE_NEW_VARIANT": "Create <PERSON>t", "VARIANT_PRODUCTS": "Variant Products List", "DEFAULT_WAREHOUSE": "Default Warehouse", "DEFAULT_SUPPLIER": "<PERSON><PERSON>ult Su<PERSON>lier", "DEFAULT_LOCATION": "Default Location", "UNITS_OF_MEASURE": "Units of Measure", "BASE_UNIT": "Base Unit", "CONVERSION_UNITS": "Conversion Units", "UNIT_NAME": "Unit Name", "CONVERSION_VALUE": "Conversion Value", "RETAIL_PRICE": "Retail Price", "ADD_UNIT": "Add Unit", "INGREDIENTS": "Ingredients", "INGREDIENT": "Ingredient", "UNIT_COST": "Unit Cost", "TOTAL": "Total", "ADD_INGREDIENT": "Add Ingredient", "ADD_WAREHOUSE": "Add Warehouse", "ADD_CATEGORY": "Add Category", "ADD_BRAND": "Add Brand", "ADD_SUPPLIER": "Add Supplier", "CUSTOM_WAREHOUSE": "Custom Warehouse", "VARIANT_DETAILS": "<PERSON><PERSON><PERSON>", "ACTIONS": "Actions", "TOTAL_COSTS": "Total Costs", "TOTAL_INGREDIENTS": "Total Ingredients"}}, "PRODUCT_LAYOUT": {"TITLE": "Product Layout List", "SUBTITLE": "Manage product layout templates", "REFRESH": "Refresh", "LOADING": "Loading layout list...", "CARD_ARIA_LABEL": "Layout template {{name}}, click to edit", "TEMPLATE_SUBTITLE": "Product layout template", "SECTIONS": "Sections", "FIELDS": "Fields", "REQUIRED_FIELDS": "Required fields", "OPTIONAL_FIELDS": "Optional fields", "EDIT_TEMPLATE": "Edit template {{name}}", "NO_TEMPLATES": "No templates found", "NO_TEMPLATES_DESC": "No layout templates have been created yet.", "REQUIRED": "Required"}, "PRODUCT_LAYOUT_EDIT": {"TITLE": "Edit Product Layout", "BACK_TO_LIST": "Back to list", "LOADING": "Loading template...", "LOADING_DESC": "Please wait a moment", "ERROR_TITLE": "Template loading error", "EDITING_TEMPLATE": "Editing template", "NO_TEMPLATE": "No template", "NO_TEMPLATE_DESC": "No template found to edit."}, "SALES": {"NOTE_DIALOG": {"TITLE": "Add note", "NOTE_TYPE": "Note type", "INTERNAL_NOTE": "Internal note", "PUBLIC_NOTE": "Public note", "NOTE_CONTENT": "Note content", "ENTER_NOTE": "Enter note content..."}, "PAYMENT_METHOD": {}, "MIXED_PAYMENT": {"TITLE": "Mixed Payment", "TOTAL_AMOUNT": "Total Amount", "PAYMENT_METHOD": "Payment Method", "AMOUNT": "Amount", "ADD_PAYMENT_METHOD": "Add Payment Method", "CUSTOMER_PAID": "Customer Paid", "REMAINING_AMOUNT": "Remaining Amount", "SCAN_QR": "Scan QR code to pay", "BANK": "Bank", "ACCOUNT_NUMBER": "Account Number", "ACCOUNT_NAME": "Account Holder", "PAYMENT_STATUS": {}}, "PRODUCT_MODIFIERS": {"CHOOSE_MORE": "Choose more", "RESET": "Reset", "ADD_TO_ORDER": "Add to order"}, "PRODUCT_SELECTOR": {}, "PROMOTION": {"TITLE": "Promotion", "TOTAL_AMOUNT": "Total Amount", "BY_AMOUNT": "By Amount", "BY_PERCENT": "By Percentage", "BY_COUPON": "By Coupon", "ENTER_AMOUNT": "Enter discount amount", "ENTER_PERCENT": "Enter discount percentage", "ENTER_COUPON": "Enter coupon code", "NAME": "Promotion description", "ENTER_NAME": "Enter promotion description", "FINAL_AMOUNT": "Final Amount"}, "ORDER_FORM": {"ADD_TAB": "Add New Order", "CLOSE_TAB": "Close Order", "ORDER_DETAILS": {"TITLE": "Order Details", "ITEMS": "items", "SEARCH_PLACEHOLDER": "Search products by name or SKU", "PRODUCT_NAME": "Product Name", "QUANTITY": "Quantity", "PRICE": "Price", "TOTAL": "Total", "ACTIONS": "Actions", "REMOVE": "Remove product", "NO_PRODUCTS": "No products in order yet", "SELECT_PRODUCTS": "Select Products", "ADD_MODIFIERS": "Add options", "NOTE": "Note", "MORE_OPTIONS": "More options", "ADD_PROMOTION": "Add promotion", "ADD_NOTE": "Add note"}, "SHIPPING_INFO": {"TITLE": "Shipping Information", "RECEIVER_INFO": "Receiver Information", "COPY_FROM_CUSTOMER": "Copy from customer", "RECEIVER_NAME": "Receiver name", "RECEIVER_PHONE": "Receiver phone", "DELIVERY_ADDRESS": "Delivery address", "PACKAGE_INFO": "Package Information", "DISTANCE": "Distance", "WEIGHT": "Weight", "LENGTH": "Length", "WIDTH": "<PERSON><PERSON><PERSON>", "HEIGHT": "Height", "PICKUP_ADDRESS": "Pickup address", "SELF_TRANSPORT": "Self transport", "DELIVERY_DATE": "Delivery date", "DELIVERY_METHOD_LABEL": "Delivery method", "DELIVERY_METHOD": {"DIRECT": "Direct delivery", "PICKUP": "Pickup by courier", "DROP_OFF": "Drop off at post office"}, "SHIPPING_CARRIER": "Shipping carrier", "TRY_ON": {"LABEL": "Try on option", "VIEW_ONLY": "Allow view only, no try", "ALLOW_TRY": "Allow try", "NO_VIEW": "No view", "VIEW_NO_CHARGE": "Allow view, no shipping charge"}, "PICKUP_TIME": {"LABEL": "Pickup time", "ALL_DAY": "All day", "MORNING": "8h-12h", "AFTERNOON": "13h-17h"}, "SHIPPING_FEE": "Shipping fee", "RETURN_FEE": "Return fee", "SHIPPING_PAYER_LABEL": "Shipping payer", "SHIPPING_PAYER": {"CUSTOMER": "Customer pays", "SHOP": "Shop pays"}, "AUTO_SEND_TO_CARRIER": "Auto send to carrier", "NOTE": "Shipping note", "DELIVERY_TYPE": {"PHYSICAL": "Physical delivery", "DIGITAL": "Digital delivery", "SERVICE": "Service", "SELF_PICKUP": "Self pickup"}, "DIGITAL": {"DOWNLOAD_LINK": "Download link", "ACCESS_CODE": "Access code", "EXPIRATION_DATE": "Expiration date"}, "SERVICE": {"APPOINTMENT_TIME": "Appointment time", "LOCATION": "Service location"}, "SELF_PICKUP": {"INFO": "Customer will pick up the order at your store."}}, "ORDER_SUMMARY": {"TOTAL_ITEMS_AMOUNT": "Total Items Amount", "DISCOUNT_AMOUNT": "Discount", "DELIVERY_FEE": "Delivery Fee", "SURCHARGE_FEE": "<PERSON><PERSON><PERSON>", "FINAL_AMOUNT": "Final Amount", "PAID_AMOUNT": "<PERSON><PERSON>", "ENTER_TAGS": "Enter tags...", "TAGS": "Tags", "CHANGE": "Change", "DEBT": "Debt", "CURRENT_DEBT": "Current Debt", "DELIVERY_TIME": "Delivery Time", "SCHEDULED_DELIVERY_TIME": "Scheduled Delivery Time", "STATUS": "Status", "PAYMENT_METHOD": "Payment Method", "BANK_ACCOUNT": "Bank Account"}, "ORDER": {"CUSTOMER_INFO": "Customer Information", "CUSTOMER_PHONE": "Phone Number", "CUSTOMER_NAME": "Customer Name", "CUSTOMER_ADDRESS": "Address", "CUSTOMER_NOTE": "Note", "CUSTOMER_SCORE": "Score", "CUSTOMER_SUCCESS_RATE": "Success Rate", "CUSTOMER_RETURN_RATE": "Return Rate", "CUSTOMER_OTHER_RATE": "Other Rate", "CUSTOMER_ORDER_COUNT": "Orders", "CUSTOMER_TOTAL_VALUE": "Total Value"}, "PAYMENT_METHOD": {"CASH": "Cash", "BANK_TRANSFER": "Bank Transfer", "CREDIT_CARD": "Credit Card", "EWALLET": "E-Wallet", "MIXED": "Mixed Payment", "MIXED_DETAILS": "Mixed Payment Details"}, "DELIVERY_TIME": {"ASAP": "As Soon As Possible", "SCHEDULED": "Scheduled Delivery"}}}, "DYNAMIC_LAYOUT_BUILDER": {"TITLE": "Dynamic Layout Builder", "SUBTITLE": "Create and manage layouts", "DROP_HERE": "Drop here", "TEXT": "Text", "NUMBER": "Number", "EMAIL": "Email", "PHONE": "Phone", "TEXTAREA": "Text Area", "DATE": "Date", "DATETIME": "DateTime", "FILE": "File", "IMAGE": "Image", "CHECKBOX": "Checkbox", "RADIO": "Radio", "SELECT": "Select", "SIZE": "Size", "COLOR": "Color", "BRAND": "Brand", "CATEGORY": "Category", "BASIC_FIELDS": "Basic Fields", "ADVANCED_FIELDS": "Advanced Fields", "INDUSTRY_FIELDS": "Industry Fields", "NEW_FIELDS": {"TITLE": "New Fields", "NEW_SECTION": "New Section", "FIELD_TYPES": {}}, "SECTION": {"DELETE": "Delete", "ADD_FIELD": "Add Field", "TITLE_PLACEHOLDER": "Enter section title", "DROP_FIELDS_HERE": "Drag and drop field types here to add fields", "COLLAPSE": "Collapse", "EXPAND": "Expand", "DELETE_CONFIRM_TITLE": "Confirm Delete Section", "DELETE_CONFIRM_MESSAGE": "Are you sure you want to delete this section? All fields in this section will be deleted. This action cannot be undone."}, "FIELD": {"MARK_REQUIRED": "<PERSON> as Required", "EDIT_PROPERTIES": "Edit Properties", "SET_PERMISSION": "Set Permission", "DELETE_FIELD": "Delete Field", "LABEL_PLACEHOLDER": "Enter field label", "ACTIONS": "Actions", "FIELDS": "fields", "DRAG_TO_REORDER": "Drag to reorder", "CLICK_TO_EDIT": "Click to edit"}, "FIELD_TYPE_SELECTOR": {"TITLE": "Select Field Type", "DESCRIPTION": "Drag and drop or click to add field to section", "BASIC_FIELDS": "Basic Fields", "ADVANCED_FIELDS": "Advanced Fields", "INDUSTRY_FIELDS": "Industry Fields"}, "TEMPLATE": {}, "PREVIEW": {"TITLE": "Preview"}, "ACTIONS": {"SAVE": "Save Layout", "RESTORE": "Restore Layout"}, "MESSAGES": {}, "CONFIRM_DELETE_FIELD_TITLE": "Confirm Delete Field", "CONFIRM_DELETE_FIELD_MESSAGE": "Are you sure you want to delete this field? This action cannot be undone.", "NEW_SECTION": {"DESCRIPTION": "Create a new section to group related fields"}, "TEMPLATE_SELECTOR": {"TITLE": "Select Industry Template", "DESCRIPTION": "Choose from available templates to quickly create layouts suitable for your industry", "SELECT_TEMPLATE": "Select Template", "NO_TEMPLATE": "No template", "APPLY_TEMPLATE": "Apply Template", "SECTIONS_INCLUDED": "Sections Included", "FIELDS": "fields", "PREVIEW": "Preview", "AVAILABLE_TEMPLATES": "Available Templates", "USE_TEMPLATE": "Use Template"}, "PREVIEW_PANEL": {"TITLE": "Form Preview", "DESCRIPTION": "Preview the form interface and test data entry based on the created layout", "FILL_SAMPLE": "Fill Sample Data", "CLEAR_DATA": "Clear Data", "VALIDATE": "Validate", "NO_FIELDS": "No Fields Yet", "NO_FIELDS_DESCRIPTION": "Add sections and fields to see preview", "FORM_VIEW": "Form View", "DATA_VIEW": "JSON Data", "FORM_DATA": "Form Data", "VALIDATION_RESULTS": "Validation Results", "FIELDS": "fields"}, "LOADING": "Loading...", "EMPTY_STATE": {"NO_SECTIONS": "No sections yet", "NO_SECTIONS_DESCRIPTION": "Drag and drop \"New Section\" or click the button below to create your first section"}, "FIELD_TYPE_DESCRIPTIONS": {"DEFAULT": "Field description"}, "ARIA_LABELS": {"ADD_FIELD": "Add", "SECTIONS_COUNT": "sections"}}, "ADDRESS": {"TITLE": "Address", "CITY": "City", "COUNTRY": "Country", "FULL_ADDRESS": "Full Address", "FULL_ADDRESS_REQUIRED": "Please enter full address", "INSTRUCTION": "Address Instruction", "POSTAL_CODE": "Postal Code", "STREET": "Street", "PROVINCE": "Province", "PROVINCE_REQUIRED": "Please select Province/City", "DISTRICT": "District", "DISTRICT_REQUIRED": "Please select District", "WARD": "Ward", "WARD_REQUIRED": "Please select <PERSON>", "EDIT_DETAILS": "Edit place details", "STREET_REQUIRED": "Please enter street name"}, "INPUT_PLACE": {"USE_MANUAL": "Switch to manual address entry", "USE_GOOGLE_MAPS": "Switch to searching on Google Maps"}, "PRODUCT_SELECTION": {"PRODUCT_SEARCH_PLACEHOLDER": "Search products by name, code, barcode...", "WAREHOUSE_FILTER": "Filter by warehouse", "ALL_WAREHOUSES": "All warehouses", "SEARCH_WAREHOUSE": "Search warehouses", "NO_MATCHING_WAREHOUSE": "No matching warehouses found", "CATEGORY_FILTER": "Filter by category", "ALL_CATEGORIES": "All categories", "SEARCH_CATEGORY": "Search categories", "NO_MATCHING_CATEGORY": "No matching categories found", "BRAND_FILTER": "Filter by brand", "ALL_BRANDS": "All brands", "SEARCH_BRAND": "Search brands", "NO_MATCHING_BRAND": "No matching brands found", "RESET_FILTERS": "Reset filters", "ADD_NEW_PRODUCT": "Add new product", "OUT_OF_STOCK": "Out of stock", "UNIT": "Unit", "INVENTORY": "Inventory", "ADD_OPTIONS": "Add options", "NO_PRODUCTS_FOUND": "No products found", "RESET_SELECTION": "Reset selection", "ADD_TO_ORDER_WITH_TOTAL": "Add to Order ({{total}})", "WITH_OTHER_PRODUCTS": "{{name}} and {{count}} other items"}, "TAX": {"ADD_TITLE": "Add Tax", "EDIT_TITLE": "Edit Tax", "TYPE": "Tax Type", "VAT": "VAT", "IMPORT_TAX": "Import Tax", "OTHER": "Other", "RATE": "Tax Rate", "AMOUNT": "Tax Amount", "RATE_TOOLTIP": "Enter tax rate to automatically calculate tax amount", "SUB_TOTAL_INFO": "Subtotal: {{value}} VND", "AUTO_TAX_INFO": "Automatic tax amount: {{value}} VND"}, "SIMPLE_NOTE_DIALOG": {"TITLE": "Note", "NOTE_CONTENT": "Note Content", "ENTER_NOTE": "Enter note content..."}, "VARIANT_SELECTOR": {"TITLE": "Select Variant", "VARIANT": "<PERSON><PERSON><PERSON>", "UNIT": "Unit", "SELECT_VARIANT": "Select variant attributes", "SELECT_UNIT": "Select unit", "NO_VARIANTS": "No variants available", "NO_UNITS": "No units available"}, "RESIZE_PANEL": {"TOOLTIP": "<PERSON>ag to resize the panel"}, "FIELD_FILTERS": {"TITLE": "Field Filters", "NO_FIELDS": "No fields available for filtering", "CLEAR_ALL": "Clear All", "APPLY_FILTERS": "Apply Filters", "OPERATORS": {"IS": "is", "ISNT": "isn't", "CONTAINS": "contains", "DOESNT_CONTAIN": "doesn't contain", "STARTS_WITH": "starts with", "ENDS_WITH": "ends with", "IS_EMPTY": "is empty", "IS_NOT_EMPTY": "is not empty", "EQUALS": "equals", "NOT_EQUALS": "not equals", "LESS_THAN": "less than", "LESS_THAN_OR_EQUAL": "less than or equal", "GREATER_THAN": "greater than", "GREATER_THAN_OR_EQUAL": "greater than or equal", "BETWEEN": "between", "NOT_BETWEEN": "not between", "AGE_IN": "age in", "DUE_IN": "due in", "PREVIOUS": "previous", "NEXT": "next", "ON": "on", "BEFORE": "before", "AFTER": "after", "TODAY": "today", "TOMORROW": "tomorrow", "TILL_YESTERDAY": "till yesterday", "STARTING_TOMORROW": "starting tomorrow", "YESTERDAY": "yesterday", "THIS_WEEK": "this week", "THIS_MONTH": "this month", "PREVIOUS_WEEK": "previous week", "PREVIOUS_MONTH": "previous month", "THIS_YEAR": "this year", "CURRENT_FY": "current fiscal year", "CURRENT_FQ": "current fiscal quarter", "PREVIOUS_YEAR": "previous year", "PREVIOUS_FY": "previous fiscal year", "PREVIOUS_FQ": "previous fiscal quarter", "NEXT_YEAR": "next year", "NEXT_FQ": "next fiscal quarter", "IS_NOT": "is not"}, "TIME_UNITS": {"DAYS": "days", "WEEKS": "weeks", "MONTHS": "months", "YEARS": "years"}, "CHECKBOX_VALUES": {"SELECTED": "selected", "NOT_SELECTED": "not selected"}, "PLACEHOLDERS": {"ENTER_VALUE": "Enter value", "ENTER_MIN_VALUE": "Enter minimum value", "ENTER_MAX_VALUE": "Enter maximum value", "SELECT_DATE": "Select date", "SELECT_START_DATE": "Select start date", "SELECT_END_DATE": "Select end date", "ENTER_NUMBER": "Enter number", "SELECT_VALUES": "Select values", "SELECT_OPERATOR": "Select operator", "SELECT_TIME_UNIT": "Select time unit", "SELECT_CHECKBOX_VALUE": "Select value"}, "LABELS": {"AND": "and", "FROM": "from", "TO": "to", "VALUE": "Value", "MIN_VALUE": "Min Value", "MAX_VALUE": "Max Value", "TIME_VALUE": "Time Value", "TIME_UNIT": "Time Unit", "OPERATOR": "Operator"}, "VALIDATION": {"REQUIRED": "This field is required", "INVALID_NUMBER": "Please enter a valid number", "INVALID_DATE": "Please enter a valid date", "MIN_GREATER_THAN_MAX": "Minimum value cannot be greater than maximum value"}}, "FIELD_PERMISSION_MODAL": {"TITLE": "Set Permission - {{fieldName}}", "PROFILES": "Profiles", "READ_WRITE": "Read and Write", "READ_ONLY": "Read Only", "DONT_SHOW": "Don't Show", "ALL": "All", "SET_ALL_READ_WRITE": "Set all profiles to read and write permission", "SET_ALL_READ_ONLY": "Set all profiles to read only permission", "SET_ALL_DONT_SHOW": "Set all profiles to don't show permission", "READ_WRITE_FOR": "Read and write permission for {{profileName}}", "READ_ONLY_FOR": "Read only permission for {{profileName}}", "DONT_SHOW_FOR": "Don't show permission for {{profileName}}", "NO_PROFILES": "No profiles available to set permissions"}, "EDIT_FIELD_PROPERTIES": {"TITLE": "Edit Properties: {{fieldType}}", "LABEL": "Label", "LABEL_REQUIRED": "Label is required", "LABEL_MAX_LENGTH": "Label cannot exceed 255 characters", "IS_PUBLIC": "Mark as Public Field", "IS_REQUIRED": "<PERSON> as Required Field", "UNIQUE": "Do not allow duplicate values", "SHOW_TOOLTIP": "Show Tooltip", "TOOLTIP": "Tooltip Content", "TOOLTIP_MAX_LENGTH": "Tooltip cannot exceed 500 characters", "MAX_LENGTH": "Maximum Length", "MAX_LENGTH_VALIDATION": "Maximum length must be between 1 and 255", "MAX_DIGITS": "Maximum Digits", "MAX_DIGITS_VALIDATION": "Maximum digits must be between 1 and 16", "TEXT_TYPE": "Type", "TEXT_TYPE_SMALL": "Plain Text, Small", "TEXT_TYPE_LARGE": "Plain Text, Large", "TEXT_TYPE_RICH": "Rich Text", "MAX_LENGTH_2000": "Character Limit: 2,000", "MAX_LENGTH_32000": "Character Limit: 32,000", "MAX_LENGTH_50000": "Character Limit: 50,000", "REMAINING_FIELDS": "Remaining Fields: {{count}}/{{total}}", "REMAINING_RICH_FIELDS": "Remaining Rich Text Fields: {{count}}/{{total}}", "PICKLIST_OPTIONS": "List Options", "ADD_OPTION": "Add Option", "ADD_BULK": "Add Bulk Options", "BULK_PLACEHOLDER": "Enter each option on a new line", "BULK_SAVE": "Save Options", "BULK_CANCEL": "Cancel", "OPTION_REQUIRED": "Option cannot be empty", "OPTION_MAX_LENGTH": "Option cannot exceed 255 characters", "OPTION_DUPLICATE": "Duplicate option", "MAX_OPTIONS_REACHED": "Maximum 100 options allowed", "DEFAULT_VALUE": "Default Value", "SORT_ORDER": "Sort Order", "SORT_ORDER_INPUT": "By Input Order", "SORT_ORDER_ALPHABETICAL": "Alphabetical Order", "SEARCH_MODULE": "Search Module", "SEARCH_MODULE_SALES_QUOTES": "Sales Quotes", "SEARCH_MODULE_CONTACTS": "Contacts", "SEARCH_MODULE_TRANSACTIONS": "Transactions", "USER_TYPE": "Type", "USER_TYPE_SINGLE": "Single User", "USER_TYPE_MULTIPLE": "Multiple Users", "ALLOW_MULTIPLE_FILES": "Allow Multiple Files", "MAX_FILES": "Maximum Files", "MAX_FILES_VALIDATION": "Maximum files must be between 1 and 5", "MAX_IMAGES": "Maximum Images", "DECIMAL_PLACES": "Number of Decimal Places", "DECIMAL_PLACES_VALIDATION_CURRENCY": "Decimal places must be between 0 and 4", "DECIMAL_PLACES_VALIDATION_DECIMAL": "Decimal places must be between 0 and 8", "ROUNDING": "Rounding Option", "ROUNDING_NORMAL": "Normal", "ROUNDING_OFF": "Rounding Off", "ROUNDING_UP": "Rounding Up", "ROUNDING_DOWN": "Rounding Down", "USE_NUMBER_SEPARATOR": "Display with Number Separator", "ENABLE_BY_DEFAULT": "Enable by <PERSON><PERSON><PERSON>", "FIELD_TYPE_TEXT": "Single Line", "FIELD_TYPE_EMAIL": "Email", "FIELD_TYPE_PHONE": "Phone", "FIELD_TYPE_URL": "URL", "FIELD_TYPE_TEXTAREA": "Multi Line", "FIELD_TYPE_NUMBER": "Number", "FIELD_TYPE_DECIMAL": "Decimal", "FIELD_TYPE_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "FIELD_TYPE_PERCENT": "Percent", "FIELD_TYPE_DATE": "Date", "FIELD_TYPE_DATETIME": "Date Time", "FIELD_TYPE_PICKLIST": "Pick List", "FIELD_TYPE_MULTI_PICKLIST": "Multi Pick List", "FIELD_TYPE_SEARCH": "Search", "FIELD_TYPE_USER": "User", "FIELD_TYPE_UPLOAD_FILE": "Upload File", "FIELD_TYPE_UPLOAD_IMAGE": "Upload Image", "FIELD_TYPE_CHECKBOX": "Checkbox", "FIELD_SPECIFIC_OPTIONS": "Field Specific Options", "OPTION_PLACEHOLDER": "Enter option"}, "WAREHOUSE": {"SELECT_WAREHOUSE": "Select Warehouse", "SELECT_LOCATION": "Select Location", "BATCH_DIALOG": {"TITLE": "Batch Information", "BATCH_NUMBER": "Batch Number", "BATCH_NUMBER_PLACEHOLDER": "Enter batch number", "BATCH_NUMBER_REQUIRED": "Please enter batch number", "MANUFACTURING_DATE": "Manufacturing Date", "EXPIRY_DATE": "Expiry Date", "EXPIRY_DATE_REQUIRED": "Please enter expiry date", "QUANTITY": "Quantity", "QUANTITY_PLACEHOLDER": "Enter quantity", "QUANTITY_REQUIRED": "Please enter quantity", "QUANTITY_MIN": "Quantity must be greater than 0", "DATE_FORMAT": "MM/DD/YYYY"}, "CATEGORY_PRODUCT_MODAL": {"TITLE": "Add Products from Categories", "CANCEL": "Cancel", "ADD": "Add", "SELECT_CATEGORIES": "Select Categories"}, "QUALITY_CHECK_REJECT_DIALOG": {"ADD_TITLE": "Add Rejected Item", "EDIT_TITLE": "Edit Rejected Item", "PRODUCT": "Product", "PRODUCT_REQUIRED": "Please select a product", "QUANTITY": "Quantity", "QUANTITY_PLACEHOLDER": "Enter rejected quantity", "QUANTITY_REQUIRED": "Please enter quantity", "QUANTITY_MIN": "Quantity must be greater than 0", "QUANTITY_MAX": "Quantity cannot exceed {{max}}", "REASON": "Reason", "REASON_PLACEHOLDER": "Enter rejection reason (e.g. Damaged goods)", "REASON_REQUIRED": "Please enter a reason", "REASON_MAX_LENGTH": "Reason cannot exceed 500 characters", "RECEIVED_QUANTITY": "Received quantity"}, "ADDITIONAL_COST": {"ADD_TITLE": "Add Additional Cost", "EDIT_TITLE": "Edit Additional Cost", "NAME": "Cost Name", "NAME_PLACEHOLDER": "Enter cost name (e.g. Shipping Fee)", "COST_TYPE": "Cost Type", "FIXED": "Fixed", "PERCENTAGE": "Percentage", "VALUE": "Value", "PAID_TO_SUPPLIER": "Paid to Supplier", "ALLOCATE_TO_ITEMS": "Allocate to Items Cost", "TAX_INFO": "Tax Information", "TAX_RATE": "Tax Rate", "TAX_AMOUNT": "Tax Amount", "TAX_RATE_TOOLTIP": "Enter rate to automatically calculate tax amount based on cost value", "SUB_TOTAL_INFO": "Subtotal: {{value}} VND", "AUTO_TAX_INFO": "Auto tax amount: {{value}} VND"}, "SELECT_ADDITIONAL_COSTS": {"TITLE": "Select Additional Costs", "CREATE_NEW": "Create New Cost", "SELECT": "Select", "NAME": "Name", "TYPE": "Type", "DEFAULT_VALUE": "Default Value", "CUSTOM_VALUE": "Custom Value", "PAID_TO_SUPPLIER": "Paid to Supplier", "ALLOCATE": "Allocate", "TAX": "Tax", "SUB_TOTAL_INFO": "Subtotal: {{value}} VND", "TAX_UPDATE_NOTICE": "Tax amount for fixed costs will update automatically when changing the custom value."}, "SERIAL_NUMBER_DIALOG": {"TITLE": "Manage Serial Numbers", "SERIAL_NUMBER": "Serial Number", "STATUS_LABEL": "Status", "TOTAL_SERIALS": "Total Serials", "ACTUAL_QUANTITY": "Actual Quantity", "STATUS": {"IN_STOCK": "In Stock", "ASSIGNED": "Assigned", "SOLD": "Sold", "MISSING": "Missing", "DAMAGED": "Damaged", "RETURNED": "Returned", "IN_TRANSIT": "In Transit"}}, "PRODUCT_FILTER_DIALOG": {"TITLE": "Filter Products", "CATEGORIES": "Categories", "WAREHOUSE_LOCATION": "Warehouse Location", "SELECT_LOCATION": "Select Location", "ALL_LOCATIONS": "All Locations", "OPTIONS": "Options", "ONLY_IN_STOCK": "Only products in stock", "ONLY_ACTIVE": "Only active products"}, "LOCATION_PICKER_MODAL": {"SELECT_LOCATION": "Select location in warehouse", "LOCATION_DIALOG_DESCRIPTION": "Select a location in the warehouse"}, "GOODS_RECEIPT": {"TITLE": "Goods Receipt", "QUALITY_CHECK": {"TITLE": "Quality Check", "CHECKED_BY": "Checked By", "CHECKED_AT": "Check Date", "NOTES": "Quality Check Notes", "STATUS": {"LABEL": "Status", "PENDING": "Pending", "PASSED": "Passed", "FAILED": "Failed", "PARTIAL": "Partially Passed"}, "ERRORS": {"STATUS_REQUIRED": "Please select a status", "CHECKED_BY_REQUIRED": "Please select who checked", "CHECKED_AT_REQUIRED": "Please select check date"}, "REJECTED_ITEMS": {"TITLE": "Rejected Items", "ADD_BUTTON": "Add Rejection", "NO_ITEMS": "No rejected items yet", "COLUMNS": {"PRODUCT": "Product", "QUANTITY": "Quantity", "REASON": "Reason", "ACTIONS": "Actions"}}}, "TRANSPORT_INFO": {"TITLE": "Transport Information", "FILLED": "Filled", "TRANSPORT_METHOD": "Transport Method", "CARRIER": "Carrier", "CARRIER_PLACEHOLDER": "Enter carrier name", "TRACKING_NUMBER": "Tracking Number", "TRACKING_NUMBER_PLACEHOLDER": "Enter tracking number", "ESTIMATED_DELIVERY_DATE": "Estimated Delivery Date", "ACTUAL_DELIVERY_DATE": "Actual Delivery Date", "METHODS": {"ROAD": "Road", "SEA": "Sea", "AIR": "Air", "OTHER": "Other"}}, "PRODUCT_LIST": {"SEARCH_PRODUCT": "Search Product", "ADD_FROM_CATEGORY": "Add from Category", "PRINT": "Print Receipt", "SELECT_LOCATION": "Select Location", "SELECT_VARIANT": "Select Variant", "CHANGE_VARIANT": "Change Variant", "MANAGE_BATCHES": "Manage Batches", "BATCH_LIST": "Batch List", "ADD_BATCH": "<PERSON><PERSON>", "NO_BATCHES": "No batches available", "COLUMNS": {"PRODUCT": "Product", "UNIT": "Unit", "ORDERED_QUANTITY": "Ordered Qty", "RECEIVED_QUANTITY": "Received Qty", "ACCEPTED_QUANTITY": "Accepted <PERSON>ty", "PRICE": "Price", "DISCOUNT": "Discount", "TOTAL": "Total", "LOCATION": "Storage Location"}}, "RECEIPT_INFO": {"EMPLOYEE": "Employee", "RECEIPT_DATE": "Receipt Date", "WAREHOUSE": "Warehouse", "SUPPLIER": "Supplier", "NOTES": "Notes", "SELECT_EMPLOYEE": "Select Employee", "SELECT_WAREHOUSE": "Select Warehouse", "SEARCH_SUPPLIER": "Search Supplier", "NOTES_PLACEHOLDER": "Enter notes for this receipt"}, "FINANCIAL": {"TITLE": "Financial Information", "RECEIPT_NUMBER": "Receipt Number", "RECEIPT_NUMBER_PLACEHOLDER": "Auto-generated when completed", "SUB_TOTAL": "Subtotal", "TOTAL_DISCOUNT": "Total Discount", "SUPPLIER_PAYMENT": "Supplier Payment", "PAYMENT_METHODS": {}}, "ACTIONS": {"SAVE_DRAFT": "Save Draft", "COMPLETE": "Complete", "SAVE_DRAFT_TOOLTIP": "Save goods receipt as draft", "COMPLETE_TOOLTIP": "Complete goods receipt"}, "SUPPLIER_COSTS": {"TITLE": "Supplier Additional Costs", "NO_COSTS": "No costs available", "TAX_INFO": "Tax: {{rate}}% ({{amount}} VND)"}, "OTHER_COSTS": {"TITLE": "Other Import Costs", "NO_COSTS": "No costs available", "TAX_INFO": "Tax: {{rate}}% ({{amount}} VND)"}, "TAXES": {"TITLE": "Taxes", "ADD_TAX": "Add Tax", "EDIT_TAX": "Edit Tax", "DELETE_TAX": "Delete Tax", "NO_TAXES": "No taxes available", "DELETE_CONFIRM_TITLE": "Delete Tax", "DELETE_CONFIRM_MESSAGE": "Are you sure you want to delete this tax?", "TYPES": {"VAT": "VAT", "IMPORT_TAX": "Import Tax", "OTHER": "Other Tax"}}, "PAYMENT": {"AMOUNT_PAID": "Amount <PERSON>", "PAYMENT_METHOD": "Payment Method", "PAYMENT_METHODS": {"CASH": "Cash", "BANK_TRANSFER": "Bank Transfer"}, "SELECT_BANK": "Select Bank Account", "DEBT": "Debt", "DUE_DATE": "Due Date"}}, "LOCATION": {"LIST": "Location List", "CREATE": "Create Location", "EDIT": "Edit Location", "ADD": "Add Location", "DELETE": "Delete Location", "DETAILS_LABEL": "Location Details", "FORM": {"NAME": "Name", "CODE": "Code", "AUTO_GENERATE": "Auto Generate", "TYPE": "Location Type", "PARENT": "Parent Location", "CAPACITY": "Capacity", "DIMENSIONS": "Dimensions", "LENGTH": "Length", "WIDTH": "<PERSON><PERSON><PERSON>", "HEIGHT": "Height", "DEPTH": "De<PERSON><PERSON>", "STATUS": "Status", "QUANTITY": "Quantity", "CREATE_CHILD": "Create Child", "CHILD_LEVEL": "Child Level", "LEVEL": "Level", "PREVIEW": "Preview", "EMPTY_PREVIEW": "Empty Preview"}}}, "INVENTORY_CHECK": {"SELECT_WAREHOUSE": "Select Warehouse", "TAB_ALL": "All", "TAB_MATCHED": "Matched", "TAB_DIFFERENT": "Different", "TAB_UNCHECKED": "Unchecked", "EMPLOYEE": "Employee", "DATE": "Date", "NOTE": "Note", "SUMMARY": "Summary", "TOTAL_INCREASE": "Total Increase", "TOTAL_DECREASE": "Total Decrease", "TOTAL_DIFFERENCE": "Total Difference", "CHECKED_PRODUCTS": "Checked", "UNCHECKED_PRODUCTS": "Unchecked", "TOTAL_PRODUCTS": "Total Products", "SAVE_DRAFT": "Save Draft", "COMPLETE": "Complete", "NO_PRODUCTS": "No products yet. Please select a warehouse and add products to check inventory.", "WAREHOUSE_REQUIRED": "Please select a warehouse", "EMPLOYEE_REQUIRED": "Please select an employee", "SAVE_DRAFT_SUCCESS": "Inventory check saved as draft", "SAVE_DRAFT_ERROR": "Error saving inventory check", "COMPLETE_SUCCESS": "Inventory check completed", "COMPLETE_ERROR": "Error completing inventory check", "CONFIRM_COMPLETE_TITLE": "Confirm Completion", "CONFIRM_COMPLETE_MESSAGE": "There are {{count}} unchecked products. Are you sure you want to complete the inventory check?", "CONFIRM_STOCK_UPDATE_TITLE": "Confirm Stock Update", "CONFIRM_STOCK_UPDATE_MESSAGE": "There are {{count}} products with differences. Are you sure you want to update the stock?", "STOCK_ADJUSTMENT_CREATED": "Stock adjustment created", "PRODUCTS_ADDED": "{{count}} products added to inventory check", "PRODUCT": {"NAME": "Product Name", "UNIT": "Unit", "STOCK": "Stock", "ACTUAL": "Actual", "DIFFERENCE": "Difference", "DIFFERENCE_VALUE": "Difference Value", "ADD_BATCH": "<PERSON><PERSON>", "EXPIRY_DATE": "Expiry Date", "VARIANT": "<PERSON><PERSON><PERSON>", "ACTIONS": "Actions", "REMOVE": "Remove Product", "REMOVE_SUCCESS": "Product removed successfully", "ADD_NOTE": "Add Note", "MANAGE_SERIALS": "Manage Serials", "CHANGE_VARIANT": "Change Variant"}, "NO_MATCHED_PRODUCTS": "No matched products", "NO_DIFFERENT_PRODUCTS": "No different products", "NO_UNCHECKED_PRODUCTS": "No unchecked products", "VALIDATION": {"ACTUAL_QUANTITY_REQUIRED": "Actual quantity is required", "ACTUAL_QUANTITY_POSITIVE": "Actual quantity must be a positive number", "ACTUAL_QUANTITY_MAX": "Actual quantity is too large", "BATCH_QUANTITY_REQUIRED": "Batch quantity is required", "BATCH_QUANTITY_POSITIVE": "Batch quantity must be a positive number", "BATCH_QUANTITY_MAX": "Batch quantity is too large", "NOTE_MAX_LENGTH": "Note is too long (maximum 500 characters)"}, "CONFIRM": {"REMOVE_PRODUCT_TITLE": "Remove Product", "REMOVE_PRODUCT_MESSAGE": "Are you sure you want to remove this product from the inventory check?"}}}